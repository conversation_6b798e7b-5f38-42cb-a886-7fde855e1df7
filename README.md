# WordPress Docker Setup with Custom InstaFeed Theme

This project contains a complete WordPress installation using Docker Compose with a custom Instagram-style theme.

## Services Included

- **WordPress**: Latest version running on port 8082
- **MySQL 8.0**: Database server
- **phpMyAdmin**: Database management interface on port 8083

## Custom InstaFeed Theme + Ramom Plugin

A complete social media-style WordPress solution featuring:

### InstaFeed Theme
- Responsive grid layout showcasing images
- Lightbox functionality for image viewing
- Mobile-optimized design
- Infinite scroll loading
- Clean, minimal aesthetic
- Sample content generator

### Ramom Plugin
- Random post generation with auto-posting
- User interaction system (likes, follows)
- User-generated content creation
- Social media sharing
- Real-time activity tracking
- Comprehensive admin dashboard
- Advanced analytics and statistics

## Quick Start

1. Make sure Docker and Docker Compose are installed on your system
2. Clone or navigate to this directory
3. Run the following command to start all services:

```bash
docker-compose up -d
```

## Access Points

- **WordPress Site**: http://localhost:8082
- **phpMyAdmin**: http://localhost:8083

## Default Credentials

### Database (for phpMyAdmin)
- **Server**: db
- **Username**: root
- **Password**: root_password_123

### WordPress Database
- **Database Name**: wordpress_db
- **Username**: wordpress_user
- **Password**: wordpress_password_123

## Useful Commands

### Start services
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### View logs
```bash
docker-compose logs -f
```

### Restart services
```bash
docker-compose restart
```

### Remove everything (including volumes)
```bash
docker-compose down -v
```

## File Structure

- `docker-compose.yml`: Main Docker Compose configuration
- `.env`: Environment variables (database credentials)
- `wp-content/`: WordPress content directory (themes, plugins, uploads)
- WordPress data and database are stored in Docker volumes

## InstaFeed Theme Setup

### Quick Start
1. Access WordPress at http://localhost:8082
2. Complete the WordPress installation
3. Go to Appearance → Themes and activate "InstaFeed Theme"
4. Go to Plugins and activate "Ramom - Random Post Generator & User Interaction"
5. Go to Tools → InstaFeed Setup and click "Create Sample Content"
6. Go to Ramom → Post Generator to create random posts
7. View your social media-style WordPress site!

### Complete Feature Set
- **Grid Layout**: Responsive Instagram-style grid
- **Lightbox**: Click images for full-screen viewing
- **Mobile Responsive**: Optimized for all devices
- **Infinite Scroll**: Automatic loading of more posts
- **User Interactions**: Like, follow, and share posts
- **User Content**: Allow users to create their own posts
- **Auto-Posting**: Automatic random post generation
- **Social Features**: Complete social media functionality
- **Analytics**: Track user engagement and activity
- **Sample Content**: 50+ demo posts with placeholder images

## Security Notes

- Change the default passwords in `.env` file before deploying to production
- Consider using Docker secrets for production deployments
- The current setup is optimized for development purposes
