# WordPress Docker Setup

This project contains a complete WordPress installation using Docker Compose.

## Services Included

- **WordPress**: Latest version running on port 8082
- **MySQL 8.0**: Database server
- **phpMyAdmin**: Database management interface on port 8083

## Quick Start

1. Make sure <PERSON><PERSON> and Docker Compose are installed on your system
2. <PERSON><PERSON> or navigate to this directory
3. Run the following command to start all services:

```bash
docker-compose up -d
```

## Access Points

- **WordPress Site**: http://localhost:8082
- **phpMyAdmin**: http://localhost:8083

## Default Credentials

### Database (for phpMyAdmin)
- **Server**: db
- **Username**: root
- **Password**: root_password_123

### WordPress Database
- **Database Name**: wordpress_db
- **Username**: wordpress_user
- **Password**: wordpress_password_123

## Useful Commands

### Start services
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### View logs
```bash
docker-compose logs -f
```

### Restart services
```bash
docker-compose restart
```

### Remove everything (including volumes)
```bash
docker-compose down -v
```

## File Structure

- `docker-compose.yml`: Main Docker Compose configuration
- `.env`: Environment variables (database credentials)
- `wp-content/`: WordPress content directory (themes, plugins, uploads)
- WordPress data and database are stored in Docker volumes

## Security Notes

- Change the default passwords in `.env` file before deploying to production
- Consider using Docker secrets for production deployments
- The current setup is optimized for development purposes
