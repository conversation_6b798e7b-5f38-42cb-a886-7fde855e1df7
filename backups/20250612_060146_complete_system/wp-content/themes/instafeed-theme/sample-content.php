<?php
/**
 * Sample Content Generator for InstaFeed Theme
 * This file creates sample posts with placeholder images
 * Run this once to populate your site with demo content
 */

// Only run if WordPress is loaded
if (!defined('ABSPATH')) {
    // If running standalone, include WordPress
    require_once('../../../wp-config.php');
}

function create_instafeed_sample_content() {
    // Sample post data
    $sample_posts = array(
        array(
            'title' => 'Beautiful Mountain Landscape',
            'content' => 'Captured this stunning mountain view during my morning hike. The way the light hits the peaks is absolutely breathtaking. Nature never fails to amaze me with its incredible beauty and power.',
            'category' => 'nature',
            'image_id' => 1
        ),
        array(
            'title' => 'Urban Street Photography',
            'content' => 'Walking through the city streets, I found this amazing architectural detail. The contrast between old and new buildings tells the story of urban evolution.',
            'category' => 'city',
            'image_id' => 2
        ),
        array(
            'title' => 'Delicious Homemade Pasta',
            'content' => 'Spent the afternoon making fresh pasta from scratch. There\'s something therapeutic about the process of creating food with your own hands.',
            'category' => 'food',
            'image_id' => 3
        ),
        array(
            'title' => 'Sunset at the Beach',
            'content' => 'Perfect ending to a perfect day. The colors in the sky were absolutely magical, reflecting off the calm ocean waters.',
            'category' => 'nature',
            'image_id' => 4
        ),
        array(
            'title' => 'Coffee Shop Vibes',
            'content' => 'My favorite corner in the local coffee shop. Great lighting, amazing coffee, and the perfect spot for people watching.',
            'category' => 'lifestyle',
            'image_id' => 5
        ),
        array(
            'title' => 'Modern Architecture',
            'content' => 'This building\'s design is a perfect example of contemporary architecture. Clean lines, innovative use of materials, and stunning visual impact.',
            'category' => 'architecture',
            'image_id' => 6
        ),
        array(
            'title' => 'Fresh Market Produce',
            'content' => 'Nothing beats the vibrant colors and fresh quality of local market produce. Supporting local farmers and eating healthy!',
            'category' => 'food',
            'image_id' => 7
        ),
        array(
            'title' => 'City Lights at Night',
            'content' => 'The city transforms at night. Neon lights, busy streets, and the energy of urban life create an entirely different atmosphere.',
            'category' => 'city',
            'image_id' => 8
        ),
        array(
            'title' => 'Peaceful Forest Path',
            'content' => 'Sometimes you need to disconnect and find peace in nature. This forest path leads to some of the most serene spots I\'ve ever discovered.',
            'category' => 'nature',
            'image_id' => 9
        ),
        array(
            'title' => 'Artistic Food Presentation',
            'content' => 'Food is art, and presentation matters. This dish is not only delicious but also a feast for the eyes.',
            'category' => 'food',
            'image_id' => 10
        ),
        array(
            'title' => 'Vintage Car Detail',
            'content' => 'Found this beautiful vintage car at a local show. The attention to detail and craftsmanship from this era is truly remarkable.',
            'category' => 'vintage',
            'image_id' => 11
        ),
        array(
            'title' => 'Ocean Waves',
            'content' => 'The power and beauty of ocean waves never gets old. Each wave is unique, carrying stories from across the vast ocean.',
            'category' => 'nature',
            'image_id' => 12
        ),
        array(
            'title' => 'Street Art Discovery',
            'content' => 'Urban art at its finest. This mural tells a story of community, creativity, and the power of public art to transform spaces.',
            'category' => 'art',
            'image_id' => 13
        ),
        array(
            'title' => 'Golden Hour Portrait',
            'content' => 'Captured during the golden hour, when the light is soft and warm. There\'s something magical about this time of day for photography.',
            'category' => 'portrait',
            'image_id' => 14
        ),
        array(
            'title' => 'Minimalist Interior',
            'content' => 'Clean, simple, and functional. This interior design approach creates a calm and peaceful living space.',
            'category' => 'interior',
            'image_id' => 15
        )
    );
    
    $created_posts = 0;
    
    foreach ($sample_posts as $index => $post_data) {
        // Create the post
        $post_args = array(
            'post_title' => $post_data['title'],
            'post_content' => $post_data['content'],
            'post_excerpt' => substr($post_data['content'], 0, 100) . '...',
            'post_status' => 'publish',
            'post_type' => 'post',
            'meta_input' => array(
                '_instafeed_sample' => 'yes',
                '_instafeed_category' => $post_data['category']
            )
        );
        
        $post_id = wp_insert_post($post_args);
        
        if ($post_id && !is_wp_error($post_id)) {
            // Create a placeholder featured image
            create_placeholder_image($post_id, $post_data['image_id'], $post_data['category']);
            $created_posts++;
        }
    }
    
    // Create additional posts to reach 50
    for ($i = 16; $i <= 50; $i++) {
        $categories = array('nature', 'city', 'food', 'travel', 'art', 'lifestyle');
        $category = $categories[array_rand($categories)];
        
        $post_args = array(
            'post_title' => 'Sample Post #' . $i,
            'post_content' => 'This is sample content for post number ' . $i . '. It showcases the ' . $category . ' category with beautiful imagery and engaging content.',
            'post_excerpt' => 'Sample content for post #' . $i . ' in the ' . $category . ' category.',
            'post_status' => 'publish',
            'post_type' => 'post',
            'meta_input' => array(
                '_instafeed_sample' => 'yes',
                '_instafeed_category' => $category
            )
        );
        
        $post_id = wp_insert_post($post_args);
        
        if ($post_id && !is_wp_error($post_id)) {
            create_placeholder_image($post_id, $i, $category);
            $created_posts++;
        }
    }
    
    return $created_posts;
}

function create_placeholder_image($post_id, $image_id, $category) {
    // Create a placeholder image URL using a service like Lorem Picsum
    $image_url = "https://picsum.photos/800/800?random=" . $image_id;
    
    // For now, we'll just store the URL as meta data
    // In a real implementation, you might want to download and store the actual images
    update_post_meta($post_id, '_placeholder_image_url', $image_url);
    update_post_meta($post_id, '_thumbnail_id', 'placeholder_' . $image_id);
}

function delete_instafeed_sample_content() {
    $posts = get_posts(array(
        'meta_key' => '_instafeed_sample',
        'meta_value' => 'yes',
        'post_type' => 'post',
        'numberposts' => -1
    ));
    
    $deleted = 0;
    foreach ($posts as $post) {
        if (wp_delete_post($post->ID, true)) {
            $deleted++;
        }
    }
    
    return $deleted;
}

// If this file is accessed directly, run the content creation
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    $created = create_instafeed_sample_content();
    echo "Created " . $created . " sample posts for InstaFeed theme.\n";
}
