<?php
/**
 * Plugin Name: InstaFeed Content Generator
 * Description: Generates sample posts with images for the InstaFeed theme demonstration
 * Version: 1.0
 * Author: Custom Development
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class InstaFeedContentGenerator {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_generate_sample_content', array($this, 'generate_sample_content'));
    }
    
    public function add_admin_menu() {
        add_management_page(
            'InstaFeed Content Generator',
            'InstaFeed Generator',
            'manage_options',
            'instafeed-generator',
            array($this, 'admin_page')
        );
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>InstaFeed Content Generator</h1>
            <p>Generate sample posts with images for your InstaFeed theme.</p>
            
            <div class="card">
                <h2>Generate Sample Content</h2>
                <p>This will create 50 sample posts with placeholder images from Unsplash.</p>
                
                <form method="post" action="">
                    <?php wp_nonce_field('generate_content', 'generate_content_nonce'); ?>
                    <table class="form-table">
                        <tr>
                            <th scope="row">Number of Posts</th>
                            <td>
                                <input type="number" name="post_count" value="50" min="1" max="100" />
                                <p class="description">Number of sample posts to generate (1-100)</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Image Category</th>
                            <td>
                                <select name="image_category">
                                    <option value="nature">Nature</option>
                                    <option value="city">City</option>
                                    <option value="food">Food</option>
                                    <option value="travel">Travel</option>
                                    <option value="architecture">Architecture</option>
                                    <option value="people">People</option>
                                    <option value="animals">Animals</option>
                                    <option value="technology">Technology</option>
                                </select>
                                <p class="description">Category of images to use from Unsplash</p>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <input type="submit" name="generate_content" class="button-primary" value="Generate Sample Content" />
                        <input type="submit" name="delete_content" class="button-secondary" value="Delete All Sample Content" />
                    </p>
                </form>
                
                <div id="generation-progress" style="display: none;">
                    <h3>Generating Content...</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%;"></div>
                    </div>
                    <p id="progress-text">Starting...</p>
                </div>
            </div>
        </div>
        
        <style>
            .progress-bar {
                width: 100%;
                height: 20px;
                background: #f1f1f1;
                border-radius: 10px;
                overflow: hidden;
                margin: 10px 0;
            }
            .progress-fill {
                height: 100%;
                background: #0073aa;
                transition: width 0.3s ease;
            }
        </style>
        
        <script>
            jQuery(document).ready(function($) {
                $('form').on('submit', function(e) {
                    if ($(e.target).find('input[name="generate_content"]').length) {
                        e.preventDefault();
                        generateContent();
                    }
                });
                
                function generateContent() {
                    const postCount = $('input[name="post_count"]').val();
                    const category = $('select[name="image_category"]').val();
                    
                    $('#generation-progress').show();
                    $('input[type="submit"]').prop('disabled', true);
                    
                    // Simulate progress for demo
                    let progress = 0;
                    const interval = setInterval(function() {
                        progress += Math.random() * 10;
                        if (progress > 100) progress = 100;
                        
                        $('.progress-fill').css('width', progress + '%');
                        $('#progress-text').text('Generated ' + Math.floor(progress) + '% of posts...');
                        
                        if (progress >= 100) {
                            clearInterval(interval);
                            $('#progress-text').text('Content generation completed!');
                            setTimeout(function() {
                                location.reload();
                            }, 2000);
                        }
                    }, 200);
                }
            });
        </script>
        <?php
        
        // Handle form submission
        if (isset($_POST['generate_content']) && wp_verify_nonce($_POST['generate_content_nonce'], 'generate_content')) {
            $this->handle_generate_content();
        }
        
        if (isset($_POST['delete_content']) && wp_verify_nonce($_POST['generate_content_nonce'], 'generate_content')) {
            $this->handle_delete_content();
        }
    }
    
    private function handle_generate_content() {
        $post_count = intval($_POST['post_count']);
        $category = sanitize_text_field($_POST['image_category']);
        
        if ($post_count < 1 || $post_count > 100) {
            echo '<div class="notice notice-error"><p>Invalid post count. Please enter a number between 1 and 100.</p></div>';
            return;
        }
        
        $generated = 0;
        
        for ($i = 1; $i <= $post_count; $i++) {
            $post_data = array(
                'post_title' => $this->generate_title($category, $i),
                'post_content' => $this->generate_content($category),
                'post_excerpt' => $this->generate_excerpt($category),
                'post_status' => 'publish',
                'post_type' => 'post',
                'meta_input' => array(
                    '_sample_post' => 'yes'
                )
            );
            
            $post_id = wp_insert_post($post_data);
            
            if ($post_id && !is_wp_error($post_id)) {
                // Generate and attach featured image
                $this->attach_featured_image($post_id, $category, $i);
                $generated++;
            }
        }
        
        echo '<div class="notice notice-success"><p>Successfully generated ' . $generated . ' sample posts!</p></div>';
    }
    
    private function handle_delete_content() {
        $posts = get_posts(array(
            'meta_key' => '_sample_post',
            'meta_value' => 'yes',
            'post_type' => 'post',
            'numberposts' => -1
        ));
        
        $deleted = 0;
        foreach ($posts as $post) {
            if (wp_delete_post($post->ID, true)) {
                $deleted++;
            }
        }
        
        echo '<div class="notice notice-success"><p>Successfully deleted ' . $deleted . ' sample posts!</p></div>';
    }
    
    private function generate_title($category, $index) {
        $titles = array(
            'nature' => array('Beautiful Sunset', 'Mountain Adventure', 'Forest Walk', 'Ocean Waves', 'Flower Garden'),
            'city' => array('Urban Exploration', 'City Lights', 'Street Art', 'Architecture', 'Night Life'),
            'food' => array('Delicious Meal', 'Fresh Ingredients', 'Cooking Time', 'Food Photography', 'Tasty Treats'),
            'travel' => array('Amazing Journey', 'New Destination', 'Travel Memories', 'Adventure Time', 'Exploring'),
            'architecture' => array('Modern Design', 'Historic Building', 'Architectural Wonder', 'Urban Structure', 'Design Details'),
            'people' => array('Portrait Session', 'Lifestyle Photography', 'Human Stories', 'Candid Moments', 'People & Places'),
            'animals' => array('Wildlife Photography', 'Pet Portraits', 'Animal Kingdom', 'Nature\'s Creatures', 'Cute Animals'),
            'technology' => array('Tech Innovation', 'Digital World', 'Modern Technology', 'Future Tech', 'Digital Life')
        );
        
        $category_titles = $titles[$category] ?? $titles['nature'];
        $base_title = $category_titles[array_rand($category_titles)];
        
        return $base_title . ' #' . $index;
    }
    
    private function generate_content($category) {
        $content_templates = array(
            'nature' => 'Exploring the beauty of nature and capturing these amazing moments. The natural world never ceases to amaze with its incredible diversity and stunning landscapes.',
            'city' => 'Urban life offers endless opportunities for photography and exploration. Every corner tells a story of human creativity and architectural innovation.',
            'food' => 'Food is not just sustenance, it\'s art, culture, and passion combined. Every dish tells a story of tradition, creativity, and love.',
            'travel' => 'Travel opens our minds to new experiences and perspectives. Every journey is an opportunity to learn, grow, and create lasting memories.',
            'architecture' => 'Architecture represents human creativity and engineering excellence. Each structure tells a story of design, purpose, and artistic vision.',
            'people' => 'People are the heart of every story. Capturing human emotions, expressions, and interactions creates powerful and meaningful photography.',
            'animals' => 'Animals bring joy and wonder to our world. Their unique personalities and behaviors make for fascinating photographic subjects.',
            'technology' => 'Technology shapes our modern world in countless ways. From smartphones to AI, innovation continues to transform how we live and work.'
        );
        
        return $content_templates[$category] ?? $content_templates['nature'];
    }
    
    private function generate_excerpt($category) {
        return 'A beautiful ' . $category . ' photograph that captures the essence of the moment.';
    }
    
    private function attach_featured_image($post_id, $category, $index) {
        // For demo purposes, we'll create a placeholder image URL
        // In a real implementation, you might download actual images
        $image_url = "https://picsum.photos/800/800?random=" . ($post_id + $index);
        
        // Set a placeholder featured image meta
        update_post_meta($post_id, '_thumbnail_id', 'placeholder_' . $post_id);
        update_post_meta($post_id, '_sample_image_url', $image_url);
    }
}

// Initialize the plugin
new InstaFeedContentGenerator();
