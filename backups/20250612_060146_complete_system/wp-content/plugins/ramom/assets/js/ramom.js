/**
 * Ramom Plugin JavaScript
 * Handles user interactions, AJAX requests, and UI updates
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initializeRamom();
    });

    /**
     * Initialize all Ramom functionality
     */
    function initializeRamom() {
        bindInteractionEvents();
        bindModalEvents();
        bindFormEvents();
        initializeNotifications();
    }

    /**
     * Bind interaction button events
     */
    function bindInteractionEvents() {
        // Like button
        $(document).on('click', '.ramom-like-btn', function(e) {
            e.preventDefault();
            handleLikeToggle($(this));
        });

        // Follow button
        $(document).on('click', '.ramom-follow-btn', function(e) {
            e.preventDefault();
            handleFollowToggle($(this));
        });

        // Share button
        $(document).on('click', '.ramom-share-btn', function(e) {
            e.preventDefault();
            handleShare($(this));
        });

        // User post button
        $(document).on('click', '.ramom-user-post-btn', function(e) {
            e.preventDefault();
            openUserPostModal();
        });
    }

    /**
     * Bind modal events
     */
    function bindModalEvents() {
        // Close modal
        $(document).on('click', '.ramom-modal-close, .ramom-modal', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Prevent modal content clicks from closing modal
        $(document).on('click', '.ramom-modal-content', function(e) {
            e.stopPropagation();
        });

        // ESC key to close modal
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    }

    /**
     * Bind form events
     */
    function bindFormEvents() {
        // User post form submission
        $(document).on('submit', '#ramom-user-post-form', function(e) {
            e.preventDefault();
            handleUserPostSubmission($(this));
        });
    }

    /**
     * Handle like toggle
     */
    function handleLikeToggle($button) {
        if ($button.hasClass('ramom-loading')) return;

        const postId = $button.data('post-id');
        const $likeText = $button.find('.ramom-like-text');
        const $likeCount = $button.find('.ramom-like-count');

        $button.addClass('ramom-loading');

        $.ajax({
            url: ramom_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ramom_toggle_like',
                post_id: postId,
                nonce: ramom_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    
                    if (data.liked) {
                        $button.addClass('ramom-liked');
                        $likeText.text(ramom_ajax.strings.unlike);
                    } else {
                        $button.removeClass('ramom-liked');
                        $likeText.text(ramom_ajax.strings.like);
                    }
                    
                    $likeCount.text('(' + data.likes_count + ')');
                    showNotification(data.message, 'success');
                } else {
                    showNotification(response.data || ramom_ajax.strings.error, 'error');
                }
            },
            error: function() {
                showNotification(ramom_ajax.strings.error, 'error');
            },
            complete: function() {
                $button.removeClass('ramom-loading');
            }
        });
    }

    /**
     * Handle follow toggle
     */
    function handleFollowToggle($button) {
        if ($button.hasClass('ramom-loading')) return;

        const userId = $button.data('user-id');
        const $followText = $button.find('.ramom-follow-text');

        $button.addClass('ramom-loading');

        $.ajax({
            url: ramom_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ramom_toggle_follow',
                user_id: userId,
                nonce: ramom_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    
                    if (data.following) {
                        $button.addClass('ramom-following');
                        $followText.text($followText.text().replace(ramom_ajax.strings.follow, ramom_ajax.strings.unfollow));
                    } else {
                        $button.removeClass('ramom-following');
                        $followText.text($followText.text().replace(ramom_ajax.strings.unfollow, ramom_ajax.strings.follow));
                    }
                    
                    showNotification(data.message, 'success');
                } else {
                    showNotification(response.data || ramom_ajax.strings.error, 'error');
                }
            },
            error: function() {
                showNotification(ramom_ajax.strings.error, 'error');
            },
            complete: function() {
                $button.removeClass('ramom-loading');
            }
        });
    }

    /**
     * Handle share
     */
    function handleShare($button) {
        const postId = $button.data('post-id');
        
        // Show share options
        const shareOptions = [
            { platform: 'twitter', name: 'Twitter', icon: '🐦' },
            { platform: 'facebook', name: 'Facebook', icon: '📘' },
            { platform: 'linkedin', name: 'LinkedIn', icon: '💼' },
            { platform: 'whatsapp', name: 'WhatsApp', icon: '💬' },
            { platform: 'telegram', name: 'Telegram', icon: '✈️' },
            { platform: 'pinterest', name: 'Pinterest', icon: '📌' }
        ];

        let shareHtml = '<div class="ramom-share-options">';
        shareOptions.forEach(option => {
            shareHtml += `<button class="ramom-share-option" data-platform="${option.platform}" data-post-id="${postId}">
                ${option.icon} ${option.name}
            </button>`;
        });
        shareHtml += '</div>';

        // Create temporary modal for share options
        const $shareModal = $(`
            <div class="ramom-modal" id="ramom-share-modal">
                <div class="ramom-modal-content">
                    <div class="ramom-modal-header">
                        <h3>Share Post</h3>
                        <button class="ramom-modal-close">&times;</button>
                    </div>
                    <div class="ramom-modal-body">
                        ${shareHtml}
                    </div>
                </div>
            </div>
        `);

        $('body').append($shareModal);
        $shareModal.show();

        // Handle share option clicks
        $(document).on('click', '.ramom-share-option', function() {
            const platform = $(this).data('platform');
            const postId = $(this).data('post-id');

            $.ajax({
                url: ramom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'ramom_share_post',
                    post_id: postId,
                    platform: platform,
                    nonce: ramom_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        window.open(response.data.share_url, '_blank', 'width=600,height=400');
                        showNotification(response.data.message, 'success');
                        $shareModal.remove();
                    } else {
                        showNotification(response.data || ramom_ajax.strings.error, 'error');
                    }
                },
                error: function() {
                    showNotification(ramom_ajax.strings.error, 'error');
                }
            });
        });
    }

    /**
     * Open user post modal
     */
    function openUserPostModal() {
        if (!ramom_ajax.user_id) {
            showNotification('Please log in to create posts', 'error');
            return;
        }

        $('#ramom-user-post-modal').show();
    }

    /**
     * Close modal
     */
    function closeModal() {
        $('.ramom-modal').hide();
        $('#ramom-share-modal').remove();
    }

    /**
     * Handle user post form submission
     */
    function handleUserPostSubmission($form) {
        const $submitBtn = $form.find('button[type="submit"]');
        const originalText = $submitBtn.text();

        $submitBtn.prop('disabled', true).text(ramom_ajax.strings.loading);

        const formData = {
            action: 'ramom_create_user_post',
            post_title: $form.find('#ramom-post-title').val(),
            post_content: $form.find('#ramom-post-content').val(),
            post_image: $form.find('#ramom-post-image').val(),
            post_category: $form.find('#ramom-post-category').val(),
            nonce: ramom_ajax.nonce
        };

        $.ajax({
            url: ramom_ajax.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    $form[0].reset();
                    closeModal();
                    
                    // Optionally redirect to the new post
                    if (response.data.post_url) {
                        setTimeout(() => {
                            window.location.href = response.data.post_url;
                        }, 1500);
                    }
                } else {
                    showNotification(response.data || ramom_ajax.strings.error, 'error');
                }
            },
            error: function() {
                showNotification(ramom_ajax.strings.error, 'error');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const $notification = $(`
            <div class="ramom-notification ${type}">
                ${message}
            </div>
        `);

        $('body').append($notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            $notification.fadeOut(() => {
                $notification.remove();
            });
        }, 3000);
    }

    /**
     * Initialize notifications system
     */
    function initializeNotifications() {
        // Remove existing notifications on click
        $(document).on('click', '.ramom-notification', function() {
            $(this).fadeOut(() => {
                $(this).remove();
            });
        });
    }

    /**
     * Load user feed (for future enhancement)
     */
    function loadUserFeed(page = 1) {
        $.ajax({
            url: ramom_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ramom_get_user_feed',
                page: page,
                nonce: ramom_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Handle feed data
                    console.log('User feed loaded:', response.data);
                }
            }
        });
    }

    /**
     * Load trending posts (for future enhancement)
     */
    function loadTrendingPosts() {
        $.ajax({
            url: ramom_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ramom_get_trending_posts',
                nonce: ramom_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Handle trending data
                    console.log('Trending posts loaded:', response.data);
                }
            }
        });
    }

    // Make functions available globally if needed
    window.ramomShowNotification = showNotification;
    window.ramomLoadUserFeed = loadUserFeed;
    window.ramomLoadTrendingPosts = loadTrendingPosts;

})(jQuery);
