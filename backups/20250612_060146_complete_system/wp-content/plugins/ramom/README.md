# Ramom - Random Post Generator & User Interaction Plugin

A comprehensive WordPress plugin that generates random posts automatically and enables rich user interactions like likes, follows, and user-generated content. Perfect for creating engaging, social media-style WordPress sites.

## Features

### 🎲 Random Post Generation
- **Automatic Post Creation**: Generates random posts on scheduled intervals
- **Multiple Categories**: Photography, lifestyle, travel, food, and motivation content
- **Smart Content**: Realistic titles and content using predefined templates
- **Placeholder Images**: Automatic image assignment using Lorem Picsum
- **Bulk Generation**: Create multiple posts at once from admin panel

### 👥 User Interactions
- **Like System**: Users can like/unlike posts with real-time updates
- **Follow System**: Follow/unfollow other users
- **User-Generated Content**: Allow users to create their own posts
- **Social Sharing**: Share posts on multiple social platforms
- **Activity Tracking**: Complete user activity logging

### 📊 Analytics & Admin
- **Comprehensive Dashboard**: View statistics and user activity
- **Real-time Stats**: Likes, follows, posts, and engagement metrics
- **User Management**: View user stats and social connections
- **Content Moderation**: Approve/reject user-generated content
- **Activity Logs**: Track all user interactions

### 🎨 Theme Integration
- **Seamless Integration**: Works perfectly with InstaFeed theme
- **Responsive Design**: Mobile-optimized interaction buttons
- **Modal Interfaces**: Clean, Instagram-style user interfaces
- **AJAX Powered**: Smooth, no-refresh interactions

## Installation

1. Upload the `ramom` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to **Ramom** in your admin menu to configure settings
4. Start generating content and enable user interactions!

## Quick Start

### 1. Basic Setup
1. Activate the plugin
2. Go to **Ramom → Settings** to configure options
3. Enable auto-posting and user interactions
4. Set posting intervals and limits

### 2. Generate Sample Content
1. Go to **Ramom → Post Generator**
2. Click "Generate Posts" to create sample content
3. Choose number of posts (1-100)
4. Posts will be created with random content and images

### 3. Enable User Features
1. Ensure "Enable likes" is checked in settings
2. Enable "Enable follows" for user connections
3. Allow "Enable user-generated posts" for user content
4. Set moderation preferences if needed

## Configuration

### Auto-Posting Settings
- **Enable/Disable**: Toggle automatic post generation
- **Intervals**: Hourly, twice daily, daily, or weekly
- **Categories**: Choose content categories for generation

### User Interaction Settings
- **Likes**: Enable/disable post liking
- **Follows**: Enable/disable user following
- **User Posts**: Allow users to create content
- **Post Limits**: Maximum posts per user per day
- **Moderation**: Require approval for user posts

### Content Categories
The plugin generates content in these categories:
- **Photography**: Camera, art, creative content
- **Lifestyle**: Daily life, self-care, inspiration
- **Travel**: Adventures, destinations, experiences
- **Food**: Cooking, restaurants, recipes
- **Motivation**: Success, growth, positivity

## User Interface

### Interaction Buttons
Each post displays interaction buttons:
- **❤️ Like**: Like/unlike posts with live count updates
- **👤 Follow**: Follow/unfollow post authors
- **📤 Share**: Share on social media platforms
- **➕ Create**: Open post creation modal (logged-in users)

### User Post Creation
Users can create posts with:
- Title and content
- Optional image URL
- Category selection
- Instant publishing or moderation queue

### Social Sharing
Share posts on:
- Twitter
- Facebook
- LinkedIn
- WhatsApp
- Telegram
- Pinterest

## Admin Features

### Dashboard Widget
- Today's activity summary
- Quick stats overview
- Direct links to full statistics

### Statistics Page
- Total likes, follows, and activities
- Generated posts count
- User engagement metrics
- Visual charts and graphs

### Activity Monitor
- Real-time user activity feed
- Activity types: likes, follows, posts, shares
- User identification and timestamps
- Object linking (posts, users)

### Post Generator
- Manual post generation
- Bulk creation (up to 100 posts)
- Category-specific generation
- Generated content management

## Database Tables

The plugin creates these custom tables:

### ramom_likes
- Stores post likes
- User and post relationships
- Timestamps for analytics

### ramom_follows
- User follow relationships
- Follower/following connections
- Social network data

### ramom_user_activity
- Complete activity log
- All user interactions
- Analytics and reporting data

## Hooks & Filters

### Actions
```php
// Triggered when auto-post is created
do_action('ramom_auto_post_created', $post_id);

// Triggered when user likes a post
do_action('ramom_post_liked', $user_id, $post_id);

// Triggered when user follows another user
do_action('ramom_user_followed', $follower_id, $following_id);
```

### Filters
```php
// Modify post generation templates
$templates = apply_filters('ramom_post_templates', $templates);

// Customize interaction buttons
$buttons = apply_filters('ramom_interaction_buttons', $buttons, $post_id);

// Filter user permissions
$can_create = apply_filters('ramom_user_can_create_post', $can_create, $user_id);
```

## AJAX Endpoints

### Public Endpoints
- `ramom_toggle_like`: Like/unlike posts
- `ramom_toggle_follow`: Follow/unfollow users
- `ramom_create_user_post`: Create user posts
- `ramom_share_post`: Generate share links
- `ramom_get_user_feed`: Load personalized feed
- `ramom_get_trending_posts`: Get trending content

### Admin Endpoints
- `ramom_admin_generate_post`: Generate single post
- `ramom_admin_get_stats`: Load statistics
- `ramom_admin_moderate_post`: Moderate content

## Customization

### Styling
Override plugin styles in your theme:
```css
.ramom-interactions {
    /* Custom interaction button styles */
}

.ramom-modal {
    /* Custom modal styles */
}
```

### JavaScript
Extend functionality:
```javascript
// Custom notification handler
window.ramomShowNotification('Custom message', 'success');

// Load user feed
window.ramomLoadUserFeed(1);

// Load trending posts
window.ramomLoadTrendingPosts();
```

## Performance

### Optimization Features
- **AJAX Loading**: No page refreshes for interactions
- **Efficient Queries**: Optimized database queries
- **Caching Ready**: Compatible with caching plugins
- **Lazy Loading**: Progressive content loading
- **Minimal Resources**: Lightweight CSS and JS

### Recommended Settings
- Set reasonable auto-post intervals
- Limit user posts per day
- Enable content moderation for quality
- Use caching plugins for better performance

## Security

### Built-in Security
- **Nonce Verification**: All AJAX requests protected
- **User Capability Checks**: Proper permission validation
- **Data Sanitization**: All input sanitized and validated
- **SQL Injection Prevention**: Prepared statements used
- **XSS Protection**: Output properly escaped

### Best Practices
- Regular plugin updates
- Strong user passwords
- Limit admin access
- Monitor user activity
- Enable content moderation

## Troubleshooting

### Common Issues

**Interactions not working:**
- Check JavaScript console for errors
- Verify user is logged in for protected actions
- Ensure AJAX URL is correct

**Auto-posting not working:**
- Check WordPress cron is functioning
- Verify auto-post is enabled in settings
- Check server cron jobs

**Database errors:**
- Ensure proper database permissions
- Check table creation on activation
- Verify WordPress database prefix

### Debug Mode
Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Compatibility

### WordPress Requirements
- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

### Theme Compatibility
- Works with any WordPress theme
- Optimized for InstaFeed theme
- Responsive design for mobile devices
- RTL language support

### Plugin Compatibility
- Compatible with major caching plugins
- Works with SEO plugins
- Integrates with user management plugins
- Compatible with multilingual plugins

## Support

For support and customization:
1. Check plugin settings and configuration
2. Review WordPress error logs
3. Test with default theme to isolate issues
4. Disable other plugins to check for conflicts

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Random post generation system
- User interaction features (likes, follows)
- User-generated content
- Social sharing functionality
- Admin dashboard and statistics
- Complete activity tracking
- Theme integration
- Mobile-responsive design
