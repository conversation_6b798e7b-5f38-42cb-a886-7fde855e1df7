<?php
/**
 * Ramom AJAX Class
 * Handles all AJAX requests
 */

if (!defined('ABSPATH')) {
    exit;
}

class Ramom_Ajax {
    
    public function __construct() {
        // Public AJAX actions (for both logged-in and non-logged-in users)
        add_action('wp_ajax_ramom_get_post_data', array($this, 'get_post_data'));
        add_action('wp_ajax_nopriv_ramom_get_post_data', array($this, 'get_post_data'));
        
        add_action('wp_ajax_ramom_share_post', array($this, 'share_post'));
        add_action('wp_ajax_nopriv_ramom_share_post', array($this, 'share_post'));
        
        // Admin AJAX actions
        add_action('wp_ajax_ramom_admin_generate_post', array($this, 'admin_generate_post'));
        add_action('wp_ajax_ramom_admin_get_stats', array($this, 'admin_get_stats'));
        add_action('wp_ajax_ramom_admin_moderate_post', array($this, 'admin_moderate_post'));
        add_action('wp_ajax_ramom_delete_generated_posts', array($this, 'admin_delete_generated_posts'));
        add_action('wp_ajax_ramom_bulk_generate_posts', array($this, 'admin_bulk_generate_posts'));
        
        // User feed actions
        add_action('wp_ajax_ramom_get_user_feed', array($this, 'get_user_feed'));
        add_action('wp_ajax_ramom_get_trending_posts', array($this, 'get_trending_posts'));
    }
    
    public function get_post_data() {
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $post_id = intval($_POST['post_id']);
        if (!$post_id) {
            wp_send_json_error('Invalid post ID');
        }
        
        $post = get_post($post_id);
        if (!$post) {
            wp_send_json_error('Post not found');
        }
        
        $likes_count = $this->get_post_likes_count($post_id);
        $user_liked = $this->user_liked_post(get_current_user_id(), $post_id);
        $author = get_userdata($post->post_author);
        
        $data = array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'content' => $post->post_content,
            'excerpt' => $post->post_excerpt,
            'author' => array(
                'id' => $author->ID,
                'name' => $author->display_name,
                'avatar' => get_avatar_url($author->ID),
                'bio' => get_user_meta($author->ID, 'ramom_bio', true)
            ),
            'likes_count' => $likes_count,
            'user_liked' => $user_liked,
            'date' => get_the_date('c', $post),
            'permalink' => get_permalink($post),
            'featured_image' => get_the_post_thumbnail_url($post, 'large')
        );
        
        wp_send_json_success($data);
    }
    
    public function share_post() {
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $post_id = intval($_POST['post_id']);
        $platform = sanitize_text_field($_POST['platform'] ?? 'general');
        
        if (!$post_id) {
            wp_send_json_error('Invalid post ID');
        }
        
        $post = get_post($post_id);
        if (!$post) {
            wp_send_json_error('Post not found');
        }
        
        $share_url = get_permalink($post);
        $share_title = $post->post_title;
        $share_text = wp_trim_words($post->post_content, 20);
        
        $share_urls = array(
            'twitter' => 'https://twitter.com/intent/tweet?url=' . urlencode($share_url) . '&text=' . urlencode($share_title),
            'facebook' => 'https://www.facebook.com/sharer/sharer.php?u=' . urlencode($share_url),
            'linkedin' => 'https://www.linkedin.com/sharing/share-offsite/?url=' . urlencode($share_url),
            'pinterest' => 'https://pinterest.com/pin/create/button/?url=' . urlencode($share_url) . '&description=' . urlencode($share_title),
            'whatsapp' => 'https://wa.me/?text=' . urlencode($share_title . ' ' . $share_url),
            'telegram' => 'https://t.me/share/url?url=' . urlencode($share_url) . '&text=' . urlencode($share_title)
        );
        
        // Log share activity
        if (get_current_user_id()) {
            $this->log_activity(get_current_user_id(), 'post_shared', $post_id, 'post');
        }
        
        wp_send_json_success(array(
            'share_url' => $share_urls[$platform] ?? $share_url,
            'message' => __('Share link generated!', 'ramom')
        ));
    }
    
    public function admin_generate_post() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $generator = new Ramom_Post_Generator();
        $post_id = $generator->create_random_post();
        
        if ($post_id) {
            wp_send_json_success(array(
                'post_id' => $post_id,
                'post_url' => get_permalink($post_id),
                'edit_url' => get_edit_post_link($post_id),
                'message' => __('Random post generated successfully!', 'ramom')
            ));
        } else {
            wp_send_json_error('Failed to generate post');
        }
    }
    
    public function admin_get_stats() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        global $wpdb;
        
        $likes_table = $wpdb->prefix . 'ramom_likes';
        $follows_table = $wpdb->prefix . 'ramom_follows';
        $activity_table = $wpdb->prefix . 'ramom_user_activity';
        
        // Get stats for different time periods
        $stats = array(
            'today' => array(
                'likes' => $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $likes_table WHERE DATE(created_at) = %s",
                    current_time('Y-m-d')
                )),
                'follows' => $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $follows_table WHERE DATE(created_at) = %s",
                    current_time('Y-m-d')
                )),
                'activities' => $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $activity_table WHERE DATE(created_at) = %s",
                    current_time('Y-m-d')
                ))
            ),
            'week' => array(
                'likes' => $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $likes_table WHERE created_at >= %s",
                    date('Y-m-d', strtotime('-7 days'))
                )),
                'follows' => $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $follows_table WHERE created_at >= %s",
                    date('Y-m-d', strtotime('-7 days'))
                )),
                'activities' => $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $activity_table WHERE created_at >= %s",
                    date('Y-m-d', strtotime('-7 days'))
                ))
            ),
            'total' => array(
                'likes' => $wpdb->get_var("SELECT COUNT(*) FROM $likes_table"),
                'follows' => $wpdb->get_var("SELECT COUNT(*) FROM $follows_table"),
                'activities' => $wpdb->get_var("SELECT COUNT(*) FROM $activity_table")
            )
        );
        
        wp_send_json_success($stats);
    }
    
    public function admin_moderate_post() {
        if (!current_user_can('moderate_comments')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $post_id = intval($_POST['post_id']);
        $action = sanitize_text_field($_POST['action_type']);
        
        if (!$post_id) {
            wp_send_json_error('Invalid post ID');
        }
        
        switch ($action) {
            case 'approve':
                wp_update_post(array('ID' => $post_id, 'post_status' => 'publish'));
                $message = __('Post approved and published', 'ramom');
                break;
                
            case 'reject':
                wp_update_post(array('ID' => $post_id, 'post_status' => 'draft'));
                $message = __('Post rejected and moved to draft', 'ramom');
                break;
                
            case 'delete':
                wp_delete_post($post_id, true);
                $message = __('Post deleted permanently', 'ramom');
                break;
                
            default:
                wp_send_json_error('Invalid action');
        }
        
        wp_send_json_success(array('message' => $message));
    }
    
    public function get_user_feed() {
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to view your feed');
        }
        
        $page = intval($_POST['page'] ?? 1);
        $per_page = 10;
        
        // Get posts from followed users
        global $wpdb;
        $follows_table = $wpdb->prefix . 'ramom_follows';
        
        $following_ids = $wpdb->get_col($wpdb->prepare(
            "SELECT following_id FROM $follows_table WHERE follower_id = %d",
            $user_id
        ));
        
        if (empty($following_ids)) {
            // If not following anyone, show recent posts
            $following_ids = array(0);
        }
        
        $args = array(
            'author__in' => $following_ids,
            'post_status' => 'publish',
            'posts_per_page' => $per_page,
            'paged' => $page,
            'meta_query' => array(
                array(
                    'key' => '_thumbnail_id',
                    'compare' => 'EXISTS'
                )
            )
        );
        
        $posts = get_posts($args);
        $feed_data = array();
        
        foreach ($posts as $post) {
            $author = get_userdata($post->post_author);
            $likes_count = $this->get_post_likes_count($post->ID);
            $user_liked = $this->user_liked_post($user_id, $post->ID);
            
            $feed_data[] = array(
                'id' => $post->ID,
                'title' => $post->post_title,
                'excerpt' => $post->post_excerpt,
                'author' => array(
                    'id' => $author->ID,
                    'name' => $author->display_name,
                    'avatar' => get_avatar_url($author->ID)
                ),
                'likes_count' => $likes_count,
                'user_liked' => $user_liked,
                'date' => get_the_date('c', $post),
                'permalink' => get_permalink($post),
                'featured_image' => get_the_post_thumbnail_url($post, 'medium')
            );
        }
        
        wp_send_json_success($feed_data);
    }
    
    public function get_trending_posts() {
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        global $wpdb;
        $likes_table = $wpdb->prefix . 'ramom_likes';
        
        // Get posts with most likes in the last 7 days
        $trending_post_ids = $wpdb->get_col($wpdb->prepare(
            "SELECT l.post_id, COUNT(*) as like_count 
             FROM $likes_table l 
             INNER JOIN {$wpdb->posts} p ON l.post_id = p.ID 
             WHERE l.created_at >= %s AND p.post_status = 'publish'
             GROUP BY l.post_id 
             ORDER BY like_count DESC 
             LIMIT 10",
            date('Y-m-d H:i:s', strtotime('-7 days'))
        ));
        
        $trending_data = array();
        
        foreach ($trending_post_ids as $post_id) {
            $post = get_post($post_id);
            if ($post) {
                $author = get_userdata($post->post_author);
                $likes_count = $this->get_post_likes_count($post->ID);
                
                $trending_data[] = array(
                    'id' => $post->ID,
                    'title' => $post->post_title,
                    'author' => $author->display_name,
                    'likes_count' => $likes_count,
                    'permalink' => get_permalink($post),
                    'featured_image' => get_the_post_thumbnail_url($post, 'thumbnail')
                );
            }
        }
        
        wp_send_json_success($trending_data);
    }

    public function admin_delete_generated_posts() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        if (!wp_verify_nonce($_POST['nonce'], 'ramom_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }

        $generator = new Ramom_Post_Generator();
        $deleted = $generator->delete_generated_posts();

        if ($deleted > 0) {
            wp_send_json_success(array(
                'deleted' => $deleted,
                'message' => sprintf(__('Successfully deleted %d generated posts', 'ramom'), $deleted)
            ));
        } else {
            wp_send_json_error('No posts were deleted');
        }
    }

    public function admin_bulk_generate_posts() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        if (!wp_verify_nonce($_POST['nonce'], 'ramom_admin_nonce')) {
            wp_send_json_error('Security check failed');
        }

        $count = intval($_POST['count'] ?? 10);
        $category = sanitize_text_field($_POST['category'] ?? 'random');
        $use_media_library = isset($_POST['use_media_library']) && $_POST['use_media_library'] === 'true';

        if ($count < 1 || $count > 50) {
            wp_send_json_error('Invalid count. Must be between 1 and 50.');
        }

        $generator = new Ramom_Post_Generator();
        $created = 0;
        $errors = 0;

        for ($i = 0; $i < $count; $i++) {
            if ($generator->create_random_post($category, $use_media_library)) {
                $created++;
            } else {
                $errors++;
            }
        }

        wp_send_json_success(array(
            'created' => $created,
            'errors' => $errors,
            'message' => sprintf(__('Created %d posts successfully', 'ramom'), $created)
        ));
    }
    
    // Helper methods
    private function get_post_likes_count($post_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'ramom_likes';
        return $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table WHERE post_id = %d", $post_id));
    }
    
    private function user_liked_post($user_id, $post_id) {
        if (!$user_id) return false;
        
        global $wpdb;
        $table = $wpdb->prefix . 'ramom_likes';
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table WHERE user_id = %d AND post_id = %d",
            $user_id, $post_id
        ));
        return !empty($result);
    }
    
    private function log_activity($user_id, $activity_type, $object_id, $object_type) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'ramom_user_activity';
        $wpdb->insert(
            $table,
            array(
                'user_id' => $user_id,
                'activity_type' => $activity_type,
                'object_id' => $object_id,
                'object_type' => $object_type,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%s', '%d', '%s', '%s')
        );
    }
}
