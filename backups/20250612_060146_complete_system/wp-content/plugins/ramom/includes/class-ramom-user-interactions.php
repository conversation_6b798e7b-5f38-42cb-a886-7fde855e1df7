<?php
/**
 * Ramom User Interactions Class
 * Handles likes, follows, and user-generated content
 */

if (!defined('ABSPATH')) {
    exit;
}

class Ramom_User_Interactions {
    
    public function __construct() {
        add_action('wp_ajax_ramom_toggle_like', array($this, 'ajax_toggle_like'));
        add_action('wp_ajax_nopriv_ramom_toggle_like', array($this, 'ajax_toggle_like'));
        
        add_action('wp_ajax_ramom_toggle_follow', array($this, 'ajax_toggle_follow'));
        add_action('wp_ajax_nopriv_ramom_toggle_follow', array($this, 'ajax_toggle_follow'));
        
        add_action('wp_ajax_ramom_create_user_post', array($this, 'ajax_create_user_post'));
        add_action('wp_ajax_nopriv_ramom_create_user_post', array($this, 'ajax_create_user_post'));
        
        add_action('wp_ajax_ramom_get_user_stats', array($this, 'ajax_get_user_stats'));
        add_action('wp_ajax_nopriv_ramom_get_user_stats', array($this, 'ajax_get_user_stats'));
        
        // Add user stats to author pages
        add_action('show_user_profile', array($this, 'show_user_stats'));
        add_action('edit_user_profile', array($this, 'show_user_stats'));
    }
    
    public function ajax_toggle_like() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        $post_id = intval($_POST['post_id']);
        
        if (!$user_id) {
            wp_send_json_error('Please log in to like posts');
        }
        
        if (!$post_id) {
            wp_send_json_error('Invalid post ID');
        }
        
        $is_liked = $this->toggle_like($user_id, $post_id);
        $likes_count = $this->get_post_likes_count($post_id);
        
        // Log activity
        $activity_type = $is_liked ? 'post_liked' : 'post_unliked';
        $this->log_activity($user_id, $activity_type, $post_id, 'post');
        
        wp_send_json_success(array(
            'liked' => $is_liked,
            'likes_count' => $likes_count,
            'message' => $is_liked ? __('Post liked!', 'ramom') : __('Post unliked!', 'ramom')
        ));
    }
    
    public function ajax_toggle_follow() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $follower_id = get_current_user_id();
        $following_id = intval($_POST['user_id']);
        
        if (!$follower_id) {
            wp_send_json_error('Please log in to follow users');
        }
        
        if (!$following_id || $follower_id == $following_id) {
            wp_send_json_error('Invalid user ID');
        }
        
        $is_following = $this->toggle_follow($follower_id, $following_id);
        $followers_count = $this->get_user_followers_count($following_id);
        
        // Log activity
        $activity_type = $is_following ? 'user_followed' : 'user_unfollowed';
        $this->log_activity($follower_id, $activity_type, $following_id, 'user');
        
        $user_info = get_userdata($following_id);
        $message = $is_following 
            ? sprintf(__('You are now following %s', 'ramom'), $user_info->display_name)
            : sprintf(__('You unfollowed %s', 'ramom'), $user_info->display_name);
        
        wp_send_json_success(array(
            'following' => $is_following,
            'followers_count' => $followers_count,
            'message' => $message
        ));
    }
    
    public function ajax_create_user_post() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to create posts');
        }
        
        $title = sanitize_text_field($_POST['post_title']);
        $content = sanitize_textarea_field($_POST['post_content']);
        $image_url = esc_url_raw($_POST['post_image']);
        $category = sanitize_text_field($_POST['post_category']);
        
        if (empty($title) || empty($content)) {
            wp_send_json_error('Title and content are required');
        }
        
        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_excerpt' => wp_trim_words($content, 20),
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => $user_id,
            'meta_input' => array(
                '_ramom_user_generated' => 'yes',
                '_ramom_category' => $category
            )
        );
        
        $post_id = wp_insert_post($post_data);
        
        if ($post_id && !is_wp_error($post_id)) {
            // Add featured image if provided
            if (!empty($image_url)) {
                update_post_meta($post_id, '_placeholder_image_url', $image_url);
                update_post_meta($post_id, '_thumbnail_id', 'placeholder_' . $post_id);
            }
            
            // Assign category
            if (!empty($category)) {
                $term = get_term_by('slug', $category, 'category');
                if (!$term) {
                    $term = wp_insert_term(ucfirst($category), 'category', array('slug' => $category));
                    if (!is_wp_error($term)) {
                        $term_id = $term['term_id'];
                    }
                } else {
                    $term_id = $term->term_id;
                }
                
                if (isset($term_id)) {
                    wp_set_post_categories($post_id, array($term_id));
                }
            }
            
            // Log activity
            $this->log_activity($user_id, 'user_post_created', $post_id, 'post');
            
            wp_send_json_success(array(
                'post_id' => $post_id,
                'post_url' => get_permalink($post_id),
                'message' => __('Post created successfully!', 'ramom')
            ));
        } else {
            wp_send_json_error('Failed to create post');
        }
    }
    
    public function ajax_get_user_stats() {
        $user_id = intval($_POST['user_id'] ?? get_current_user_id());
        
        if (!$user_id) {
            wp_send_json_error('Invalid user ID');
        }
        
        $stats = $this->get_user_stats($user_id);
        wp_send_json_success($stats);
    }
    
    public function show_user_stats($user) {
        $stats = $this->get_user_stats($user->ID);
        ?>
        <h3><?php _e('Ramom Social Stats', 'ramom'); ?></h3>
        <table class="form-table">
            <tr>
                <th><?php _e('Posts Created', 'ramom'); ?></th>
                <td><?php echo $stats['posts_count']; ?></td>
            </tr>
            <tr>
                <th><?php _e('Total Likes Received', 'ramom'); ?></th>
                <td><?php echo $stats['likes_received']; ?></td>
            </tr>
            <tr>
                <th><?php _e('Followers', 'ramom'); ?></th>
                <td><?php echo $stats['followers_count']; ?></td>
            </tr>
            <tr>
                <th><?php _e('Following', 'ramom'); ?></th>
                <td><?php echo $stats['following_count']; ?></td>
            </tr>
            <tr>
                <th><?php _e('Member Since', 'ramom'); ?></th>
                <td><?php echo date('F j, Y', strtotime($user->user_registered)); ?></td>
            </tr>
        </table>
        <?php
    }
    
    // Core functionality methods
    private function toggle_like($user_id, $post_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'ramom_likes';
        
        // Check if already liked
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table WHERE user_id = %d AND post_id = %d",
            $user_id, $post_id
        ));
        
        if ($existing) {
            // Unlike
            $wpdb->delete($table, array('user_id' => $user_id, 'post_id' => $post_id));
            return false;
        } else {
            // Like
            $wpdb->insert($table, array(
                'user_id' => $user_id,
                'post_id' => $post_id,
                'created_at' => current_time('mysql')
            ));
            return true;
        }
    }
    
    private function toggle_follow($follower_id, $following_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'ramom_follows';
        
        // Check if already following
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table WHERE follower_id = %d AND following_id = %d",
            $follower_id, $following_id
        ));
        
        if ($existing) {
            // Unfollow
            $wpdb->delete($table, array('follower_id' => $follower_id, 'following_id' => $following_id));
            return false;
        } else {
            // Follow
            $wpdb->insert($table, array(
                'follower_id' => $follower_id,
                'following_id' => $following_id,
                'created_at' => current_time('mysql')
            ));
            return true;
        }
    }
    
    private function get_post_likes_count($post_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'ramom_likes';
        return $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table WHERE post_id = %d", $post_id));
    }
    
    private function get_user_followers_count($user_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'ramom_follows';
        return $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table WHERE following_id = %d", $user_id));
    }
    
    private function get_user_following_count($user_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'ramom_follows';
        return $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table WHERE follower_id = %d", $user_id));
    }
    
    private function get_user_stats($user_id) {
        global $wpdb;
        
        // Get posts count
        $posts_count = count(get_posts(array(
            'author' => $user_id,
            'post_status' => 'publish',
            'numberposts' => -1
        )));
        
        // Get total likes received on user's posts
        $likes_table = $wpdb->prefix . 'ramom_likes';
        $likes_received = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(l.id) FROM $likes_table l 
             INNER JOIN {$wpdb->posts} p ON l.post_id = p.ID 
             WHERE p.post_author = %d AND p.post_status = 'publish'",
            $user_id
        ));
        
        return array(
            'posts_count' => $posts_count,
            'likes_received' => $likes_received ?: 0,
            'followers_count' => $this->get_user_followers_count($user_id),
            'following_count' => $this->get_user_following_count($user_id)
        );
    }
    
    private function log_activity($user_id, $activity_type, $object_id, $object_type) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'ramom_user_activity';
        $wpdb->insert(
            $table,
            array(
                'user_id' => $user_id,
                'activity_type' => $activity_type,
                'object_id' => $object_id,
                'object_type' => $object_type,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%s', '%d', '%s', '%s')
        );
    }
}
