/**
 * InstaFeed Comments JavaScript
 * Handles Instagram-style comment interactions
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        initializeInstaFeedComments();
    });

    function initializeInstaFeedComments() {
        bindCommentEvents();
        setupEmojiPicker();
        setupTextareaAutoResize();
        setupRealTimeUpdates();
    }

    function bindCommentEvents() {
        // Submit comment form
        $(document).on('submit', '#commentform', function(e) {
            e.preventDefault();
            submitComment($(this));
        });

        // Reply button
        $(document).on('click', '.instafeed-reply-btn', function(e) {
            e.preventDefault();
            toggleReplyForm($(this));
        });

        // Reaction buttons
        $(document).on('click', '.instafeed-react-btn', function(e) {
            e.preventDefault();
            addReaction($(this));
        });

        // Load more comments
        $(document).on('click', '#instafeed-load-more-comments', function(e) {
            e.preventDefault();
            loadMoreComments();
        });

        // Emoji picker toggle
        $(document).on('click', '.instafeed-emoji-btn', function(e) {
            e.preventDefault();
            toggleEmojiPicker($(this));
        });

        // Emoji selection
        $(document).on('click', '.instafeed-emoji-panel .emoji', function(e) {
            e.preventDefault();
            insertEmoji($(this));
        });

        // Close emoji picker when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.instafeed-emoji-picker').length) {
                $('.instafeed-emoji-panel').removeClass('show');
            }
        });
    }

    function submitComment($form) {
        const $textarea = $form.find('#comment');
        const $submitBtn = $form.find('.instafeed-submit-comment');
        const commentText = $textarea.val().trim();

        if (!commentText) {
            showNotification(instafeed_comments_ajax.strings.comment_too_short, 'error');
            return;
        }

        if (!instafeed_comments_ajax.user_id) {
            showNotification(instafeed_comments_ajax.strings.login_required, 'error');
            return;
        }

        $submitBtn.prop('disabled', true).text(instafeed_comments_ajax.strings.loading);

        $.ajax({
            url: instafeed_comments_ajax.ajax_url,
            type: 'POST',
            data: $form.serialize() + '&action=instafeed_submit_comment&nonce=' + instafeed_comments_ajax.nonce,
            success: function(response) {
                if (response.success) {
                    $textarea.val('');
                    addCommentToList(response.data.comment_html);
                    updateCommentsCount(1);
                    showNotification(instafeed_comments_ajax.strings.comment_posted, 'success');
                } else {
                    showNotification(response.data || instafeed_comments_ajax.strings.error, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_comments_ajax.strings.error, 'error');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text(instafeed_comments_ajax.strings.post_comment);
            }
        });
    }

    function toggleReplyForm($button) {
        const commentId = $button.data('comment-id');
        const $replyContainer = $('#reply-form-' + commentId);

        if ($replyContainer.is(':visible')) {
            $replyContainer.slideUp();
            return;
        }

        // Hide other reply forms
        $('.instafeed-reply-form-container').slideUp();

        // Create reply form if it doesn't exist
        if ($replyContainer.is(':empty')) {
            const replyFormHtml = createReplyForm(commentId);
            $replyContainer.html(replyFormHtml);
        }

        $replyContainer.slideDown();
        $replyContainer.find('textarea').focus();
    }

    function createReplyForm(parentId) {
        const currentUser = instafeed_comments_ajax.user_id;
        const avatarUrl = `https://www.gravatar.com/avatar/${currentUser}?s=40&d=mp`;

        return `
            <div class="instafeed-reply-form">
                <div class="instafeed-comment-form-wrapper">
                    <div class="instafeed-comment-form-avatar">
                        <img src="${avatarUrl}" alt="Your avatar" class="instafeed-avatar" />
                    </div>
                    <div class="instafeed-comment-form-input">
                        <textarea placeholder="${instafeed_comments_ajax.strings.reply}..." rows="1"></textarea>
                        <div class="instafeed-reply-actions">
                            <button type="button" class="instafeed-submit-reply" data-parent-id="${parentId}">
                                ${instafeed_comments_ajax.strings.post_comment}
                            </button>
                            <button type="button" class="instafeed-cancel-reply">
                                ${instafeed_comments_ajax.strings.cancel}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function addReaction($button) {
        if (!instafeed_comments_ajax.user_id) {
            showNotification(instafeed_comments_ajax.strings.login_required, 'error');
            return;
        }

        const commentId = $button.data('comment-id');
        const reaction = $button.data('reaction');

        $.ajax({
            url: instafeed_comments_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_add_reaction',
                comment_id: commentId,
                reaction: reaction,
                nonce: instafeed_comments_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateReactionDisplay($button, response.data);
                }
            }
        });
    }

    function setupEmojiPicker() {
        // Emoji picker functionality is handled by click events
    }

    function toggleEmojiPicker($button) {
        const $panel = $button.siblings('.instafeed-emoji-panel');
        $('.instafeed-emoji-panel').not($panel).removeClass('show');
        $panel.toggleClass('show');
    }

    function insertEmoji($emoji) {
        const emojiChar = $emoji.data('emoji');
        const $textarea = $emoji.closest('.instafeed-comment-form-input').find('textarea');
        const currentText = $textarea.val();
        const cursorPos = $textarea[0].selectionStart;
        
        const newText = currentText.slice(0, cursorPos) + emojiChar + currentText.slice(cursorPos);
        $textarea.val(newText);
        
        // Set cursor position after emoji
        const newCursorPos = cursorPos + emojiChar.length;
        $textarea[0].setSelectionRange(newCursorPos, newCursorPos);
        $textarea.focus();
        
        // Hide emoji picker
        $('.instafeed-emoji-panel').removeClass('show');
    }

    function setupTextareaAutoResize() {
        $(document).on('input', '.instafeed-comment-form-input textarea', function() {
            autoResizeTextarea($(this));
        });
    }

    function autoResizeTextarea($textarea) {
        $textarea.css('height', 'auto');
        const scrollHeight = $textarea[0].scrollHeight;
        const maxHeight = 100; // Maximum height in pixels
        
        if (scrollHeight > maxHeight) {
            $textarea.css('height', maxHeight + 'px');
            $textarea.css('overflow-y', 'scroll');
        } else {
            $textarea.css('height', scrollHeight + 'px');
            $textarea.css('overflow-y', 'hidden');
        }
    }

    function setupRealTimeUpdates() {
        // Poll for new comments every 30 seconds
        if (instafeed_comments_ajax.real_time) {
            setInterval(checkForNewComments, 30000);
        }
    }

    function checkForNewComments() {
        const lastCommentId = $('.instafeed-comment').first().attr('id');
        
        $.ajax({
            url: instafeed_comments_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_check_new_comments',
                post_id: instafeed_comments_ajax.post_id,
                last_comment_id: lastCommentId,
                nonce: instafeed_comments_ajax.nonce
            },
            success: function(response) {
                if (response.success && response.data.new_comments) {
                    response.data.new_comments.forEach(function(commentHtml) {
                        addCommentToList(commentHtml, true);
                    });
                    updateCommentsCount(response.data.new_comments.length);
                }
            }
        });
    }

    function addCommentToList(commentHtml, prepend = false) {
        const $commentsList = $('.instafeed-comments-list');
        const $noComments = $('.instafeed-no-comments');
        
        if ($noComments.length) {
            $noComments.remove();
            $commentsList.show();
        }
        
        if (prepend) {
            $commentsList.prepend(commentHtml);
        } else {
            $commentsList.append(commentHtml);
        }
        
        // Animate new comment
        const $newComment = prepend ? $commentsList.children().first() : $commentsList.children().last();
        $newComment.css('opacity', 0).animate({opacity: 1}, 500);
    }

    function updateCommentsCount(change) {
        const $title = $('.instafeed-comments-title');
        const currentText = $title.text();
        const currentCount = parseInt(currentText.match(/\d+/)) || 0;
        const newCount = currentCount + change;
        
        if (newCount === 1) {
            $title.text('1 ' + instafeed_comments_ajax.strings.comment);
        } else {
            $title.text(newCount + ' ' + instafeed_comments_ajax.strings.comments);
        }
    }

    function updateReactionDisplay($button, data) {
        // Update reaction counts and visual feedback
        const $comment = $button.closest('.instafeed-comment');
        const $reactions = $comment.find('.instafeed-comment-reactions');
        
        // Update or create reaction display
        // Implementation depends on the response data structure
    }

    function loadMoreComments() {
        const $button = $('#instafeed-load-more-comments');
        $button.text(instafeed_comments_ajax.strings.loading);
        
        // Implementation for loading more comments via AJAX
        // This would typically involve pagination
    }

    function showNotification(message, type = 'info') {
        const $notification = $(`
            <div class="instafeed-notification ${type}">
                ${message}
            </div>
        `);
        
        $('body').append($notification);
        
        setTimeout(() => {
            $notification.fadeOut(() => {
                $notification.remove();
            });
        }, 3000);
    }

    // Handle reply form actions
    $(document).on('click', '.instafeed-submit-reply', function() {
        const $button = $(this);
        const parentId = $button.data('parent-id');
        const $textarea = $button.closest('.instafeed-reply-form').find('textarea');
        const replyText = $textarea.val().trim();
        
        if (!replyText) {
            showNotification(instafeed_comments_ajax.strings.comment_too_short, 'error');
            return;
        }
        
        // Submit reply via AJAX
        submitReply(parentId, replyText, $button);
    });

    $(document).on('click', '.instafeed-cancel-reply', function() {
        $(this).closest('.instafeed-reply-form-container').slideUp();
    });

    function submitReply(parentId, replyText, $button) {
        $button.prop('disabled', true).text(instafeed_comments_ajax.strings.loading);
        
        $.ajax({
            url: instafeed_comments_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_submit_reply',
                parent_id: parentId,
                comment: replyText,
                post_id: instafeed_comments_ajax.post_id,
                nonce: instafeed_comments_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Add reply to the comment thread
                    const $parentComment = $('#comment-' + parentId);
                    const $repliesContainer = $parentComment.find('.instafeed-comment-replies');
                    
                    if ($repliesContainer.length) {
                        $repliesContainer.append(response.data.reply_html);
                    } else {
                        $parentComment.append('<div class="instafeed-comment-replies">' + response.data.reply_html + '</div>');
                    }
                    
                    // Hide reply form
                    $button.closest('.instafeed-reply-form-container').slideUp();
                    
                    showNotification(instafeed_comments_ajax.strings.comment_posted, 'success');
                } else {
                    showNotification(response.data || instafeed_comments_ajax.strings.error, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_comments_ajax.strings.error, 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(instafeed_comments_ajax.strings.post_comment);
            }
        });
    }

})(jQuery);
