<?php
/**
 * Plugin Name: InstaFeed Comments
 * Plugin URI: https://example.com/instafeed-comments
 * Description: Instagram-style comment system that perfectly matches the InstaFeed theme design. Features real-time comments, emoji reactions, and mobile-optimized interface.
 * Version: 1.0.0
 * Author: Custom Development
 * License: GPL v2 or later
 * Text Domain: instafeed-comments
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('INSTAFEED_COMMENTS_URL', plugin_dir_url(__FILE__));
define('INSTAFEED_COMMENTS_PATH', plugin_dir_path(__FILE__));
define('INSTAFEED_COMMENTS_VERSION', '1.0.0');

// Include required files
require_once INSTAFEED_COMMENTS_PATH . 'includes/class-instafeed-comments-core.php';
require_once INSTAFEED_COMMENTS_PATH . 'includes/class-instafeed-comments-ajax.php';
require_once INSTAFEED_COMMENTS_PATH . 'includes/class-instafeed-comments-admin.php';

/**
 * Main InstaFeed Comments Plugin Class
 */
class InstaFeed_Comments_Plugin {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Initialize plugin components
        new InstaFeed_Comments_Core();
        new InstaFeed_Comments_Ajax();
        new InstaFeed_Comments_Admin();
        
        // Load text domain
        load_plugin_textdomain('instafeed-comments', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function enqueue_scripts() {
        // Only load on single posts and pages
        if (is_single() || is_page()) {
            // Enqueue CSS
            wp_enqueue_style(
                'instafeed-comments-style',
                INSTAFEED_COMMENTS_URL . 'assets/css/instafeed-comments.css',
                array(),
                INSTAFEED_COMMENTS_VERSION
            );
            
            // Enqueue JavaScript
            wp_enqueue_script(
                'instafeed-comments-script',
                INSTAFEED_COMMENTS_URL . 'assets/js/instafeed-comments.js',
                array('jquery'),
                INSTAFEED_COMMENTS_VERSION,
                true
            );
            
            // Localize script
            wp_localize_script('instafeed-comments-script', 'instafeed_comments_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('instafeed_comments_nonce'),
                'user_id' => get_current_user_id(),
                'post_id' => get_the_ID(),
                'strings' => array(
                    'reply' => __('Reply', 'instafeed-comments'),
                    'cancel' => __('Cancel', 'instafeed-comments'),
                    'post_comment' => __('Post', 'instafeed-comments'),
                    'loading' => __('Loading...', 'instafeed-comments'),
                    'error' => __('Something went wrong', 'instafeed-comments'),
                    'login_required' => __('Please log in to comment', 'instafeed-comments'),
                    'comment_posted' => __('Comment posted!', 'instafeed-comments'),
                    'comment_too_short' => __('Comment is too short', 'instafeed-comments'),
                    'show_more' => __('Show more comments', 'instafeed-comments'),
                    'show_less' => __('Show less', 'instafeed-comments'),
                )
            ));
        }
    }
    
    public function activate() {
        // Set default options
        add_option('instafeed_comments_enabled', 1);
        add_option('instafeed_comments_emoji_reactions', 1);
        add_option('instafeed_comments_real_time', 1);
        add_option('instafeed_comments_moderation', 0);
        add_option('instafeed_comments_max_length', 500);
        add_option('instafeed_comments_show_avatars', 1);
        add_option('instafeed_comments_show_timestamps', 1);
        
        // Create custom table for comment reactions
        $this->create_reactions_table();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    private function create_reactions_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'instafeed_comment_reactions';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            comment_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            reaction varchar(50) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_comment_reaction (user_id, comment_id, reaction),
            KEY comment_id (comment_id),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

// Initialize the plugin
function instafeed_comments_init() {
    return InstaFeed_Comments_Plugin::get_instance();
}

// Start the plugin
instafeed_comments_init();
