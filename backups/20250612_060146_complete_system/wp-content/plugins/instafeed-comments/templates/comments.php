<?php
/**
 * Instagram-style Comments Template
 * Custom comments template that matches the InstaFeed theme
 */

if (!defined('ABSPATH')) {
    exit;
}

$core = new InstaFeed_Comments_Core();
$comments_count = get_comments_number();
$post_id = get_the_ID();
?>

<div id="instafeed-comments" class="instafeed-comments-section">
    
    <?php if (have_comments()) : ?>
        <div class="instafeed-comments-header">
            <h3 class="instafeed-comments-title">
                <?php
                if ($comments_count == 1) {
                    echo '1 ' . __('comment', 'instafeed-comments');
                } else {
                    echo $comments_count . ' ' . __('comments', 'instafeed-comments');
                }
                ?>
            </h3>
        </div>

        <div class="instafeed-comments-list" id="instafeed-comments-list">
            <?php
            wp_list_comments(array(
                'style' => 'div',
                'short_ping' => true,
                'avatar_size' => 40,
                'callback' => 'instafeed_custom_comment_callback',
                'end-callback' => 'instafeed_custom_comment_end_callback',
                'per_page' => 10,
                'reverse_top_level' => false
            ));
            ?>
        </div>

        <?php if (get_comment_pages_count() > 1 && get_option('page_comments')) : ?>
            <div class="instafeed-comments-pagination">
                <button id="instafeed-load-more-comments" class="instafeed-load-more-btn">
                    <?php _e('Load more comments', 'instafeed-comments'); ?>
                </button>
            </div>
        <?php endif; ?>

    <?php else : ?>
        <div class="instafeed-no-comments">
            <p><?php _e('Be the first to comment!', 'instafeed-comments'); ?></p>
        </div>
    <?php endif; ?>

    <?php if (comments_open()) : ?>
        <div class="instafeed-comment-form-container">
            <?php comment_form(); ?>
        </div>
    <?php else : ?>
        <div class="instafeed-comments-closed">
            <p><?php _e('Comments are closed.', 'instafeed-comments'); ?></p>
        </div>
    <?php endif; ?>

</div>

<?php
/**
 * Custom comment callback function
 */
function instafeed_custom_comment_callback($comment, $args, $depth) {
    $core = new InstaFeed_Comments_Core();
    $user_id = get_current_user_id();
    $comment_id = $comment->comment_ID;
    $reactions = $core->get_comment_reactions($comment_id);
    $comment_time = $core->format_comment_time($comment->comment_date);
    $user_display_name = $core->get_user_display_name($comment->user_id);
    
    ?>
    <div <?php comment_class('instafeed-comment'); ?> id="comment-<?php comment_ID(); ?>">
        <div class="instafeed-comment-content">
            <div class="instafeed-comment-avatar">
                <?php echo get_avatar($comment, 40, '', '', array('class' => 'instafeed-avatar')); ?>
            </div>
            
            <div class="instafeed-comment-body">
                <div class="instafeed-comment-main">
                    <div class="instafeed-comment-text">
                        <span class="instafeed-comment-author"><?php echo esc_html($user_display_name); ?></span>
                        <span class="instafeed-comment-content-text"><?php comment_text(); ?></span>
                    </div>
                </div>
                
                <div class="instafeed-comment-meta">
                    <span class="instafeed-comment-time"><?php echo esc_html($comment_time); ?></span>
                    
                    <?php if (!empty($reactions)) : ?>
                        <span class="instafeed-comment-reactions">
                            <?php foreach ($reactions as $reaction => $count) : ?>
                                <span class="instafeed-reaction" data-reaction="<?php echo esc_attr($reaction); ?>">
                                    <?php echo $reaction; ?> <?php echo $count; ?>
                                </span>
                            <?php endforeach; ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php if (is_user_logged_in()) : ?>
                        <button class="instafeed-reply-btn" data-comment-id="<?php echo $comment_id; ?>">
                            <?php _e('Reply', 'instafeed-comments'); ?>
                        </button>
                        
                        <div class="instafeed-reaction-buttons">
                            <button class="instafeed-react-btn" data-comment-id="<?php echo $comment_id; ?>" data-reaction="❤️">❤️</button>
                            <button class="instafeed-react-btn" data-comment-id="<?php echo $comment_id; ?>" data-reaction="👍">👍</button>
                            <button class="instafeed-react-btn" data-comment-id="<?php echo $comment_id; ?>" data-reaction="😂">😂</button>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($comment->comment_parent == 0) : ?>
                    <div class="instafeed-reply-form-container" id="reply-form-<?php echo $comment_id; ?>" style="display: none;">
                        <!-- Reply form will be inserted here via JavaScript -->
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <?php
        // Display child comments (replies)
        $children = get_comments(array(
            'parent' => $comment_id,
            'status' => 'approve',
            'post_id' => get_the_ID()
        ));
        
        if (!empty($children)) :
        ?>
            <div class="instafeed-comment-replies">
                <?php foreach ($children as $child_comment) : ?>
                    <div class="instafeed-comment instafeed-comment-reply" id="comment-<?php echo $child_comment->comment_ID; ?>">
                        <div class="instafeed-comment-content">
                            <div class="instafeed-comment-avatar">
                                <?php echo get_avatar($child_comment, 32, '', '', array('class' => 'instafeed-avatar')); ?>
                            </div>
                            
                            <div class="instafeed-comment-body">
                                <div class="instafeed-comment-main">
                                    <div class="instafeed-comment-text">
                                        <span class="instafeed-comment-author"><?php echo esc_html($core->get_user_display_name($child_comment->user_id)); ?></span>
                                        <span class="instafeed-comment-content-text"><?php echo $child_comment->comment_content; ?></span>
                                    </div>
                                </div>
                                
                                <div class="instafeed-comment-meta">
                                    <span class="instafeed-comment-time"><?php echo esc_html($core->format_comment_time($child_comment->comment_date)); ?></span>
                                    
                                    <?php if (is_user_logged_in()) : ?>
                                        <div class="instafeed-reaction-buttons">
                                            <button class="instafeed-react-btn" data-comment-id="<?php echo $child_comment->comment_ID; ?>" data-reaction="❤️">❤️</button>
                                            <button class="instafeed-react-btn" data-comment-id="<?php echo $child_comment->comment_ID; ?>" data-reaction="👍">👍</button>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php
}

/**
 * Custom comment end callback
 */
function instafeed_custom_comment_end_callback() {
    echo '</div>';
}
?>
