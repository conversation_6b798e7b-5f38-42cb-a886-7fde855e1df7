<?php
/**
 * InstaFeed Comments Core Class
 * Handles core comment functionality and template overrides
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Comments_Core {
    
    public function __construct() {
        add_filter('comments_template', array($this, 'custom_comments_template'));
        add_filter('comment_form_defaults', array($this, 'custom_comment_form'));
        add_action('wp_head', array($this, 'add_comment_meta'));
        add_filter('comment_text', array($this, 'enhance_comment_text'), 10, 2);
        add_action('comment_post', array($this, 'after_comment_posted'));
    }
    
    public function custom_comments_template($template) {
        // Only override if our plugin is enabled and we're on a single post
        if (get_option('instafeed_comments_enabled') && (is_single() || is_page())) {
            $custom_template = INSTAFEED_COMMENTS_PATH . 'templates/comments.php';
            if (file_exists($custom_template)) {
                return $custom_template;
            }
        }
        return $template;
    }
    
    public function custom_comment_form($defaults) {
        if (!get_option('instafeed_comments_enabled')) {
            return $defaults;
        }
        
        $current_user = wp_get_current_user();
        $user_avatar = get_avatar_url($current_user->ID, array('size' => 40));
        
        $defaults['title_reply'] = '';
        $defaults['title_reply_to'] = __('Reply to %s', 'instafeed-comments');
        $defaults['cancel_reply_link'] = __('Cancel', 'instafeed-comments');
        $defaults['label_submit'] = __('Post', 'instafeed-comments');
        $defaults['submit_button'] = '<button type="submit" class="instafeed-submit-comment">%4$s</button>';
        $defaults['submit_field'] = '<div class="instafeed-comment-form-submit">%1$s %2$s</div>';
        
        $defaults['comment_field'] = '
            <div class="instafeed-comment-form-wrapper">
                <div class="instafeed-comment-form-avatar">
                    <img src="' . esc_url($user_avatar) . '" alt="' . esc_attr($current_user->display_name) . '" class="instafeed-avatar" />
                </div>
                <div class="instafeed-comment-form-input">
                    <textarea id="comment" name="comment" placeholder="' . __('Add a comment...', 'instafeed-comments') . '" rows="1" required></textarea>
                    <div class="instafeed-comment-form-actions">
                        <div class="instafeed-emoji-picker">
                            <button type="button" class="instafeed-emoji-btn">😊</button>
                            <div class="instafeed-emoji-panel">
                                <span class="emoji" data-emoji="❤️">❤️</span>
                                <span class="emoji" data-emoji="😍">😍</span>
                                <span class="emoji" data-emoji="😊">😊</span>
                                <span class="emoji" data-emoji="😂">😂</span>
                                <span class="emoji" data-emoji="🔥">🔥</span>
                                <span class="emoji" data-emoji="👏">👏</span>
                                <span class="emoji" data-emoji="🙌">🙌</span>
                                <span class="emoji" data-emoji="💯">💯</span>
                                <span class="emoji" data-emoji="✨">✨</span>
                                <span class="emoji" data-emoji="🌟">🌟</span>
                                <span class="emoji" data-emoji="💫">💫</span>
                                <span class="emoji" data-emoji="🎉">🎉</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
        
        $defaults['fields'] = array(); // Remove name and email fields for logged-in users
        
        $defaults['must_log_in'] = '
            <div class="instafeed-login-required">
                <p>' . sprintf(__('You must be <a href="%s">logged in</a> to post a comment.', 'instafeed-comments'), wp_login_url(apply_filters('the_permalink', get_permalink()))) . '</p>
            </div>';
        
        return $defaults;
    }
    
    public function add_comment_meta() {
        if (is_single() || is_page()) {
            global $post;
            $comments_count = get_comments_number($post->ID);
            echo '<meta name="instafeed-post-id" content="' . $post->ID . '">';
            echo '<meta name="instafeed-comments-count" content="' . $comments_count . '">';
        }
    }
    
    public function enhance_comment_text($comment_text, $comment) {
        // Add emoji support and hashtag/mention linking
        $comment_text = $this->add_emoji_support($comment_text);
        $comment_text = $this->add_hashtag_links($comment_text);
        $comment_text = $this->add_mention_links($comment_text);
        
        return $comment_text;
    }
    
    private function add_emoji_support($text) {
        // Convert emoji shortcodes to actual emojis
        $emoji_map = array(
            ':heart:' => '❤️',
            ':fire:' => '🔥',
            ':star:' => '⭐',
            ':sparkles:' => '✨',
            ':clap:' => '👏',
            ':thumbsup:' => '👍',
            ':smile:' => '😊',
            ':laugh:' => '😂',
            ':love:' => '😍',
            ':party:' => '🎉'
        );
        
        return str_replace(array_keys($emoji_map), array_values($emoji_map), $text);
    }
    
    private function add_hashtag_links($text) {
        // Convert #hashtags to searchable links
        return preg_replace('/#([a-zA-Z0-9_]+)/', '<a href="' . home_url('/?s=%23$1') . '" class="instafeed-hashtag">#$1</a>', $text);
    }
    
    private function add_mention_links($text) {
        // Convert @mentions to user profile links
        return preg_replace('/@([a-zA-Z0-9_]+)/', '<a href="' . home_url('/author/$1') . '" class="instafeed-mention">@$1</a>', $text);
    }
    
    public function after_comment_posted($comment_id) {
        // Add any post-comment processing here
        do_action('instafeed_comment_posted', $comment_id);
    }
    
    public function get_comment_reactions($comment_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'instafeed_comment_reactions';
        $reactions = $wpdb->get_results($wpdb->prepare(
            "SELECT reaction, COUNT(*) as count 
             FROM $table_name 
             WHERE comment_id = %d 
             GROUP BY reaction",
            $comment_id
        ));
        
        $reaction_counts = array();
        foreach ($reactions as $reaction) {
            $reaction_counts[$reaction->reaction] = $reaction->count;
        }
        
        return $reaction_counts;
    }
    
    public function user_reacted_to_comment($user_id, $comment_id, $reaction) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'instafeed_comment_reactions';
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name 
             WHERE user_id = %d AND comment_id = %d AND reaction = %s",
            $user_id, $comment_id, $reaction
        ));
        
        return !empty($result);
    }
    
    public function format_comment_time($comment_date) {
        $time_diff = human_time_diff(strtotime($comment_date), current_time('timestamp'));
        
        // Instagram-style time formatting
        if (strpos($time_diff, 'min') !== false) {
            $minutes = intval($time_diff);
            return $minutes . 'm';
        } elseif (strpos($time_diff, 'hour') !== false) {
            $hours = intval($time_diff);
            return $hours . 'h';
        } elseif (strpos($time_diff, 'day') !== false) {
            $days = intval($time_diff);
            return $days . 'd';
        } elseif (strpos($time_diff, 'week') !== false) {
            $weeks = intval($time_diff);
            return $weeks . 'w';
        } else {
            return $time_diff;
        }
    }
    
    public function get_user_display_name($user_id) {
        $user = get_userdata($user_id);
        if ($user) {
            // Prefer display name, fallback to username
            return $user->display_name ?: $user->user_login;
        }
        return __('Unknown User', 'instafeed-comments');
    }
}
