<?php
/**
 * InstaFeed Comments Admin Class
 * Handles admin interface and settings
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Comments_Admin {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
    }
    
    public function add_admin_menu() {
        add_options_page(
            __('InstaFeed Comments Settings', 'instafeed-comments'),
            __('InstaFeed Comments', 'instafeed-comments'),
            'manage_options',
            'instafeed-comments-settings',
            array($this, 'settings_page')
        );
    }
    
    public function init_settings() {
        register_setting('instafeed_comments_settings', 'instafeed_comments_enabled');
        register_setting('instafeed_comments_settings', 'instafeed_comments_emoji_reactions');
        register_setting('instafeed_comments_settings', 'instafeed_comments_real_time');
        register_setting('instafeed_comments_settings', 'instafeed_comments_moderation');
        register_setting('instafeed_comments_settings', 'instafeed_comments_max_length');
        register_setting('instafeed_comments_settings', 'instafeed_comments_show_avatars');
        register_setting('instafeed_comments_settings', 'instafeed_comments_show_timestamps');
    }
    
    public function settings_page() {
        if (isset($_POST['submit'])) {
            update_option('instafeed_comments_enabled', isset($_POST['instafeed_comments_enabled']) ? 1 : 0);
            update_option('instafeed_comments_emoji_reactions', isset($_POST['instafeed_comments_emoji_reactions']) ? 1 : 0);
            update_option('instafeed_comments_real_time', isset($_POST['instafeed_comments_real_time']) ? 1 : 0);
            update_option('instafeed_comments_moderation', isset($_POST['instafeed_comments_moderation']) ? 1 : 0);
            update_option('instafeed_comments_max_length', intval($_POST['instafeed_comments_max_length']));
            update_option('instafeed_comments_show_avatars', isset($_POST['instafeed_comments_show_avatars']) ? 1 : 0);
            update_option('instafeed_comments_show_timestamps', isset($_POST['instafeed_comments_show_timestamps']) ? 1 : 0);
            
            echo '<div class="notice notice-success"><p>' . __('Settings saved!', 'instafeed-comments') . '</p></div>';
        }
        
        $enabled = get_option('instafeed_comments_enabled', 1);
        $emoji_reactions = get_option('instafeed_comments_emoji_reactions', 1);
        $real_time = get_option('instafeed_comments_real_time', 1);
        $moderation = get_option('instafeed_comments_moderation', 0);
        $max_length = get_option('instafeed_comments_max_length', 500);
        $show_avatars = get_option('instafeed_comments_show_avatars', 1);
        $show_timestamps = get_option('instafeed_comments_show_timestamps', 1);
        
        ?>
        <div class="wrap">
            <h1><?php _e('InstaFeed Comments Settings', 'instafeed-comments'); ?></h1>
            
            <div class="card">
                <h2><?php _e('Instagram-Style Comment System', 'instafeed-comments'); ?></h2>
                <p><?php _e('Configure your Instagram-style comment system that perfectly matches the InstaFeed theme.', 'instafeed-comments'); ?></p>
                
                <form method="post" action="">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable InstaFeed Comments', 'instafeed-comments'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_comments_enabled" value="1" <?php checked($enabled); ?> />
                                    <?php _e('Replace default WordPress comments with Instagram-style comments', 'instafeed-comments'); ?>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Emoji Reactions', 'instafeed-comments'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_comments_emoji_reactions" value="1" <?php checked($emoji_reactions); ?> />
                                    <?php _e('Enable emoji reactions on comments', 'instafeed-comments'); ?>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Real-time Updates', 'instafeed-comments'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_comments_real_time" value="1" <?php checked($real_time); ?> />
                                    <?php _e('Check for new comments automatically', 'instafeed-comments'); ?>
                                </label>
                                <p class="description"><?php _e('Comments will update in real-time without page refresh', 'instafeed-comments'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Comment Moderation', 'instafeed-comments'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_comments_moderation" value="1" <?php checked($moderation); ?> />
                                    <?php _e('Require approval for new comments', 'instafeed-comments'); ?>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Maximum Comment Length', 'instafeed-comments'); ?></th>
                            <td>
                                <input type="number" name="instafeed_comments_max_length" value="<?php echo $max_length; ?>" min="50" max="2000" />
                                <p class="description"><?php _e('Maximum number of characters allowed in a comment', 'instafeed-comments'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Show User Avatars', 'instafeed-comments'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_comments_show_avatars" value="1" <?php checked($show_avatars); ?> />
                                    <?php _e('Display user avatars in comments', 'instafeed-comments'); ?>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Show Timestamps', 'instafeed-comments'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_comments_show_timestamps" value="1" <?php checked($show_timestamps); ?> />
                                    <?php _e('Show when comments were posted', 'instafeed-comments'); ?>
                                </label>
                            </td>
                        </tr>
                    </table>
                    
                    <?php submit_button(); ?>
                </form>
            </div>
            
            <div class="card">
                <h2><?php _e('Theme Integration', 'instafeed-comments'); ?></h2>
                <p><?php _e('This comment system is specifically designed to match the InstaFeed theme:', 'instafeed-comments'); ?></p>
                <ul>
                    <li>✅ <?php _e('Instagram-style visual design', 'instafeed-comments'); ?></li>
                    <li>✅ <?php _e('Mobile-responsive interface', 'instafeed-comments'); ?></li>
                    <li>✅ <?php _e('Emoji picker and reactions', 'instafeed-comments'); ?></li>
                    <li>✅ <?php _e('Real-time comment updates', 'instafeed-comments'); ?></li>
                    <li>✅ <?php _e('Hashtag and mention support', 'instafeed-comments'); ?></li>
                    <li>✅ <?php _e('Threaded replies', 'instafeed-comments'); ?></li>
                </ul>
            </div>
            
            <div class="card">
                <h2><?php _e('Usage Instructions', 'instafeed-comments'); ?></h2>
                <ol>
                    <li><?php _e('Enable the comment system above', 'instafeed-comments'); ?></li>
                    <li><?php _e('The plugin will automatically replace WordPress comments on posts and pages', 'instafeed-comments'); ?></li>
                    <li><?php _e('Users can comment, reply, and react with emojis', 'instafeed-comments'); ?></li>
                    <li><?php _e('Comments support hashtags (#tag) and mentions (@user)', 'instafeed-comments'); ?></li>
                    <li><?php _e('The design perfectly matches your InstaFeed theme', 'instafeed-comments'); ?></li>
                </ol>
            </div>
        </div>
        <?php
    }
}
