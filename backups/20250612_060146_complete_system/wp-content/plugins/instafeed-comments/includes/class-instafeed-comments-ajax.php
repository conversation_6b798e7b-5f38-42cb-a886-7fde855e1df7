<?php
/**
 * InstaFeed Comments AJAX Class
 * Handles all AJAX requests for the comment system
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Comments_Ajax {
    
    public function __construct() {
        // Comment submission
        add_action('wp_ajax_instafeed_submit_comment', array($this, 'submit_comment'));
        add_action('wp_ajax_nopriv_instafeed_submit_comment', array($this, 'submit_comment'));
        
        // Reply submission
        add_action('wp_ajax_instafeed_submit_reply', array($this, 'submit_reply'));
        add_action('wp_ajax_nopriv_instafeed_submit_reply', array($this, 'submit_reply'));
        
        // Reactions
        add_action('wp_ajax_instafeed_add_reaction', array($this, 'add_reaction'));
        add_action('wp_ajax_nopriv_instafeed_add_reaction', array($this, 'add_reaction'));
        
        // Real-time updates
        add_action('wp_ajax_instafeed_check_new_comments', array($this, 'check_new_comments'));
        add_action('wp_ajax_nopriv_instafeed_check_new_comments', array($this, 'check_new_comments'));
        
        // Load more comments
        add_action('wp_ajax_instafeed_load_more_comments', array($this, 'load_more_comments'));
        add_action('wp_ajax_nopriv_instafeed_load_more_comments', array($this, 'load_more_comments'));
    }
    
    public function submit_comment() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_comments_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to comment');
        }
        
        $post_id = intval($_POST['comment_post_ID']);
        $comment_content = sanitize_textarea_field($_POST['comment']);
        
        if (empty($comment_content)) {
            wp_send_json_error('Comment cannot be empty');
        }
        
        if (strlen($comment_content) > get_option('instafeed_comments_max_length', 500)) {
            wp_send_json_error('Comment is too long');
        }
        
        $comment_data = array(
            'comment_post_ID' => $post_id,
            'comment_content' => $comment_content,
            'comment_type' => 'instafeed_comment',
            'comment_parent' => 0,
            'user_id' => $user_id,
            'comment_author' => wp_get_current_user()->display_name,
            'comment_author_email' => wp_get_current_user()->user_email,
            'comment_author_url' => wp_get_current_user()->user_url,
            'comment_approved' => get_option('instafeed_comments_moderation') ? 0 : 1
        );
        
        $comment_id = wp_insert_comment($comment_data);
        
        if ($comment_id) {
            $comment = get_comment($comment_id);
            $comment_html = $this->render_comment_html($comment);
            
            wp_send_json_success(array(
                'comment_id' => $comment_id,
                'comment_html' => $comment_html,
                'message' => __('Comment posted successfully!', 'instafeed-comments')
            ));
        } else {
            wp_send_json_error('Failed to post comment');
        }
    }
    
    public function submit_reply() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_comments_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to reply');
        }
        
        $post_id = intval($_POST['post_id']);
        $parent_id = intval($_POST['parent_id']);
        $comment_content = sanitize_textarea_field($_POST['comment']);
        
        if (empty($comment_content)) {
            wp_send_json_error('Reply cannot be empty');
        }
        
        $comment_data = array(
            'comment_post_ID' => $post_id,
            'comment_content' => $comment_content,
            'comment_type' => 'instafeed_comment',
            'comment_parent' => $parent_id,
            'user_id' => $user_id,
            'comment_author' => wp_get_current_user()->display_name,
            'comment_author_email' => wp_get_current_user()->user_email,
            'comment_author_url' => wp_get_current_user()->user_url,
            'comment_approved' => get_option('instafeed_comments_moderation') ? 0 : 1
        );
        
        $comment_id = wp_insert_comment($comment_data);
        
        if ($comment_id) {
            $comment = get_comment($comment_id);
            $reply_html = $this->render_reply_html($comment);
            
            wp_send_json_success(array(
                'comment_id' => $comment_id,
                'reply_html' => $reply_html,
                'message' => __('Reply posted successfully!', 'instafeed-comments')
            ));
        } else {
            wp_send_json_error('Failed to post reply');
        }
    }
    
    public function add_reaction() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_comments_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to react');
        }
        
        $comment_id = intval($_POST['comment_id']);
        $reaction = sanitize_text_field($_POST['reaction']);
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'instafeed_comment_reactions';
        
        // Check if user already reacted with this emoji
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE user_id = %d AND comment_id = %d AND reaction = %s",
            $user_id, $comment_id, $reaction
        ));
        
        if ($existing) {
            // Remove reaction
            $wpdb->delete($table_name, array(
                'user_id' => $user_id,
                'comment_id' => $comment_id,
                'reaction' => $reaction
            ));
            $action = 'removed';
        } else {
            // Add reaction
            $wpdb->insert($table_name, array(
                'user_id' => $user_id,
                'comment_id' => $comment_id,
                'reaction' => $reaction,
                'created_at' => current_time('mysql')
            ));
            $action = 'added';
        }
        
        // Get updated reaction counts
        $core = new InstaFeed_Comments_Core();
        $reactions = $core->get_comment_reactions($comment_id);
        
        wp_send_json_success(array(
            'action' => $action,
            'reactions' => $reactions,
            'message' => $action === 'added' ? 'Reaction added!' : 'Reaction removed!'
        ));
    }
    
    public function check_new_comments() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_comments_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $post_id = intval($_POST['post_id']);
        $last_comment_id = sanitize_text_field($_POST['last_comment_id']);
        
        // Extract numeric ID from the format "comment-123"
        $last_id = str_replace('comment-', '', $last_comment_id);
        
        $new_comments = get_comments(array(
            'post_id' => $post_id,
            'status' => 'approve',
            'comment__not_in' => array($last_id),
            'date_query' => array(
                array(
                    'after' => '5 minutes ago'
                )
            ),
            'orderby' => 'comment_date',
            'order' => 'DESC'
        ));
        
        $new_comments_html = array();
        foreach ($new_comments as $comment) {
            $new_comments_html[] = $this->render_comment_html($comment);
        }
        
        wp_send_json_success(array(
            'new_comments' => $new_comments_html,
            'count' => count($new_comments_html)
        ));
    }
    
    public function load_more_comments() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_comments_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $post_id = intval($_POST['post_id']);
        $page = intval($_POST['page']) ?: 1;
        $per_page = 10;
        
        $comments = get_comments(array(
            'post_id' => $post_id,
            'status' => 'approve',
            'parent' => 0,
            'number' => $per_page,
            'offset' => ($page - 1) * $per_page,
            'orderby' => 'comment_date',
            'order' => 'ASC'
        ));
        
        $comments_html = array();
        foreach ($comments as $comment) {
            $comments_html[] = $this->render_comment_html($comment);
        }
        
        wp_send_json_success(array(
            'comments' => $comments_html,
            'has_more' => count($comments) === $per_page
        ));
    }
    
    private function render_comment_html($comment) {
        $core = new InstaFeed_Comments_Core();
        $user_display_name = $core->get_user_display_name($comment->user_id);
        $comment_time = $core->format_comment_time($comment->comment_date);
        $reactions = $core->get_comment_reactions($comment->comment_ID);
        
        ob_start();
        ?>
        <div class="instafeed-comment" id="comment-<?php echo $comment->comment_ID; ?>">
            <div class="instafeed-comment-content">
                <div class="instafeed-comment-avatar">
                    <?php echo get_avatar($comment, 40, '', '', array('class' => 'instafeed-avatar')); ?>
                </div>
                
                <div class="instafeed-comment-body">
                    <div class="instafeed-comment-main">
                        <div class="instafeed-comment-text">
                            <span class="instafeed-comment-author"><?php echo esc_html($user_display_name); ?></span>
                            <span class="instafeed-comment-content-text"><?php echo $comment->comment_content; ?></span>
                        </div>
                    </div>
                    
                    <div class="instafeed-comment-meta">
                        <span class="instafeed-comment-time"><?php echo esc_html($comment_time); ?></span>
                        
                        <?php if (is_user_logged_in()) : ?>
                            <button class="instafeed-reply-btn" data-comment-id="<?php echo $comment->comment_ID; ?>">
                                <?php _e('Reply', 'instafeed-comments'); ?>
                            </button>
                            
                            <div class="instafeed-reaction-buttons">
                                <button class="instafeed-react-btn" data-comment-id="<?php echo $comment->comment_ID; ?>" data-reaction="❤️">❤️</button>
                                <button class="instafeed-react-btn" data-comment-id="<?php echo $comment->comment_ID; ?>" data-reaction="👍">👍</button>
                                <button class="instafeed-react-btn" data-comment-id="<?php echo $comment->comment_ID; ?>" data-reaction="😂">😂</button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    private function render_reply_html($comment) {
        $core = new InstaFeed_Comments_Core();
        $user_display_name = $core->get_user_display_name($comment->user_id);
        $comment_time = $core->format_comment_time($comment->comment_date);
        
        ob_start();
        ?>
        <div class="instafeed-comment instafeed-comment-reply" id="comment-<?php echo $comment->comment_ID; ?>">
            <div class="instafeed-comment-content">
                <div class="instafeed-comment-avatar">
                    <?php echo get_avatar($comment, 32, '', '', array('class' => 'instafeed-avatar')); ?>
                </div>
                
                <div class="instafeed-comment-body">
                    <div class="instafeed-comment-main">
                        <div class="instafeed-comment-text">
                            <span class="instafeed-comment-author"><?php echo esc_html($user_display_name); ?></span>
                            <span class="instafeed-comment-content-text"><?php echo $comment->comment_content; ?></span>
                        </div>
                    </div>
                    
                    <div class="instafeed-comment-meta">
                        <span class="instafeed-comment-time"><?php echo esc_html($comment_time); ?></span>
                        
                        <?php if (is_user_logged_in()) : ?>
                            <div class="instafeed-reaction-buttons">
                                <button class="instafeed-react-btn" data-comment-id="<?php echo $comment->comment_ID; ?>" data-reaction="❤️">❤️</button>
                                <button class="instafeed-react-btn" data-comment-id="<?php echo $comment->comment_ID; ?>" data-reaction="👍">👍</button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
}
