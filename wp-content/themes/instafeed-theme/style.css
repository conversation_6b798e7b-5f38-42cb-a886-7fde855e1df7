/*
Theme Name: InstaFeed Theme
Description: A modern Instagram-style WordPress theme with responsive grid layout, lightbox functionality, and mobile optimization. Perfect for showcasing images in a clean, minimal design.
Author: Custom Development
Version: 1.0
Requires at least: 5.0
Tested up to: 6.7
Requires PHP: 7.4
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: instafeed-theme
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: #262626;
    background-color: #fafafa;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.site-header {
    background: #fff;
    border-bottom: 1px solid #dbdbdb;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.site-title {
    font-size: 24px;
    font-weight: 400;
    color: #262626;
    text-decoration: none;
    font-family: 'Billabong', cursive;
}

.site-title:hover {
    color: #262626;
    text-decoration: none;
}

/* Main Content */
.site-main {
    margin-top: 80px;
    padding: 20px 0;
}

/* Instagram Grid */
.instagram-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Post Item */
.post-item {
    background: #fff;
    border: 1px solid #dbdbdb;
    border-radius: 8px;
    overflow: visible;
    transition: transform 0.2s ease;
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 300px;
}

.post-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Post Image */
.post-image {
    position: relative;
    width: 100%;
    aspect-ratio: 4/3;
    overflow: hidden;
    cursor: pointer;
    flex-shrink: 0;
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.post-image:hover img {
    transform: scale(1.05);
}

/* Post Content */
.post-content {
    padding: 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 120px;
}

.post-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.3;
    color: #262626;
    display: block;
    visibility: visible;
}

.post-title a {
    color: #262626;
    text-decoration: none;
    display: block;
    font-weight: 600;
}

.post-title a:hover {
    color: #0095f6;
}

.post-excerpt {
    font-size: 14px;
    color: #8e8e8e;
    line-height: 1.4;
    margin-bottom: 10px;
}

.post-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    color: #8e8e8e;
}

.post-date {
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .instagram-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
        padding: 0 15px;
    }
    
    .container {
        padding: 0 15px;
    }
    
    .header-content {
        padding: 0 15px;
    }
    
    .site-main {
        margin-top: 70px;
        padding: 15px 0;
    }
}

@media (max-width: 480px) {
    .instagram-grid {
        grid-template-columns: 1fr;
        gap: 10px;
        padding: 0 10px;
    }
    
    .site-title {
        font-size: 20px;
    }
    
    .post-content {
        padding: 12px;
    }
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
}

.loading::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0095f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-20 {
    margin-bottom: 20px;
}

.hidden {
    display: none;
}

/* No Posts Message */
.no-posts {
    text-align: center;
    padding: 60px 20px;
    color: #8e8e8e;
}

.no-posts h2 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #262626;
}

/* Footer */
.site-footer {
    background: #fff;
    border-top: 1px solid #dbdbdb;
    padding: 40px 0;
    margin-top: 60px;
    text-align: center;
    color: #8e8e8e;
    font-size: 14px;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.post-item {
    opacity: 0;
    animation: fadeInUp 0.6s ease forwards;
}

.animate-in {
    animation-play-state: running;
}

/* Image hover effects */
.post-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 149, 246, 0.1), rgba(255, 0, 132, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.post-item:hover .post-image::before {
    opacity: 1;
}

/* Improved mobile navigation */
@media (max-width: 768px) {
    .site-header {
        height: 50px;
    }

    .header-content {
        height: 50px;
    }

    .site-main {
        margin-top: 60px;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #000;
        color: #fff;
    }

    .site-header {
        background: #000;
        border-bottom-color: #262626;
    }

    .post-item {
        background: #262626;
        border-color: #363636;
    }

    .site-footer {
        background: #000;
        border-top-color: #262626;
    }
}
