# InstaFeed WordPress Theme

A modern, Instagram-style WordPress theme featuring a responsive grid layout, lightbox functionality, and mobile optimization.

## Features

- **Instagram-Style Grid Layout**: Clean, responsive grid showcasing featured images
- **Lightbox Image Viewing**: Click any image to view in a beautiful lightbox with navigation
- **Mobile Responsive**: Optimized for all device sizes
- **Infinite Scroll**: Load more posts automatically as you scroll
- **Fast Performance**: Lightweight and optimized for speed
- **Clean Design**: Minimal, modern aesthetic inspired by Instagram
- **Custom Post Display**: Only shows posts with featured images for a consistent look

## Installation

1. Upload the `instafeed-theme` folder to your `/wp-content/themes/` directory
2. Activate the theme in WordPress Admin → Appearance → Themes
3. Go to Tools → InstaFeed Setup to generate sample content (optional)

## Theme Setup

### Quick Start with Sample Content

1. Go to **WordPress Admin → Tools → InstaFeed Setup**
2. Click "Create Sample Content" to generate 50 sample posts with placeholder images
3. Your theme will immediately showcase the Instagram-style grid layout

### Manual Setup

1. Create posts with featured images
2. The theme automatically displays only posts that have featured images
3. Recommended image size: 800x800px (square format works best)

## Customization

### Theme Customizer Options

Go to **Appearance → Customize → InstaFeed Settings** to adjust:

- Grid columns (2-5 columns)
- Color schemes
- Typography options

### CSS Customization

The theme includes several CSS custom properties for easy customization:

```css
:root {
    --primary-color: #0095f6;
    --text-color: #262626;
    --background-color: #fafafa;
    --border-color: #dbdbdb;
}
```

## File Structure

```
instafeed-theme/
├── style.css              # Main stylesheet with theme header
├── index.php              # Main template file
├── single.php             # Single post template
├── functions.php          # Theme functions and features
├── sample-content.php     # Sample content generator
├── assets/
│   ├── css/
│   │   └── lightbox.css   # Lightbox styles
│   ├── js/
│   │   └── instafeed.js   # Theme JavaScript
│   └── images/            # Theme images
└── README.md              # This file
```

## JavaScript Features

### Lightbox Functionality

- Click any image to open in lightbox
- Navigate with arrow keys or buttons
- Close with Escape key or clicking outside
- Responsive design for mobile devices

### Infinite Scroll

- Automatically loads more posts when scrolling near bottom
- Smooth animations for new content
- Load more button as fallback

### AJAX Loading

- Posts load without page refresh
- Loading indicators for better UX
- Error handling for failed requests

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Features

- Optimized CSS and JavaScript
- Lazy loading for images
- Minimal HTTP requests
- Compressed assets
- Mobile-first responsive design

## WordPress Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

## Recommended Plugins

While the theme works standalone, these plugins enhance functionality:

- **Jetpack**: For additional image optimization
- **Yoast SEO**: For better search engine optimization
- **WP Rocket**: For caching and performance
- **Smush**: For image compression

## Development

### Local Development Setup

1. Clone or download the theme
2. Install in WordPress development environment
3. Use browser dev tools for CSS/JS debugging
4. Test on multiple devices and screen sizes

### Customization Guidelines

- Use child themes for major modifications
- Follow WordPress coding standards
- Test changes on mobile devices
- Optimize images for web (WebP format recommended)

## Troubleshooting

### Common Issues

**Images not displaying in grid:**
- Ensure posts have featured images set
- Check image file permissions
- Verify image URLs are accessible

**Lightbox not working:**
- Check JavaScript console for errors
- Ensure jQuery is loaded
- Verify theme scripts are enqueued

**Mobile layout issues:**
- Clear browser cache
- Test in device emulator
- Check CSS media queries

### Support

For theme support and customization help:
1. Check WordPress admin for error messages
2. Review browser console for JavaScript errors
3. Ensure all theme files are properly uploaded
4. Test with default WordPress content

## License

This theme is licensed under the GPL v2 or later.

## Credits

- Inspired by Instagram's clean, grid-based design
- Uses modern CSS Grid and Flexbox layouts
- Placeholder images from Lorem Picsum
- Icons and fonts from Google Fonts

## Changelog

### Version 1.0
- Initial release
- Instagram-style grid layout
- Lightbox functionality
- Mobile responsive design
- Infinite scroll loading
- Sample content generator
