<?php
/**
 * InstaFeed Theme Functions
 * 
 * @package InstaFeed_Theme
 * @version 1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function instafeed_theme_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Add support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add support for editor styles
    add_theme_support('editor-styles');
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'instafeed-theme'),
        'footer' => __('Footer Menu', 'instafeed-theme'),
    ));
    
    // Add custom image sizes
    add_image_size('instafeed-square', 400, 400, true);
    add_image_size('instafeed-large', 800, 800, true);
}
add_action('after_setup_theme', 'instafeed_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function instafeed_theme_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style(
        'instafeed-style',
        get_stylesheet_uri(),
        array(),
        wp_get_theme()->get('Version')
    );
    
    // Enqueue custom JavaScript
    wp_enqueue_script(
        'instafeed-script',
        get_template_directory_uri() . '/assets/js/instafeed.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );
    
    // Localize script for AJAX
    wp_localize_script('instafeed-script', 'instafeed_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('instafeed_nonce'),
        'loading_text' => __('Loading...', 'instafeed-theme'),
        'no_more_posts' => __('No more posts to load', 'instafeed-theme'),
    ));
    
    // Enqueue lightbox styles
    wp_enqueue_style(
        'instafeed-lightbox',
        get_template_directory_uri() . '/assets/css/lightbox.css',
        array('instafeed-style'),
        wp_get_theme()->get('Version')
    );
}
add_action('wp_enqueue_scripts', 'instafeed_theme_scripts');

/**
 * Custom Post Query for Instagram-style feed
 */
function instafeed_modify_main_query($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_home()) {
            $query->set('posts_per_page', 12);
            $query->set('meta_query', array(
                array(
                    'key' => '_thumbnail_id',
                    'compare' => 'EXISTS'
                )
            ));
        }
    }
}
add_action('pre_get_posts', 'instafeed_modify_main_query');

/**
 * AJAX Load More Posts
 */
function instafeed_load_more_posts() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'instafeed_nonce')) {
        wp_die('Security check failed');
    }
    
    $page = intval($_POST['page']);
    $posts_per_page = 12;
    
    $args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged' => $page,
        'meta_query' => array(
            array(
                'key' => '_thumbnail_id',
                'compare' => 'EXISTS'
            )
        )
    );
    
    $query = new WP_Query($args);
    
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            ?>
            <article class="post-item" data-post-id="<?php the_ID(); ?>">
                <?php if (has_post_thumbnail()) : ?>
                    <div class="post-image" onclick="openLightbox(<?php the_ID(); ?>)">
                        <?php the_post_thumbnail('large', array('alt' => get_the_title())); ?>
                        <div class="image-overlay">
                            <span class="view-icon">👁</span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="post-content">
                    <h2 class="post-title">
                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                    </h2>
                    
                    <div class="post-excerpt">
                        <?php echo wp_trim_words(get_the_excerpt() ?: get_the_content(), 15, '...'); ?>
                    </div>
                    
                    <div class="post-meta">
                        <span class="post-date"><?php echo get_the_date('M j, Y'); ?></span>
                        <span class="post-comments">
                            <?php comments_number('0', '1', '%'); ?> comments
                        </span>
                    </div>
                </div>
            </article>
            <?php
        }
        wp_reset_postdata();
    }
    
    wp_die();
}
add_action('wp_ajax_instafeed_load_more', 'instafeed_load_more_posts');
add_action('wp_ajax_nopriv_instafeed_load_more', 'instafeed_load_more_posts');

/**
 * Custom Excerpt Length
 */
function instafeed_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'instafeed_excerpt_length');

/**
 * Custom Excerpt More
 */
function instafeed_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'instafeed_excerpt_more');

/**
 * Add Custom Body Classes
 */
function instafeed_body_classes($classes) {
    $classes[] = 'instafeed-theme';
    
    if (wp_is_mobile()) {
        $classes[] = 'mobile-device';
    }
    
    return $classes;
}
add_filter('body_class', 'instafeed_body_classes');

/**
 * Customize Login Page
 */
function instafeed_login_logo() {
    ?>
    <style type="text/css">
        #login h1 a, .login h1 a {
            background-image: none;
            background-size: contain;
            width: auto;
            height: auto;
            text-indent: 0;
            font-family: 'Billabong', cursive;
            font-size: 48px;
            color: #262626;
            text-decoration: none;
        }
        #login h1 a:before, .login h1 a:before {
            content: "<?php bloginfo('name'); ?>";
        }
    </style>
    <?php
}
add_action('login_enqueue_scripts', 'instafeed_login_logo');

/**
 * Remove unnecessary WordPress features for cleaner Instagram-like experience
 */
function instafeed_remove_wp_features() {
    // Remove WordPress version from head
    remove_action('wp_head', 'wp_generator');
    
    // Remove RSD link
    remove_action('wp_head', 'rsd_link');
    
    // Remove Windows Live Writer link
    remove_action('wp_head', 'wlwmanifest_link');
    
    // Remove shortlink
    remove_action('wp_head', 'wp_shortlink_wp_head');
}
add_action('init', 'instafeed_remove_wp_features');

/**
 * Add theme customizer options
 */
function instafeed_customize_register($wp_customize) {
    // Add Instagram-style settings section
    $wp_customize->add_section('instafeed_settings', array(
        'title' => __('InstaFeed Settings', 'instafeed-theme'),
        'priority' => 30,
    ));
    
    // Grid columns setting
    $wp_customize->add_setting('instafeed_grid_columns', array(
        'default' => 3,
        'sanitize_callback' => 'absint',
    ));
    
    $wp_customize->add_control('instafeed_grid_columns', array(
        'label' => __('Grid Columns', 'instafeed-theme'),
        'section' => 'instafeed_settings',
        'type' => 'select',
        'choices' => array(
            2 => '2 Columns',
            3 => '3 Columns',
            4 => '4 Columns',
            5 => '5 Columns',
        ),
    ));
}
add_action('customize_register', 'instafeed_customize_register');

/**
 * Get theme option
 */
function instafeed_get_option($option, $default = '') {
    return get_theme_mod($option, $default);
}

/**
 * Handle placeholder images for demo content
 */
function instafeed_placeholder_image($html, $post_id, $post_thumbnail_id, $size, $attr) {
    if (strpos($post_thumbnail_id, 'placeholder_') === 0) {
        $placeholder_url = get_post_meta($post_id, '_placeholder_image_url', true);
        if ($placeholder_url) {
            $alt = get_the_title($post_id);
            return '<img src="' . esc_url($placeholder_url) . '" alt="' . esc_attr($alt) . '" class="wp-post-image" />';
        }
    }
    return $html;
}
add_filter('post_thumbnail_html', 'instafeed_placeholder_image', 10, 5);

/**
 * Check if post has thumbnail (including placeholders)
 */
function instafeed_has_post_thumbnail($has_thumbnail, $post) {
    if (!$has_thumbnail) {
        $placeholder_url = get_post_meta($post->ID, '_placeholder_image_url', true);
        return !empty($placeholder_url);
    }
    return $has_thumbnail;
}
add_filter('has_post_thumbnail', 'instafeed_has_post_thumbnail', 10, 2);

/**
 * Add admin notice for theme setup
 */
function instafeed_admin_notice() {
    if (current_user_can('manage_options')) {
        $sample_posts = get_posts(array(
            'meta_key' => '_instafeed_sample',
            'meta_value' => 'yes',
            'numberposts' => 1
        ));

        if (empty($sample_posts)) {
            ?>
            <div class="notice notice-info is-dismissible">
                <p><strong>InstaFeed Theme:</strong> Want to see the theme in action?
                <a href="<?php echo admin_url('tools.php?page=instafeed-setup'); ?>">Generate sample content</a>
                or upload some images and create posts with featured images!</p>
            </div>
            <?php
        }
    }
}
add_action('admin_notices', 'instafeed_admin_notice');

/**
 * Auto-activate InstaFeed plugins
 */
function activate_instafeed_plugins() {
    $plugins_to_activate = array(
        'instafeed-admin-dashboard/instafeed-admin-dashboard.php', // Activate dashboard first
        'instafeed-profile/instafeed-profile.php',
        'instafeed-creator/instafeed-creator.php',
        'instafeed-interactions/instafeed-interactions.php'
    );

    foreach ($plugins_to_activate as $plugin) {
        if (!is_plugin_active($plugin) && file_exists(WP_PLUGIN_DIR . '/' . $plugin)) {
            $result = activate_plugin($plugin);
            if (!is_wp_error($result)) {
                error_log("InstaFeed: Activated plugin $plugin");
            }
        }
    }
}

// Run on theme activation and admin init
add_action('after_switch_theme', 'activate_instafeed_plugins');
add_action('admin_init', 'activate_instafeed_plugins');

/**
 * AJAX handler for loading more posts
 */
function load_more_posts_ajax() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'load_more_posts')) {
        wp_send_json_error('Security check failed');
    }

    $page = intval($_POST['page']);
    $posts_per_page = 6; // Match the initial posts per page

    $args = array(
        'post_type' => 'post',
        'post_status' => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged' => $page,
        'meta_query' => array(
            array(
                'key' => '_thumbnail_id',
                'compare' => 'EXISTS'
            )
        )
    );

    $query = new WP_Query($args);
    $posts_data = array();

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();

            $thumbnail_url = get_the_post_thumbnail_url(get_the_ID(), 'large');
            if (!$thumbnail_url) {
                $thumbnail_url = 'https://via.placeholder.com/600x600?text=No+Image';
            }

            $posts_data[] = array(
                'id' => get_the_ID(),
                'title' => get_the_title(),
                'url' => get_permalink(),
                'image' => $thumbnail_url,
                'excerpt' => get_the_excerpt(),
                'date' => get_the_date(),
                'author' => get_the_author()
            );
        }
        wp_reset_postdata();
    }

    if (!empty($posts_data)) {
        wp_send_json_success(array(
            'posts' => $posts_data,
            'has_more' => $page < $query->max_num_pages
        ));
    } else {
        wp_send_json_error('No more posts found');
    }
}

add_action('wp_ajax_load_more_posts', 'load_more_posts_ajax');
add_action('wp_ajax_nopriv_load_more_posts', 'load_more_posts_ajax');

/**
 * Add theme setup page
 */
function instafeed_add_setup_page() {
    add_management_page(
        'InstaFeed Setup',
        'InstaFeed Setup',
        'manage_options',
        'instafeed-setup',
        'instafeed_setup_page'
    );
}
add_action('admin_menu', 'instafeed_add_setup_page');

/**
 * Theme setup page
 */
function instafeed_setup_page() {
    if (isset($_POST['create_sample_content'])) {
        require_once(get_template_directory() . '/sample-content.php');
        $created = create_instafeed_sample_content();
        echo '<div class="notice notice-success"><p>Created ' . $created . ' sample posts!</p></div>';
    }

    if (isset($_POST['delete_sample_content'])) {
        require_once(get_template_directory() . '/sample-content.php');
        $deleted = delete_instafeed_sample_content();
        echo '<div class="notice notice-success"><p>Deleted ' . $deleted . ' sample posts!</p></div>';
    }

    // Check if Ramom plugin is active
    $ramom_active = is_plugin_active('ramom/ramom.php');

    ?>
    <div class="wrap">
        <h1>InstaFeed Theme Setup</h1>

        <?php if (!$ramom_active) : ?>
        <div class="notice notice-info">
            <p><strong>Enhance your experience!</strong> Activate the <strong>Ramom plugin</strong> to enable user interactions like likes, follows, and user-generated content.</p>
        </div>
        <?php endif; ?>

        <div class="card">
            <h2>Sample Content</h2>
            <p>Generate sample posts with placeholder images to see how your InstaFeed theme looks.</p>

            <form method="post">
                <p>
                    <input type="submit" name="create_sample_content" class="button-primary" value="Create Sample Content (50 posts)" />
                    <input type="submit" name="delete_sample_content" class="button-secondary" value="Delete Sample Content" />
                </p>
            </form>
        </div>

        <div class="card">
            <h2>Theme Features</h2>
            <ul>
                <li>✅ Instagram-style grid layout</li>
                <li>✅ Responsive design for all devices</li>
                <li>✅ Lightbox image viewing</li>
                <li>✅ Infinite scroll loading</li>
                <li>✅ Mobile-optimized interface</li>
                <li>✅ Clean, minimal design</li>
                <?php if ($ramom_active) : ?>
                <li>✅ User interactions (likes, follows)</li>
                <li>✅ User-generated content</li>
                <li>✅ Random post generation</li>
                <li>✅ Social features</li>
                <?php endif; ?>
            </ul>
        </div>

        <?php if ($ramom_active) : ?>
        <div class="card">
            <h2>Ramom Plugin Integration</h2>
            <p>The Ramom plugin is active and integrated with your theme! Users can now:</p>
            <ul>
                <li>Like and unlike posts</li>
                <li>Follow and unfollow other users</li>
                <li>Create their own posts</li>
                <li>Share posts on social media</li>
            </ul>
            <p><a href="<?php echo admin_url('admin.php?page=ramom-settings'); ?>" class="button">Configure Ramom Settings</a></p>
        </div>
        <?php endif; ?>
    </div>
    <?php
}
