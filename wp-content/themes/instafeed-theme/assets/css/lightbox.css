/* Lightbox Modal Styles */
.lightbox-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.lightbox-modal:not(.hidden) {
    opacity: 1;
    visibility: visible;
}

.lightbox-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.lightbox-modal:not(.hidden) .lightbox-content {
    transform: scale(1);
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 24px;
    cursor: pointer;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.lightbox-close:hover {
    background: rgba(0, 0, 0, 0.9);
}

.lightbox-image-container {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
    min-height: 400px;
}

.lightbox-image-container img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 8px 8px 0 0;
}

.lightbox-info {
    padding: 20px;
    background: #fff;
    border-top: 1px solid #dbdbdb;
}

.lightbox-info h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #262626;
    line-height: 1.3;
}

.lightbox-info p {
    font-size: 14px;
    color: #8e8e8e;
    line-height: 1.4;
    margin-bottom: 15px;
}

.lightbox-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    color: #8e8e8e;
}

.lightbox-meta a {
    color: #0095f6;
    text-decoration: none;
    font-weight: 500;
}

.lightbox-meta a:hover {
    text-decoration: underline;
}

.lightbox-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
}

.lightbox-navigation button {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    pointer-events: all;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0.8;
}

.lightbox-navigation button:hover {
    background: rgba(0, 0, 0, 0.9);
    opacity: 1;
    transform: scale(1.1);
}

.lightbox-navigation button:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

/* Image Overlay for Grid Items */
.post-image {
    position: relative;
    overflow: hidden;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.post-image:hover .image-overlay {
    opacity: 1;
}

.view-icon {
    font-size: 24px;
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Placeholder for posts without images */
.no-image {
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1;
}

.placeholder-image {
    text-align: center;
    color: #8e8e8e;
}

.placeholder-image span {
    font-size: 48px;
    display: block;
    margin-bottom: 10px;
}

/* Load More Button */
.load-more-container {
    margin-top: 40px;
    padding: 20px;
}

.load-more-btn {
    background: #0095f6;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.load-more-btn:hover {
    background: #1877f2;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 149, 246, 0.3);
}

.load-more-btn:disabled {
    background: #dbdbdb;
    color: #8e8e8e;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Responsive Lightbox */
@media (max-width: 768px) {
    .lightbox-content {
        max-width: 95vw;
        max-height: 95vh;
        margin: 20px;
    }
    
    .lightbox-image-container {
        min-height: 300px;
    }
    
    .lightbox-image-container img {
        max-height: 60vh;
    }
    
    .lightbox-info {
        padding: 15px;
    }
    
    .lightbox-info h3 {
        font-size: 16px;
    }
    
    .lightbox-navigation {
        padding: 0 10px;
    }
    
    .lightbox-navigation button {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .lightbox-close {
        width: 35px;
        height: 35px;
        font-size: 20px;
        top: 10px;
        right: 10px;
    }
}

@media (max-width: 480px) {
    .lightbox-content {
        max-width: 100vw;
        max-height: 100vh;
        margin: 0;
        border-radius: 0;
    }
    
    .lightbox-image-container img {
        border-radius: 0;
    }
    
    .view-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
}

/* Animation for smooth transitions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.lightbox-modal:not(.hidden) {
    animation: fadeIn 0.3s ease;
}
