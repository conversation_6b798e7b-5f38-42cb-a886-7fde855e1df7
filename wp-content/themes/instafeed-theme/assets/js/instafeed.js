/**
 * InstaFeed Theme JavaScript
 * Handles lightbox, infinite scroll, and AJAX functionality
 */

(function($) {
    'use strict';

    let currentPage = 1;
    let isLoading = false;
    let hasMorePosts = true;
    let posts = [];
    let currentPostIndex = 0;

    // Initialize when document is ready
    $(document).ready(function() {
        initializeInstaFeed();
    });

    /**
     * Initialize all InstaFeed functionality
     */
    function initializeInstaFeed() {
        initializePosts();
        bindEvents();
        setupInfiniteScroll();
        setupKeyboardNavigation();
    }

    /**
     * Initialize posts array for lightbox navigation
     */
    function initializePosts() {
        posts = [];
        $('.post-item').each(function(index) {
            const $item = $(this);
            const $img = $item.find('.post-image img');
            const $title = $item.find('.post-title a');
            const $excerpt = $item.find('.post-excerpt');
            const $date = $item.find('.post-date');

            posts.push({
                id: $item.data('post-id'),
                image: $img.attr('src') || '',
                title: $title.text() || '',
                excerpt: $excerpt.text() || '',
                date: $date.text() || '',
                link: $title.attr('href') || '',
                element: $item[0]
            });
        });
    }

    /**
     * Bind event handlers
     */
    function bindEvents() {
        // Load more button
        $('#load-more-btn').on('click', loadMorePosts);

        // Lightbox events
        $(document).on('click', '.post-image', function() {
            const postId = $(this).closest('.post-item').data('post-id');
            openLightbox(postId);
        });

        $('.lightbox-overlay, .lightbox-close').on('click', closeLightbox);
        $('#prev-btn').on('click', function() { navigateLightbox('prev'); });
        $('#next-btn').on('click', function() { navigateLightbox('next'); });

        // Prevent lightbox content clicks from closing modal
        $('.lightbox-content').on('click', function(e) {
            e.stopPropagation();
        });

        // Handle window resize
        $(window).on('resize', debounce(handleResize, 250));
    }

    /**
     * Setup infinite scroll functionality
     */
    function setupInfiniteScroll() {
        $(window).on('scroll', debounce(function() {
            if (!isLoading && hasMorePosts) {
                const scrollTop = $(window).scrollTop();
                const windowHeight = $(window).height();
                const documentHeight = $(document).height();
                
                // Load more when user is 200px from bottom
                if (scrollTop + windowHeight >= documentHeight - 200) {
                    loadMorePosts();
                }
            }
        }, 100));
    }

    /**
     * Setup keyboard navigation
     */
    function setupKeyboardNavigation() {
        $(document).on('keydown', function(e) {
            if (!$('#lightbox-modal').hasClass('hidden')) {
                switch(e.key) {
                    case 'Escape':
                        closeLightbox();
                        break;
                    case 'ArrowLeft':
                        navigateLightbox('prev');
                        break;
                    case 'ArrowRight':
                        navigateLightbox('next');
                        break;
                }
            }
        });
    }

    /**
     * Load more posts via AJAX
     */
    function loadMorePosts() {
        if (isLoading || !hasMorePosts) return;

        isLoading = true;
        currentPage++;

        const $loadBtn = $('#load-more-btn');
        const $loading = $('#loading');

        $loadBtn.prop('disabled', true).text(instafeed_ajax.loading_text);
        $loading.removeClass('hidden');

        $.ajax({
            url: instafeed_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_load_more',
                page: currentPage,
                nonce: instafeed_ajax.nonce
            },
            success: function(response) {
                if (response.trim()) {
                    const $newPosts = $(response);
                    $('#instagram-grid').append($newPosts);
                    
                    // Animate new posts
                    $newPosts.css('opacity', 0).animate({opacity: 1}, 500);
                    
                    // Update posts array
                    initializePosts();
                } else {
                    hasMorePosts = false;
                    $loadBtn.text(instafeed_ajax.no_more_posts);
                }
            },
            error: function() {
                console.error('Failed to load more posts');
                $loadBtn.text('Error loading posts');
            },
            complete: function() {
                isLoading = false;
                $loading.addClass('hidden');
                
                if (hasMorePosts) {
                    $loadBtn.prop('disabled', false).text('Load More Posts');
                }
            }
        });
    }

    /**
     * Open lightbox with specific post
     */
    function openLightbox(postId) {
        currentPostIndex = posts.findIndex(post => post.id == postId);
        
        if (currentPostIndex !== -1) {
            showLightboxContent(currentPostIndex);
            $('#lightbox-modal').removeClass('hidden');
            $('body').addClass('lightbox-open').css('overflow', 'hidden');
            
            // Update navigation buttons
            updateNavigationButtons();
        }
    }

    /**
     * Close lightbox
     */
    function closeLightbox() {
        $('#lightbox-modal').addClass('hidden');
        $('body').removeClass('lightbox-open').css('overflow', 'auto');
    }

    /**
     * Show lightbox content for specific post index
     */
    function showLightboxContent(index) {
        const post = posts[index];
        
        $('#lightbox-image').attr('src', post.image).attr('alt', post.title);
        $('#lightbox-title').text(post.title);
        $('#lightbox-excerpt').text(post.excerpt);
        $('#lightbox-date').text(post.date);
        $('#lightbox-link').attr('href', post.link);
        
        // Scroll to current post in grid (optional visual feedback)
        if (post.element) {
            post.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    /**
     * Navigate lightbox (prev/next)
     */
    function navigateLightbox(direction) {
        if (direction === 'next') {
            currentPostIndex = (currentPostIndex + 1) % posts.length;
        } else {
            currentPostIndex = (currentPostIndex - 1 + posts.length) % posts.length;
        }
        
        showLightboxContent(currentPostIndex);
        updateNavigationButtons();
    }

    /**
     * Update navigation button states
     */
    function updateNavigationButtons() {
        const $prevBtn = $('#prev-btn');
        const $nextBtn = $('#next-btn');
        
        // Enable/disable buttons based on position
        $prevBtn.prop('disabled', posts.length <= 1);
        $nextBtn.prop('disabled', posts.length <= 1);
        
        // Hide buttons if only one post
        if (posts.length <= 1) {
            $('.lightbox-navigation').hide();
        } else {
            $('.lightbox-navigation').show();
        }
    }

    /**
     * Handle window resize
     */
    function handleResize() {
        // Reinitialize grid if needed
        // Could add masonry-like functionality here
    }

    /**
     * Debounce function to limit function calls
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = function() {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Lazy loading for images (optional enhancement)
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Add smooth animations to grid items
     */
    function animateGridItems() {
        $('.post-item').each(function(index) {
            $(this).css({
                'animation-delay': (index * 0.1) + 's'
            }).addClass('animate-in');
        });
    }

    // Make functions globally available for inline onclick handlers
    window.openLightbox = openLightbox;
    window.closeLightbox = closeLightbox;
    window.navigateLightbox = navigateLightbox;

    // Initialize animations on load
    $(window).on('load', function() {
        animateGridItems();
    });

})(jQuery);
