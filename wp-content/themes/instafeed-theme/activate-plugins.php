<?php
/**
 * Plugin Activation Helper
 * This file helps activate InstaFeed plugins
 */

// Ensure WordPress is loaded
if (!defined('ABSPATH')) {
    // Try to load WordPress
    $wp_load_paths = array(
        '../../../wp-load.php',
        '../../wp-load.php',
        '../wp-load.php',
        'wp-load.php'
    );
    
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once($path);
            break;
        }
    }
}

if (!defined('ABSPATH')) {
    die('WordPress not found');
}

// Activate InstaFeed plugins
$plugins_to_activate = array(
    'instafeed-profile/instafeed-profile.php',
    'instafeed-creator/instafeed-creator.php',
    'instafeed-interactions/instafeed-interactions.php'
);

foreach ($plugins_to_activate as $plugin) {
    if (!is_plugin_active($plugin)) {
        $result = activate_plugin($plugin);
        if (is_wp_error($result)) {
            echo "Failed to activate $plugin: " . $result->get_error_message() . "\n";
        } else {
            echo "Successfully activated $plugin\n";
        }
    } else {
        echo "$plugin is already active\n";
    }
}

echo "Plugin activation complete!\n";
?>
