<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Billabong&display=swap" rel="stylesheet">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
    <?php wp_body_open(); ?>

    <!-- Header -->
    <header class="site-header">
        <div class="header-content">
            <h1 class="site-title">
                <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                    <?php bloginfo('name'); ?>
                </a>
            </h1>
            
            <nav class="main-navigation">
                <!-- Add navigation menu here if needed -->
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="site-main">
        <div class="container">
            <?php if (have_posts()) : ?>
                <div class="instagram-grid" id="instagram-grid">
                    <?php while (have_posts()) : the_post(); ?>
                        <article class="post-item" data-post-id="<?php the_ID(); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="post-image" onclick="openLightbox(<?php the_ID(); ?>)">
                                    <?php the_post_thumbnail('large', array('alt' => get_the_title())); ?>
                                    <div class="image-overlay">
                                        <span class="view-icon">👁</span>
                                    </div>

                                    <!-- User Interactions Overlay -->
                                    <div class="post-interactions">
                                        <div class="interaction-left">
                                            <button class="interaction-btn like-btn" data-post-id="<?php the_ID(); ?>" onclick="event.stopPropagation(); toggleLike(<?php the_ID(); ?>)">
                                                <span class="like-icon">❤️</span>
                                                <span class="interaction-count like-count"><?php echo rand(5, 150); ?></span>
                                            </button>
                                            <button class="interaction-btn comment-btn" onclick="event.stopPropagation(); scrollToComments(<?php the_ID(); ?>)">
                                                <span class="comment-icon">💬</span>
                                                <span class="interaction-count"><?php echo get_comments_number(); ?></span>
                                            </button>
                                            <button class="interaction-btn share-btn" onclick="event.stopPropagation(); sharePost(<?php the_ID(); ?>)">
                                                <span class="share-icon">📤</span>
                                            </button>
                                        </div>
                                        <div class="interaction-right">
                                            <button class="interaction-btn bookmark-btn" data-post-id="<?php the_ID(); ?>" onclick="event.stopPropagation(); toggleBookmark(<?php the_ID(); ?>)">
                                                <span class="bookmark-icon">🔖</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php else : ?>
                                <div class="post-image no-image">
                                    <div class="placeholder-image">
                                        <span>📷</span>
                                    </div>

                                    <!-- User Interactions Overlay for placeholder -->
                                    <div class="post-interactions">
                                        <div class="interaction-left">
                                            <button class="interaction-btn like-btn" data-post-id="<?php the_ID(); ?>" onclick="event.stopPropagation(); toggleLike(<?php the_ID(); ?>)">
                                                <span class="like-icon">❤️</span>
                                                <span class="interaction-count like-count"><?php echo rand(5, 150); ?></span>
                                            </button>
                                            <button class="interaction-btn comment-btn" onclick="event.stopPropagation(); scrollToComments(<?php the_ID(); ?>)">
                                                <span class="comment-icon">💬</span>
                                                <span class="interaction-count"><?php echo get_comments_number(); ?></span>
                                            </button>
                                            <button class="interaction-btn share-btn" onclick="event.stopPropagation(); sharePost(<?php the_ID(); ?>)">
                                                <span class="share-icon">📤</span>
                                            </button>
                                        </div>
                                        <div class="interaction-right">
                                            <button class="interaction-btn bookmark-btn" data-post-id="<?php the_ID(); ?>" onclick="event.stopPropagation(); toggleBookmark(<?php the_ID(); ?>)">
                                                <span class="bookmark-icon">🔖</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="post-content">
                                <h2 class="post-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h2>
                                
                                <?php if (has_excerpt()) : ?>
                                    <div class="post-excerpt">
                                        <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                                    </div>
                                <?php else : ?>
                                    <div class="post-excerpt">
                                        <?php echo wp_trim_words(get_the_content(), 15, '...'); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="post-meta">
                                    <span class="post-date"><?php echo get_the_date('M j, Y'); ?></span>
                                    <span class="post-comments">
                                        <?php comments_number('0', '1', '%'); ?> comments
                                    </span>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>

                <!-- Load More Button -->
                <div class="load-more-container text-center">
                    <button id="load-more-btn" class="load-more-btn">Load More Posts</button>
                    <div id="loading" class="loading hidden"></div>
                </div>

            <?php else : ?>
                <div class="no-posts">
                    <h2>No Posts Found</h2>
                    <p>It looks like there are no posts to display. Start creating some content!</p>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Lightbox Modal -->
    <div id="lightbox-modal" class="lightbox-modal hidden">
        <div class="lightbox-overlay" onclick="closeLightbox()"></div>
        <div class="lightbox-content">
            <button class="lightbox-close" onclick="closeLightbox()">&times;</button>
            <div class="lightbox-image-container">
                <img id="lightbox-image" src="" alt="">
            </div>
            <div class="lightbox-info">
                <h3 id="lightbox-title"></h3>
                <p id="lightbox-excerpt"></p>
                <div class="lightbox-meta">
                    <span id="lightbox-date"></span>
                    <a id="lightbox-link" href="">View Full Post</a>
                </div>
            </div>
            <div class="lightbox-navigation">
                <button id="prev-btn" onclick="navigateLightbox('prev')">&larr;</button>
                <button id="next-btn" onclick="navigateLightbox('next')">&rarr;</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. All rights reserved.</p>
            <p>Powered by <a href="https://wordpress.org" target="_blank">WordPress</a> | InstaFeed Theme</p>
        </div>
    </footer>

    <?php wp_footer(); ?>

    <style>
    /* Dark theme styling */
    body {
        background: #000000 !important;
        color: #ffffff !important;
    }

    .site-header {
        background: #000000 !important;
        border-bottom: 1px solid #262626 !important;
    }

    .site-title {
        color: #ffffff !important;
    }

    .instagram-grid {
        background: #000000 !important;
    }

    /* Post item dark styling */
    .post-item {
        background: #1a1a1a !important;
        border: 1px solid #262626 !important;
        border-radius: 12px !important;
        overflow: hidden !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
    }

    .post-item:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 15px 40px rgba(0, 149, 246, 0.3) !important;
        border-color: #0095f6 !important;
    }

    /* Post content dark styling */
    .post-content {
        background: #1a1a1a !important;
        padding: 15px !important;
        border-top: 1px solid #262626 !important;
        display: block !important;
        visibility: visible !important;
    }

    /* Title styling */
    .post-title {
        background: rgba(0, 0, 0, 0.8) !important;
        color: #ffffff !important;
        padding: 12px 15px !important;
        margin: 10px 0 !important;
        border-radius: 8px !important;
        text-align: center !important;
        font-weight: 700 !important;
        font-size: 16px !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 999 !important;
        position: relative !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.6) !important;
        transition: all 0.3s ease !important;
        border: 1px solid #262626 !important;
    }

    .post-title a {
        color: #ffffff !important;
        text-decoration: none !important;
        display: block !important;
        font-weight: 700 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        visibility: visible !important;
        opacity: 1 !important;
        transition: all 0.3s ease !important;
    }

    .post-title a:hover {
        color: #00d4ff !important;
        text-shadow: 0 2px 8px rgba(0, 212, 255, 0.6) !important;
        transform: translateY(-1px) !important;
    }

    .post-item:hover .post-title {
        background: rgba(0, 149, 246, 0.9) !important;
        transform: scale(1.02) !important;
        box-shadow: 0 6px 20px rgba(0, 149, 246, 0.4) !important;
        border-color: #00d4ff !important;
    }

    /* Post excerpt and meta dark styling */
    .post-excerpt {
        color: #b3b3b3 !important;
    }

    .post-meta {
        color: #8e8e8e !important;
    }

    /* Footer dark styling */
    .site-footer {
        background: #000000 !important;
        border-top: 1px solid #262626 !important;
        color: #8e8e8e !important;
    }

    /* User interactions overlay */
    .post-image {
        position: relative !important;
    }

    .post-interactions {
        position: absolute;
        bottom: 10px;
        left: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.7);
        border-radius: 8px;
        padding: 8px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.3s ease;
        z-index: 10;
    }

    .post-item:hover .post-interactions {
        opacity: 1;
        transform: translateY(0);
    }

    .interaction-left,
    .interaction-right {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .interaction-btn {
        background: none;
        border: none;
        color: #ffffff;
        font-size: 18px;
        cursor: pointer;
        transition: all 0.3s ease;
        padding: 5px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .interaction-btn:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.1);
    }

    .interaction-btn.liked {
        color: #ff3040;
    }

    .interaction-btn.bookmarked {
        color: #ffd700;
    }

    .interaction-count {
        font-size: 12px;
        font-weight: 600;
        color: #ffffff;
    }

    /* Load more button dark styling */
    .load-more-btn {
        background: #0095f6 !important;
        color: #ffffff !important;
        border: none !important;
        padding: 12px 30px !important;
        border-radius: 8px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }

    .load-more-btn:hover {
        background: #1877f2 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 149, 246, 0.3) !important;
    }

    /* Animations */
    @keyframes heartFloat {
        0% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
        100% {
            opacity: 0;
            transform: translate(-50%, -150%) scale(1.5);
        }
    }

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* Mobile responsive interactions */
    @media (max-width: 768px) {
        .post-interactions {
            bottom: 5px;
            left: 5px;
            right: 5px;
            padding: 6px 10px;
        }

        .interaction-left,
        .interaction-right {
            gap: 10px;
        }

        .interaction-btn {
            font-size: 16px;
            padding: 4px;
        }

        .interaction-count {
            font-size: 11px;
        }
    }

    /* Container dark styling */
    .container {
        background: #000000 !important;
    }

    .site-main {
        background: #000000 !important;
    }
    </style>

    <script>
        // Basic lightbox functionality
        let currentPostIndex = 0;
        let posts = [];

        function initializePosts() {
            posts = Array.from(document.querySelectorAll('.post-item')).map(item => ({
                id: item.dataset.postId,
                image: item.querySelector('.post-image img')?.src || '',
                title: item.querySelector('.post-title a')?.textContent || '',
                excerpt: item.querySelector('.post-excerpt')?.textContent || '',
                date: item.querySelector('.post-date')?.textContent || '',
                link: item.querySelector('.post-title a')?.href || ''
            }));
        }

        function openLightbox(postId) {
            initializePosts();
            currentPostIndex = posts.findIndex(post => post.id == postId);
            if (currentPostIndex !== -1) {
                showLightboxContent(currentPostIndex);
                document.getElementById('lightbox-modal').classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeLightbox() {
            document.getElementById('lightbox-modal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function showLightboxContent(index) {
            const post = posts[index];
            document.getElementById('lightbox-image').src = post.image;
            document.getElementById('lightbox-title').textContent = post.title;
            document.getElementById('lightbox-excerpt').textContent = post.excerpt;
            document.getElementById('lightbox-date').textContent = post.date;
            document.getElementById('lightbox-link').href = post.link;
        }

        function navigateLightbox(direction) {
            if (direction === 'next') {
                currentPostIndex = (currentPostIndex + 1) % posts.length;
            } else {
                currentPostIndex = (currentPostIndex - 1 + posts.length) % posts.length;
            }
            showLightboxContent(currentPostIndex);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializePosts();

            // Ensure titles are visible
            const titles = document.querySelectorAll('.post-title');
            titles.forEach((title) => {
                title.style.display = 'block';
                title.style.visibility = 'visible';
                title.style.opacity = '1';
            });
        });

        // User interaction functions
        function toggleLike(postId) {
            const likeBtn = document.querySelector(`[data-post-id="${postId}"].like-btn`);
            const likeCount = likeBtn.querySelector('.like-count');
            const currentCount = parseInt(likeCount.textContent);

            if (likeBtn.classList.contains('liked')) {
                likeBtn.classList.remove('liked');
                likeCount.textContent = currentCount - 1;
                likeBtn.querySelector('.like-icon').textContent = '🤍';
            } else {
                likeBtn.classList.add('liked');
                likeCount.textContent = currentCount + 1;
                likeBtn.querySelector('.like-icon').textContent = '❤️';

                // Add heart animation
                createHeartAnimation(likeBtn);
            }
        }

        function toggleBookmark(postId) {
            const bookmarkBtn = document.querySelector(`[data-post-id="${postId}"].bookmark-btn`);

            if (bookmarkBtn.classList.contains('bookmarked')) {
                bookmarkBtn.classList.remove('bookmarked');
                bookmarkBtn.querySelector('.bookmark-icon').textContent = '🔖';
            } else {
                bookmarkBtn.classList.add('bookmarked');
                bookmarkBtn.querySelector('.bookmark-icon').textContent = '📌';
            }
        }

        function scrollToComments(postId) {
            // Navigate to the post page
            window.location.href = document.querySelector(`[data-post-id="${postId}"] .post-title a`).href + '#comments';
        }

        function sharePost(postId) {
            const postUrl = document.querySelector(`[data-post-id="${postId}"] .post-title a`).href;
            const postTitle = document.querySelector(`[data-post-id="${postId}"] .post-title a`).textContent;

            if (navigator.share) {
                navigator.share({
                    title: postTitle,
                    url: postUrl
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(postUrl).then(() => {
                    showNotification('Link copied to clipboard!');
                });
            }
        }

        function createHeartAnimation(button) {
            const heart = document.createElement('div');
            heart.innerHTML = '❤️';
            heart.style.position = 'absolute';
            heart.style.fontSize = '20px';
            heart.style.pointerEvents = 'none';
            heart.style.animation = 'heartFloat 1s ease-out forwards';
            heart.style.left = '50%';
            heart.style.top = '50%';
            heart.style.transform = 'translate(-50%, -50%)';
            heart.style.zIndex = '1000';

            button.style.position = 'relative';
            button.appendChild(heart);

            setTimeout(() => {
                heart.remove();
            }, 1000);
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.background = 'rgba(0, 0, 0, 0.8)';
            notification.style.color = 'white';
            notification.style.padding = '10px 20px';
            notification.style.borderRadius = '8px';
            notification.style.zIndex = '10000';
            notification.style.animation = 'slideIn 0.3s ease-out';

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-out';
                setTimeout(() => notification.remove(), 300);
            }, 2000);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (!document.getElementById('lightbox-modal').classList.contains('hidden')) {
                if (e.key === 'Escape') closeLightbox();
                if (e.key === 'ArrowLeft') navigateLightbox('prev');
                if (e.key === 'ArrowRight') navigateLightbox('next');
            }
        });
    </script>
</body>
</html>
