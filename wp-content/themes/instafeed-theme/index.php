<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Billabong&display=swap" rel="stylesheet">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
    <?php wp_body_open(); ?>

    <!-- Header -->
    <header class="site-header">
        <div class="header-content">
            <h1 class="site-title">
                <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                    <?php bloginfo('name'); ?>
                </a>
            </h1>
            
            <nav class="main-navigation">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="<?php echo home_url(); ?>" class="nav-link <?php echo is_home() ? 'active' : ''; ?>">
                            <span class="nav-icon">🏠</span>
                            <span class="nav-text">Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showExplore()">
                            <span class="nav-icon">🔍</span>
                            <span class="nav-text">Explore</span>
                        </a>
                    </li>
                    <?php if (is_user_logged_in()) : ?>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showCreatePost()">
                                <span class="nav-icon">➕</span>
                                <span class="nav-text">Create</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showNotifications()">
                                <span class="nav-icon">❤️</span>
                                <span class="nav-text">Activity</span>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a href="#" class="nav-link" onclick="toggleUserMenu()">
                                <span class="nav-icon">👤</span>
                                <span class="nav-text">Profile</span>
                                <span class="dropdown-arrow">▼</span>
                            </a>
                            <ul class="dropdown-menu" id="user-dropdown">
                                <li><a href="#" onclick="showProfile()">👤 My Profile</a></li>
                                <li><a href="#" onclick="showMyPosts()">📝 My Posts</a></li>
                                <li><a href="#" onclick="showSettings()">⚙️ Settings</a></li>
                                <li><a href="#" onclick="showBookmarks()">🔖 Saved Posts</a></li>
                                <li class="divider"></li>
                                <li><a href="<?php echo wp_logout_url(home_url()); ?>">🚪 Logout</a></li>
                            </ul>
                        </li>
                    <?php else : ?>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showLoginModal()">
                                <span class="nav-icon">🔑</span>
                                <span class="nav-text">Login</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="showRegisterModal()">
                                <span class="nav-icon">📝</span>
                                <span class="nav-text">Sign Up</span>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>

                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="site-main">
        <div class="container">
            <?php if (have_posts()) : ?>
                <div class="instagram-grid" id="instagram-grid">
                    <?php while (have_posts()) : the_post(); ?>
                        <article class="post-item" data-post-id="<?php the_ID(); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="post-image" onclick="openLightbox(<?php the_ID(); ?>)">
                                    <?php the_post_thumbnail('large', array('alt' => get_the_title())); ?>
                                    <div class="image-overlay">
                                        <span class="view-icon">👁</span>
                                    </div>

                                    <!-- User interactions will be loaded by InstaFeed Interactions plugin -->
                                    <div class="instafeed-interactions-placeholder" data-post-id="<?php the_ID(); ?>"></div>
                                </div>
                            <?php else : ?>
                                <div class="post-image no-image">
                                    <div class="placeholder-image">
                                        <span>📷</span>
                                    </div>

                                    <!-- User interactions will be loaded by InstaFeed Interactions plugin -->
                                    <div class="instafeed-interactions-placeholder" data-post-id="<?php the_ID(); ?>"></div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="post-content">
                                <h2 class="post-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h2>
                                
                                <?php if (has_excerpt()) : ?>
                                    <div class="post-excerpt">
                                        <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                                    </div>
                                <?php else : ?>
                                    <div class="post-excerpt">
                                        <?php echo wp_trim_words(get_the_content(), 15, '...'); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="post-meta">
                                    <span class="post-date"><?php echo get_the_date('M j, Y'); ?></span>
                                    <span class="post-comments">
                                        <?php comments_number('0', '1', '%'); ?> comments
                                    </span>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>

                <!-- Load More Button -->
                <div class="load-more-container text-center">
                    <button id="load-more-btn" class="load-more-btn">Load More Posts</button>
                    <div id="loading" class="loading hidden">
                        <div class="loading-spinner"></div>
                        <p>Loading more posts...</p>
                    </div>

                    <!-- Load More Button -->
                    <div class="load-more-container" style="text-align: center; margin: 40px 0;">
                        <button id="load-more-btn" class="load-more-btn">
                            Load More Posts
                        </button>
                    </div>
                </div>

            <?php else : ?>
                <div class="no-posts">
                    <h2>No Posts Found</h2>
                    <p>It looks like there are no posts to display. Start creating some content!</p>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Go to Top Button -->
    <button class="go-to-top" id="goToTop" onclick="scrollToTop()" title="Go to top">
        ↑
    </button>

    <!-- Login Modal -->
    <div id="login-modal" class="instafeed-modal" style="display: none;">
        <div class="instafeed-modal-overlay" onclick="closeLoginModal()"></div>
        <div class="instafeed-modal-content" style="max-width: 400px;">
            <div class="instafeed-modal-header">
                <h2><?php _e('Login Required', 'instafeed-theme'); ?></h2>
                <button class="instafeed-modal-close" onclick="closeLoginModal()">&times;</button>
            </div>
            <div class="instafeed-modal-body">
                <p style="color: #8e8e8e; margin-bottom: 20px;">
                    <?php _e('Please log in to access your profile and interact with posts.', 'instafeed-theme'); ?>
                </p>
                <div class="instafeed-login-actions" style="display: flex; gap: 10px;">
                    <a href="<?php echo wp_login_url(get_permalink()); ?>" class="instafeed-btn instafeed-btn-primary" style="flex: 1; text-align: center; text-decoration: none;">
                        <?php _e('Log In', 'instafeed-theme'); ?>
                    </a>
                    <a href="<?php echo wp_registration_url(); ?>" class="instafeed-btn instafeed-btn-secondary" style="flex: 1; text-align: center; text-decoration: none;">
                        <?php _e('Sign Up', 'instafeed-theme'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if (is_user_logged_in()) : ?>
    <!-- Fallback Profile Modal (if plugin not loaded) -->
    <div id="fallback-profile-modal" class="instafeed-modal" style="display: none;">
        <div class="instafeed-modal-overlay" onclick="closeFallbackProfile()"></div>
        <div class="instafeed-modal-content instafeed-modal-large">
            <div class="instafeed-modal-header">
                <h2><?php _e('My Profile', 'instafeed-theme'); ?></h2>
                <button class="instafeed-modal-close" onclick="closeFallbackProfile()">&times;</button>
            </div>
            <div class="instafeed-modal-body">
                <div class="instafeed-profile-container">
                    <div class="instafeed-profile-header">
                        <div class="instafeed-profile-avatar-section">
                            <div class="instafeed-profile-avatar">
                                <?php echo get_avatar(get_current_user_id(), 120); ?>
                            </div>
                        </div>
                        <div class="instafeed-profile-info">
                            <div class="instafeed-profile-username">
                                <h3><?php echo esc_html(wp_get_current_user()->display_name); ?></h3>
                                <span class="instafeed-username">@<?php echo esc_html(wp_get_current_user()->user_login); ?></span>
                            </div>
                            <div class="instafeed-profile-bio">
                                <p><?php echo esc_html(get_user_meta(get_current_user_id(), 'description', true) ?: 'No bio yet. Tell us about yourself!'); ?></p>
                            </div>
                            <div class="instafeed-profile-actions">
                                <a href="<?php echo admin_url('profile.php'); ?>" class="instafeed-btn instafeed-btn-primary">
                                    <?php _e('Edit Profile in WordPress', 'instafeed-theme'); ?>
                                </a>
                                <button class="instafeed-btn instafeed-btn-secondary" onclick="activateProfilePlugin()">
                                    <?php _e('Activate Full Profile', 'instafeed-theme'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="instafeed-profile-notice" style="background: rgba(0, 149, 246, 0.1); border: 1px solid rgba(0, 149, 246, 0.3); border-radius: 8px; padding: 15px; margin-top: 20px;">
                        <p style="color: #0095f6; margin: 0;">
                            <strong><?php _e('Enhanced Profile Available!', 'instafeed-theme'); ?></strong><br>
                            <?php _e('Activate the InstaFeed Profile plugin for advanced features like custom avatars, social links, and more.', 'instafeed-theme'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Lightbox Modal -->
    <div id="lightbox-modal" class="lightbox-modal hidden">
        <div class="lightbox-overlay" onclick="closeLightbox()"></div>
        <div class="lightbox-content">
            <button class="lightbox-close" onclick="closeLightbox()">&times;</button>
            <div class="lightbox-image-container">
                <img id="lightbox-image" src="" alt="">
            </div>
            <div class="lightbox-info">
                <h3 id="lightbox-title"></h3>
                <p id="lightbox-excerpt"></p>
                <div class="lightbox-meta">
                    <span id="lightbox-date"></span>
                    <a id="lightbox-link" href="">View Full Post</a>
                </div>
            </div>
            <div class="lightbox-navigation">
                <button id="prev-btn" onclick="navigateLightbox('prev')">&larr;</button>
                <button id="next-btn" onclick="navigateLightbox('next')">&rarr;</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. All rights reserved.</p>
            <p>Powered by <a href="https://wordpress.org" target="_blank">WordPress</a> | InstaFeed Theme</p>
        </div>
    </footer>

    <?php wp_footer(); ?>

    <style>
    /* Dark theme styling */
    body {
        background: #000000 !important;
        color: #ffffff !important;
    }

    .site-header {
        background: #000000 !important;
        border-bottom: 1px solid #262626 !important;
        padding: 15px 0 !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 1000 !important;
    }

    .header-content {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 20px !important;
    }

    .site-title {
        color: #ffffff !important;
        margin: 0 !important;
    }

    .site-title a {
        color: #ffffff !important;
        text-decoration: none !important;
        font-size: 24px !important;
        font-weight: 700 !important;
    }

    /* Navigation Menu */
    .main-navigation {
        display: flex !important;
        align-items: center !important;
    }

    .nav-menu {
        display: flex !important;
        list-style: none !important;
        margin: 0 !important;
        padding: 0 !important;
        gap: 20px !important;
    }

    .nav-item {
        position: relative !important;
    }

    .nav-link {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        color: #ffffff !important;
        text-decoration: none !important;
        padding: 8px 12px !important;
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
        font-weight: 500 !important;
    }

    .nav-link:hover,
    .nav-link.active {
        background: rgba(0, 149, 246, 0.1) !important;
        color: #0095f6 !important;
    }

    .nav-icon {
        font-size: 18px !important;
    }

    .nav-text {
        font-size: 14px !important;
    }

    /* Dropdown Menu */
    .dropdown {
        position: relative !important;
    }

    .dropdown-menu {
        position: absolute !important;
        top: 100% !important;
        right: 0 !important;
        background: #1a1a1a !important;
        border: 1px solid #262626 !important;
        border-radius: 8px !important;
        padding: 8px 0 !important;
        min-width: 200px !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5) !important;
        opacity: 0 !important;
        visibility: hidden !important;
        transform: translateY(-10px) !important;
        transition: all 0.3s ease !important;
        z-index: 1001 !important;
    }

    .dropdown-menu.show {
        opacity: 1 !important;
        visibility: visible !important;
        transform: translateY(0) !important;
    }

    .dropdown-menu li {
        list-style: none !important;
    }

    .dropdown-menu a {
        display: block !important;
        padding: 10px 16px !important;
        color: #ffffff !important;
        text-decoration: none !important;
        transition: background 0.3s ease !important;
    }

    .dropdown-menu a:hover {
        background: rgba(0, 149, 246, 0.1) !important;
        color: #0095f6 !important;
    }

    .dropdown-menu .divider {
        height: 1px !important;
        background: #262626 !important;
        margin: 8px 0 !important;
    }

    /* Mobile Menu Toggle */
    .mobile-menu-toggle {
        display: none !important;
        flex-direction: column !important;
        background: none !important;
        border: none !important;
        cursor: pointer !important;
        padding: 5px !important;
    }

    .mobile-menu-toggle span {
        width: 25px !important;
        height: 3px !important;
        background: #ffffff !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 2px !important;
    }

    .instagram-grid {
        background: #000000 !important;
    }

    /* Post item dark styling */
    .post-item {
        background: #1a1a1a !important;
        border: 1px solid #262626 !important;
        border-radius: 12px !important;
        overflow: hidden !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
    }

    .post-item:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 15px 40px rgba(0, 149, 246, 0.3) !important;
        border-color: #0095f6 !important;
    }

    /* Post content dark styling */
    .post-content {
        background: #1a1a1a !important;
        padding: 15px !important;
        border-top: 1px solid #262626 !important;
        display: block !important;
        visibility: visible !important;
    }

    /* Title styling */
    .post-title {
        background: rgba(0, 0, 0, 0.8) !important;
        color: #ffffff !important;
        padding: 12px 15px !important;
        margin: 10px 0 !important;
        border-radius: 8px !important;
        text-align: center !important;
        font-weight: 700 !important;
        font-size: 16px !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 999 !important;
        position: relative !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.6) !important;
        transition: all 0.3s ease !important;
        border: 1px solid #262626 !important;
    }

    .post-title a {
        color: #ffffff !important;
        text-decoration: none !important;
        display: block !important;
        font-weight: 700 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        visibility: visible !important;
        opacity: 1 !important;
        transition: all 0.3s ease !important;
    }

    .post-title a:hover {
        color: #00d4ff !important;
        text-shadow: 0 2px 8px rgba(0, 212, 255, 0.6) !important;
        transform: translateY(-1px) !important;
    }

    .post-item:hover .post-title {
        background: rgba(0, 149, 246, 0.9) !important;
        transform: scale(1.02) !important;
        box-shadow: 0 6px 20px rgba(0, 149, 246, 0.4) !important;
        border-color: #00d4ff !important;
    }

    /* Post excerpt and meta dark styling */
    .post-excerpt {
        color: #b3b3b3 !important;
    }

    .post-meta {
        color: #8e8e8e !important;
    }

    /* Footer dark styling */
    .site-footer {
        background: #000000 !important;
        border-top: 1px solid #262626 !important;
        color: #8e8e8e !important;
    }

    /* User interactions will be styled by InstaFeed Interactions plugin */
    .instafeed-interactions-placeholder {
        /* Placeholder for interactions */
    }

    /* Load more button dark styling */
    .load-more-btn {
        background: #0095f6 !important;
        color: #ffffff !important;
        border: none !important;
        padding: 12px 30px !important;
        border-radius: 8px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }

    .load-more-btn:hover {
        background: #1877f2 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(0, 149, 246, 0.3) !important;
    }

    /* Animations */
    @keyframes heartFloat {
        0% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
        100% {
            opacity: 0;
            transform: translate(-50%, -150%) scale(1.5);
        }
    }

    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* Custom Modals */
    .custom-modal,
    .instafeed-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    }

    .instafeed-modal.show {
        display: flex !important;
    }

    .instafeed-modal-large {
        max-width: 800px !important;
        width: 95% !important;
    }

    .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .modal-content {
        background: #1a1a1a;
        border-radius: 12px;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
        border: 1px solid #262626;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #262626;
    }

    .modal-header h2 {
        color: #ffffff;
        margin: 0;
        font-size: 20px;
    }

    .modal-close {
        background: none;
        border: none;
        color: #8e8e8e;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .modal-close:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }

    .modal-body {
        padding: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        color: #ffffff;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: 12px;
        background: #262626;
        border: 1px solid #404040;
        border-radius: 8px;
        color: #ffffff;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }

    .form-group input:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #0095f6;
    }

    .btn-primary {
        width: 100%;
        padding: 12px;
        background: #0095f6;
        color: #ffffff;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.3s ease;
    }

    .btn-primary:hover {
        background: #1877f2;
    }

    .modal-footer-text {
        text-align: center;
        margin-top: 20px;
        color: #8e8e8e;
    }

    .modal-footer-text a {
        color: #0095f6;
        text-decoration: none;
    }

    .modal-footer-text a:hover {
        text-decoration: underline;
    }

    /* Mobile responsive */
    @media (max-width: 768px) {

        /* Mobile Navigation */
        .mobile-menu-toggle {
            display: flex !important;
        }

        .nav-menu {
            position: fixed;
            top: 70px;
            left: -100%;
            width: 100%;
            height: calc(100vh - 70px);
            background: #000000;
            flex-direction: column;
            padding: 20px;
            transition: left 0.3s ease;
            border-top: 1px solid #262626;
        }

        .nav-menu.mobile-open {
            left: 0;
        }

        .nav-item {
            width: 100%;
            margin-bottom: 10px;
        }

        .nav-link {
            width: 100%;
            justify-content: flex-start;
            padding: 15px;
        }

        .nav-text {
            font-size: 16px;
        }

        .dropdown-menu {
            position: static;
            background: #262626;
            box-shadow: none;
            border: none;
            opacity: 1;
            visibility: visible;
            transform: none;
            margin-top: 10px;
        }

        .modal-content {
            width: 95%;
            margin: 20px;
        }

        .modal-header,
        .modal-body {
            padding: 15px;
        }
    }

    /* Container dark styling */
    .container {
        background: #000000 !important;
    }

    .site-main {
        background: #000000 !important;
    }

    /* Notification Styles */
    .instafeed-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: #ffffff;
        font-weight: 500;
        z-index: 10001;
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.3s ease;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .instafeed-notification.show {
        transform: translateX(0);
        opacity: 1;
    }

    .instafeed-notification.success {
        background: #27ae60;
    }

    .instafeed-notification.error {
        background: #e74c3c;
    }

    .instafeed-notification.info {
        background: #3498db;
    }

    /* Fix any remaining white elements */
    body, html {
        background: #000000 !important;
        color: #ffffff !important;
    }

    .entry-content, .entry-summary, .page-content {
        background: #000000 !important;
        color: #ffffff !important;
    }

    .entry-title, .entry-title a {
        color: #ffffff !important;
    }

    .entry-meta, .entry-meta a {
        color: #8e8e8e !important;
    }

    /* WordPress default elements */
    .wp-block-group, .wp-block-cover, .wp-block-columns {
        background: #000000 !important;
        color: #ffffff !important;
    }

    /* Comments section */
    .comments-area, .comment-list, .comment-body {
        background: #000000 !important;
        color: #ffffff !important;
    }

    .comment-author, .comment-meta {
        color: #8e8e8e !important;
    }

    /* Forms */
    input[type="text"], input[type="email"], input[type="password"],
    input[type="search"], input[type="url"], textarea, select {
        background: #262626 !important;
        color: #ffffff !important;
        border: 1px solid #404040 !important;
    }

    input[type="text"]:focus, input[type="email"]:focus,
    input[type="password"]:focus, textarea:focus {
        border-color: #0095f6 !important;
        box-shadow: 0 0 0 2px rgba(0, 149, 246, 0.2) !important;
    }

    /* Go to Top Arrow */
    .go-to-top {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: #0095f6;
        color: #ffffff;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(20px);
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 149, 246, 0.3);
    }

    .go-to-top.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .go-to-top:hover {
        background: #1877f2;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 149, 246, 0.4);
    }

    /* Mobile go to top */
    @media (max-width: 768px) {
        .go-to-top {
            bottom: 20px;
            right: 20px;
            width: 45px;
            height: 45px;
            font-size: 18px;
        }
    }
    </style>

    <script>
        // Basic lightbox functionality
        let currentPostIndex = 0;
        let posts = [];

        function initializePosts() {
            posts = Array.from(document.querySelectorAll('.post-item')).map(item => ({
                id: item.dataset.postId,
                image: item.querySelector('.post-image img')?.src || '',
                title: item.querySelector('.post-title a')?.textContent || '',
                excerpt: item.querySelector('.post-excerpt')?.textContent || '',
                date: item.querySelector('.post-date')?.textContent || '',
                link: item.querySelector('.post-title a')?.href || ''
            }));
        }

        function openLightbox(postId) {
            initializePosts();
            currentPostIndex = posts.findIndex(post => post.id == postId);
            if (currentPostIndex !== -1) {
                showLightboxContent(currentPostIndex);
                document.getElementById('lightbox-modal').classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeLightbox() {
            document.getElementById('lightbox-modal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function showLightboxContent(index) {
            const post = posts[index];
            document.getElementById('lightbox-image').src = post.image;
            document.getElementById('lightbox-title').textContent = post.title;
            document.getElementById('lightbox-excerpt').textContent = post.excerpt;
            document.getElementById('lightbox-date').textContent = post.date;
            document.getElementById('lightbox-link').href = post.link;
        }

        function navigateLightbox(direction) {
            if (direction === 'next') {
                currentPostIndex = (currentPostIndex + 1) % posts.length;
            } else {
                currentPostIndex = (currentPostIndex - 1 + posts.length) % posts.length;
            }
            showLightboxContent(currentPostIndex);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializePosts();

            // Ensure titles are visible
            const titles = document.querySelectorAll('.post-title');
            titles.forEach((title) => {
                title.style.display = 'block';
                title.style.visibility = 'visible';
                title.style.opacity = '1';
            });

            // Initialize go to top button
            initializeGoToTop();

            // Initialize load more functionality
            initializeLoadMore();

            // Initialize interactions for existing content
            setTimeout(function() {
                if (typeof window.initializeInteractionsForNewContent === 'function') {
                    window.initializeInteractionsForNewContent();
                    console.log('Initialized interactions for existing content');
                }
            }, 500);

            // Check if profile plugin is loaded
            setTimeout(function() {
                const profileModal = document.getElementById('instafeed-profile-modal');
                if (profileModal) {
                    console.log('InstaFeed Profile modal found and ready');
                } else {
                    console.log('InstaFeed Profile modal not found - plugin may not be active');
                }
            }, 1000);
        });

        // Go to top functionality
        function initializeGoToTop() {
            const goToTopBtn = document.getElementById('goToTop');

            // Show/hide button based on scroll position
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    goToTopBtn.classList.add('show');
                } else {
                    goToTopBtn.classList.remove('show');
                }
            });
        }

        // Load more posts functionality
        function initializeLoadMore() {
            const loadMoreBtn = document.getElementById('load-more-btn');
            const loading = document.getElementById('loading');
            let currentPage = 1;
            let isLoading = false;

            if (!loadMoreBtn) return;

            loadMoreBtn.addEventListener('click', function() {
                if (isLoading) return;

                isLoading = true;
                loadMoreBtn.style.display = 'none';
                loading.classList.remove('hidden');

                // Simulate loading more posts (replace with actual AJAX call)
                setTimeout(function() {
                    loadMorePosts(currentPage + 1).then(function(newPosts) {
                        if (newPosts && newPosts.length > 0) {
                            appendNewPosts(newPosts);
                            currentPage++;

                            // Initialize interactions for new posts
                            setTimeout(function() {
                                if (typeof window.initializeInteractionsForNewContent === 'function') {
                                    window.initializeInteractionsForNewContent();
                                    console.log('Initialized interactions for newly loaded posts');
                                }
                            }, 100);
                        } else {
                            loadMoreBtn.textContent = 'No more posts';
                            loadMoreBtn.disabled = true;
                        }

                        isLoading = false;
                        loading.classList.add('hidden');
                        loadMoreBtn.style.display = 'block';
                    }).catch(function() {
                        isLoading = false;
                        loading.classList.add('hidden');
                        loadMoreBtn.style.display = 'block';
                        showNotification('Failed to load more posts', 'error');
                    });
                }, 1000);
            });
        }

        function loadMorePosts(page) {
            return new Promise(function(resolve, reject) {
                // AJAX call to load more posts
                fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=load_more_posts&page=' + page + '&nonce=<?php echo wp_create_nonce('load_more_posts'); ?>'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resolve(data.data.posts);
                    } else {
                        reject(data.data);
                    }
                })
                .catch(error => {
                    console.error('Error loading more posts:', error);
                    reject(error);
                });
            });
        }

        function appendNewPosts(posts) {
            const postsGrid = document.querySelector('.posts-grid');
            if (!postsGrid) return;

            posts.forEach(function(post) {
                const postElement = createPostElement(post);
                postsGrid.appendChild(postElement);
            });

            // Initialize interactions for new posts
            setTimeout(function() {
                console.log('Initializing interactions for', posts.length, 'new posts');

                // Method 1: Use global function if available
                if (typeof window.initializeInteractionsForNewContent === 'function') {
                    window.initializeInteractionsForNewContent(postsGrid);
                    console.log('Used global initializeInteractionsForNewContent');
                }

                // Method 2: Trigger custom event for plugins to listen
                const event = new CustomEvent('instaFeedNewContent', {
                    detail: {
                        container: postsGrid,
                        posts: posts,
                        postCount: posts.length
                    }
                });
                document.dispatchEvent(event);
                console.log('Dispatched instaFeedNewContent event');

                // Method 3: Direct initialization if interactions plugin is loaded
                if (typeof jQuery !== 'undefined' && jQuery.fn.instaFeedInteractions) {
                    jQuery(postsGrid).find('.instafeed-interactions-placeholder').instaFeedInteractions();
                    console.log('Used jQuery plugin initialization');
                }

            }, 200); // Small delay to ensure DOM is ready
        }

        function createPostElement(post) {
            const postDiv = document.createElement('div');
            postDiv.className = 'post-item';
            postDiv.setAttribute('data-post-id', post.id);

            postDiv.innerHTML = `
                <div class="post-image" style="position: relative;">
                    <img src="${post.image}" alt="${post.title}" onclick="openLightbox('${post.image}', '${post.title}')" style="width: 100%; height: auto; display: block;">

                    <!-- User interactions placeholder - will be populated by InstaFeed Interactions plugin -->
                    <div class="instafeed-interactions-placeholder"
                         data-post-id="${post.id}"
                         data-post-title="${post.title.replace(/"/g, '&quot;')}"
                         data-post-url="${post.url}"
                         style="position: absolute; bottom: 10px; left: 10px; right: 10px; background: rgba(0,0,0,0.7); border-radius: 8px; padding: 8px; display: flex; justify-content: space-between; align-items: center;">

                        <!-- Loading indicator while interactions load -->
                        <div class="interactions-loading" style="color: #fff; font-size: 12px;">
                            Loading interactions...
                        </div>
                    </div>

                    <!-- Image overlay for better interaction visibility -->
                    <div class="image-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(transparent 60%, rgba(0,0,0,0.3)); pointer-events: none;"></div>
                </div>

                <div class="post-content" style="padding: 15px; background: #1a1a1a; border-top: 1px solid #262626;">
                    <h3 class="post-title" style="margin: 0 0 10px; font-size: 16px;">
                        <a href="${post.url}" style="color: #fff; text-decoration: none;">${post.title}</a>
                    </h3>
                    <div class="post-meta" style="color: #8e8e8e; font-size: 14px;">
                        <span class="post-date">${post.date}</span>
                        <span class="post-author"> • by ${post.author}</span>
                    </div>
                </div>
            `;

            // Mark as newly created for interaction initialization
            postDiv.classList.add('newly-loaded');

            return postDiv;
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Login modal functions
        function showLoginModal() {
            const modal = document.getElementById('login-modal');
            if (modal) {
                modal.style.display = 'flex';
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeLoginModal() {
            const modal = document.getElementById('login-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }, 300);
            }
        }

        // User interaction functions will be loaded by InstaFeed Interactions plugin

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.background = 'rgba(0, 0, 0, 0.8)';
            notification.style.color = 'white';
            notification.style.padding = '10px 20px';
            notification.style.borderRadius = '8px';
            notification.style.zIndex = '10000';
            notification.style.animation = 'slideIn 0.3s ease-out';

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-out';
                setTimeout(() => notification.remove(), 300);
            }, 2000);
        }

        // Navigation menu functions
        function toggleUserMenu() {
            const dropdown = document.getElementById('user-dropdown');
            dropdown.classList.toggle('show');
        }

        function toggleMobileMenu() {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.classList.toggle('mobile-open');
        }

        function showExplore() {
            showNotification('Explore feature coming soon!');
        }

        function showCreatePost() {
            if (typeof openInstaFeedModal === 'function' && document.getElementById('instafeed-creator-modal')) {
                openInstaFeedModal('instafeed-creator-modal');
            } else {
                showNotification('Creator plugin not loaded. Please activate InstaFeed Creator plugin.', 'error');
            }
        }

        function showNotifications() {
            showNotification('Activity notifications coming soon!');
        }

        function showProfile() {
            console.log('showProfile() called');

            // Check if user is logged in first
            <?php if (!is_user_logged_in()) : ?>
                console.log('User not logged in, showing login modal');
                showLoginModal();
                return;
            <?php endif; ?>

            // Try to open the enhanced profile modal first
            const profileModal = document.getElementById('instafeed-profile-modal');
            console.log('Enhanced profile modal found:', !!profileModal);

            if (profileModal && typeof openInstaFeedModal === 'function') {
                // Enhanced profile plugin is loaded
                console.log('Opening enhanced profile modal');
                openInstaFeedModal('instafeed-profile-modal');
            } else if (profileModal) {
                // Modal exists but function might not be loaded yet
                console.log('Opening profile modal directly');
                profileModal.style.display = 'flex';
                profileModal.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                // Use fallback profile modal
                console.log('Using fallback profile modal');
                const fallbackModal = document.getElementById('fallback-profile-modal');
                console.log('Fallback modal found:', !!fallbackModal);

                if (fallbackModal) {
                    fallbackModal.style.display = 'flex';
                    fallbackModal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                    console.log('Fallback profile modal opened');
                } else {
                    // Last resort - redirect to WordPress profile
                    console.log('No profile modal found, redirecting to WordPress profile');
                    if (confirm('Profile modal not available. Go to WordPress profile page?')) {
                        window.location.href = '<?php echo admin_url('profile.php'); ?>';
                    }
                }
            }
        }

        function closeFallbackProfile() {
            const modal = document.getElementById('fallback-profile-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }, 300);
            }
        }

        function activateProfilePlugin() {
            showNotification('Please contact your administrator to activate the InstaFeed Profile plugin for enhanced features.', 'info');
        }

        // Global modal functions (fallback if plugins don't load)
        if (typeof window.openInstaFeedModal === 'undefined') {
            window.openInstaFeedModal = function(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'flex';
                    modal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                    console.log('Opened modal:', modalId);
                } else {
                    console.log('Modal not found:', modalId);
                }
            };
        }

        if (typeof window.closeInstaFeedModal === 'undefined') {
            window.closeInstaFeedModal = function(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.remove('show');
                    setTimeout(() => {
                        modal.style.display = 'none';
                        document.body.style.overflow = 'auto';
                    }, 300);
                    console.log('Closed modal:', modalId);
                }
            };
        }

        // Profile plugin integration helper
        function ensureProfilePluginLoaded() {
            // This function ensures the profile plugin modal is available
            return document.getElementById('instafeed-profile-modal') !== null;
        }

        function showMyPosts() {
            if (typeof openInstaFeedModal === 'function' && document.getElementById('instafeed-my-posts-modal')) {
                openInstaFeedModal('instafeed-my-posts-modal');
            } else {
                showNotification('Creator plugin not loaded. Please activate InstaFeed Creator plugin.', 'error');
            }
        }

        function showSettings() {
            showSettingsModal();
        }

        function showBookmarks() {
            showBookmarksModal();
        }

        function showLoginModal() {
            showModal('login-modal');
        }

        function showRegisterModal() {
            showModal('register-modal');
        }

        function showCreatePostModal() {
            showModal('create-post-modal');
        }

        function showProfileModal() {
            showModal('profile-modal');
        }

        function showMyPostsModal() {
            showModal('my-posts-modal');
        }

        function showSettingsModal() {
            showModal('settings-modal');
        }

        function showBookmarksModal() {
            showModal('bookmarks-modal');
        }

        function showModal(modalId) {
            // Create modal if it doesn't exist
            if (!document.getElementById(modalId)) {
                createModal(modalId);
            }
            document.getElementById(modalId).style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function createModal(modalId) {
            const modal = document.createElement('div');
            modal.id = modalId;
            modal.className = 'custom-modal';
            modal.innerHTML = getModalContent(modalId);
            document.body.appendChild(modal);
        }

        function getModalContent(modalId) {
            switch(modalId) {
                case 'login-modal':
                    return `
                        <div class="modal-overlay" onclick="closeModal('login-modal')"></div>
                        <div class="modal-content">
                            <div class="modal-header">
                                <h2>Login to InstaFeed</h2>
                                <button class="modal-close" onclick="closeModal('login-modal')">&times;</button>
                            </div>
                            <div class="modal-body">
                                <form id="login-form">
                                    <div class="form-group">
                                        <label>Username or Email</label>
                                        <input type="text" name="username" required>
                                    </div>
                                    <div class="form-group">
                                        <label>Password</label>
                                        <input type="password" name="password" required>
                                    </div>
                                    <button type="submit" class="btn-primary">Login</button>
                                </form>
                                <p class="modal-footer-text">
                                    Don't have an account? <a href="#" onclick="closeModal('login-modal'); showRegisterModal();">Sign up</a>
                                </p>
                            </div>
                        </div>
                    `;
                case 'register-modal':
                    return `
                        <div class="modal-overlay" onclick="closeModal('register-modal')"></div>
                        <div class="modal-content">
                            <div class="modal-header">
                                <h2>Join InstaFeed</h2>
                                <button class="modal-close" onclick="closeModal('register-modal')">&times;</button>
                            </div>
                            <div class="modal-body">
                                <form id="register-form">
                                    <div class="form-group">
                                        <label>Username</label>
                                        <input type="text" name="username" required>
                                    </div>
                                    <div class="form-group">
                                        <label>Email</label>
                                        <input type="email" name="email" required>
                                    </div>
                                    <div class="form-group">
                                        <label>Password</label>
                                        <input type="password" name="password" required>
                                    </div>
                                    <button type="submit" class="btn-primary">Sign Up</button>
                                </form>
                                <p class="modal-footer-text">
                                    Already have an account? <a href="#" onclick="closeModal('register-modal'); showLoginModal();">Login</a>
                                </p>
                            </div>
                        </div>
                    `;
                case 'create-post-modal':
                    return `
                        <div class="modal-overlay" onclick="closeModal('create-post-modal')"></div>
                        <div class="modal-content">
                            <div class="modal-header">
                                <h2>Create New Post</h2>
                                <button class="modal-close" onclick="closeModal('create-post-modal')">&times;</button>
                            </div>
                            <div class="modal-body">
                                <form id="create-post-form">
                                    <div class="form-group">
                                        <label>Post Title</label>
                                        <input type="text" name="title" required>
                                    </div>
                                    <div class="form-group">
                                        <label>Upload Image</label>
                                        <input type="file" name="image" accept="image/*">
                                    </div>
                                    <div class="form-group">
                                        <label>Caption</label>
                                        <textarea name="content" rows="4"></textarea>
                                    </div>
                                    <button type="submit" class="btn-primary">Create Post</button>
                                </form>
                            </div>
                        </div>
                    `;
                default:
                    return `
                        <div class="modal-overlay" onclick="closeModal('${modalId}')"></div>
                        <div class="modal-content">
                            <div class="modal-header">
                                <h2>Feature Coming Soon</h2>
                                <button class="modal-close" onclick="closeModal('${modalId}')">&times;</button>
                            </div>
                            <div class="modal-body">
                                <p>This feature is currently under development. Stay tuned!</p>
                            </div>
                        </div>
                    `;
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.remove('show');
                });
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (!document.getElementById('lightbox-modal').classList.contains('hidden')) {
                if (e.key === 'Escape') closeLightbox();
                if (e.key === 'ArrowLeft') navigateLightbox('prev');
                if (e.key === 'ArrowRight') navigateLightbox('next');
            }
        });
    </script>
</body>
</html>
