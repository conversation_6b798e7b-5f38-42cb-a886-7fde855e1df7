<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Billabong&display=swap" rel="stylesheet">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
    <?php wp_body_open(); ?>

    <!-- Header -->
    <header class="site-header">
        <div class="header-content">
            <h1 class="site-title">
                <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                    <?php bloginfo('name'); ?>
                </a>
            </h1>
            
            <nav class="main-navigation">
                <!-- Add navigation menu here if needed -->
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="site-main">
        <div class="container">
            <?php if (have_posts()) : ?>
                <div class="instagram-grid" id="instagram-grid">
                    <?php while (have_posts()) : the_post(); ?>
                        <article class="post-item" data-post-id="<?php the_ID(); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="post-image" onclick="openLightbox(<?php the_ID(); ?>)">
                                    <?php the_post_thumbnail('large', array('alt' => get_the_title())); ?>
                                    <div class="image-overlay">
                                        <span class="view-icon">👁</span>
                                    </div>
                                </div>
                            <?php else : ?>
                                <div class="post-image no-image">
                                    <div class="placeholder-image">
                                        <span>📷</span>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="post-content">
                                <h2 class="post-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h2>
                                
                                <?php if (has_excerpt()) : ?>
                                    <div class="post-excerpt">
                                        <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                                    </div>
                                <?php else : ?>
                                    <div class="post-excerpt">
                                        <?php echo wp_trim_words(get_the_content(), 15, '...'); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="post-meta">
                                    <span class="post-date"><?php echo get_the_date('M j, Y'); ?></span>
                                    <span class="post-comments">
                                        <?php comments_number('0', '1', '%'); ?> comments
                                    </span>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>

                <!-- Load More Button -->
                <div class="load-more-container text-center">
                    <button id="load-more-btn" class="load-more-btn">Load More Posts</button>
                    <div id="loading" class="loading hidden"></div>
                </div>

            <?php else : ?>
                <div class="no-posts">
                    <h2>No Posts Found</h2>
                    <p>It looks like there are no posts to display. Start creating some content!</p>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Lightbox Modal -->
    <div id="lightbox-modal" class="lightbox-modal hidden">
        <div class="lightbox-overlay" onclick="closeLightbox()"></div>
        <div class="lightbox-content">
            <button class="lightbox-close" onclick="closeLightbox()">&times;</button>
            <div class="lightbox-image-container">
                <img id="lightbox-image" src="" alt="">
            </div>
            <div class="lightbox-info">
                <h3 id="lightbox-title"></h3>
                <p id="lightbox-excerpt"></p>
                <div class="lightbox-meta">
                    <span id="lightbox-date"></span>
                    <a id="lightbox-link" href="">View Full Post</a>
                </div>
            </div>
            <div class="lightbox-navigation">
                <button id="prev-btn" onclick="navigateLightbox('prev')">&larr;</button>
                <button id="next-btn" onclick="navigateLightbox('next')">&rarr;</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. All rights reserved.</p>
            <p>Powered by <a href="https://wordpress.org" target="_blank">WordPress</a> | InstaFeed Theme</p>
        </div>
    </footer>

    <?php wp_footer(); ?>

    <style>
    /* Final title visibility solution */
    .post-title {
        background: rgba(0, 0, 0, 0.85) !important;
        color: #ffffff !important;
        padding: 12px 15px !important;
        margin: 10px 0 !important;
        border-radius: 8px !important;
        text-align: center !important;
        font-weight: 700 !important;
        font-size: 16px !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 999 !important;
        position: relative !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4) !important;
        transition: all 0.3s ease !important;
    }

    .post-title a {
        color: #ffffff !important;
        text-decoration: none !important;
        display: block !important;
        font-weight: 700 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8) !important;
        visibility: visible !important;
        opacity: 1 !important;
        transition: all 0.3s ease !important;
    }

    .post-title a:hover {
        color: #00d4ff !important;
        text-shadow: 0 2px 8px rgba(0, 212, 255, 0.6) !important;
        transform: translateY(-1px) !important;
    }

    .post-item:hover .post-title {
        background: rgba(0, 149, 246, 0.9) !important;
        transform: scale(1.02) !important;
        box-shadow: 0 6px 20px rgba(0, 149, 246, 0.4) !important;
    }

    /* Ensure post content is visible */
    .post-content {
        background: #ffffff !important;
        padding: 15px !important;
        border-top: 1px solid #efefef !important;
        display: block !important;
        visibility: visible !important;
    }

    /* Post item styling */
    .post-item {
        background: #fff !important;
        border: 1px solid #dbdbdb !important;
        border-radius: 8px !important;
        overflow: visible !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .post-item:hover {
        transform: translateY(-3px) !important;
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
        border-color: #0095f6 !important;
    }
    </style>

    <script>
        // Basic lightbox functionality
        let currentPostIndex = 0;
        let posts = [];

        function initializePosts() {
            posts = Array.from(document.querySelectorAll('.post-item')).map(item => ({
                id: item.dataset.postId,
                image: item.querySelector('.post-image img')?.src || '',
                title: item.querySelector('.post-title a')?.textContent || '',
                excerpt: item.querySelector('.post-excerpt')?.textContent || '',
                date: item.querySelector('.post-date')?.textContent || '',
                link: item.querySelector('.post-title a')?.href || ''
            }));
        }

        function openLightbox(postId) {
            initializePosts();
            currentPostIndex = posts.findIndex(post => post.id == postId);
            if (currentPostIndex !== -1) {
                showLightboxContent(currentPostIndex);
                document.getElementById('lightbox-modal').classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeLightbox() {
            document.getElementById('lightbox-modal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function showLightboxContent(index) {
            const post = posts[index];
            document.getElementById('lightbox-image').src = post.image;
            document.getElementById('lightbox-title').textContent = post.title;
            document.getElementById('lightbox-excerpt').textContent = post.excerpt;
            document.getElementById('lightbox-date').textContent = post.date;
            document.getElementById('lightbox-link').href = post.link;
        }

        function navigateLightbox(direction) {
            if (direction === 'next') {
                currentPostIndex = (currentPostIndex + 1) % posts.length;
            } else {
                currentPostIndex = (currentPostIndex - 1 + posts.length) % posts.length;
            }
            showLightboxContent(currentPostIndex);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializePosts();

            // Ensure titles are visible
            const titles = document.querySelectorAll('.post-title');
            titles.forEach((title) => {
                title.style.display = 'block';
                title.style.visibility = 'visible';
                title.style.opacity = '1';
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (!document.getElementById('lightbox-modal').classList.contains('hidden')) {
                if (e.key === 'Escape') closeLightbox();
                if (e.key === 'ArrowLeft') navigateLightbox('prev');
                if (e.key === 'ArrowRight') navigateLightbox('next');
            }
        });
    </script>
</body>
</html>
