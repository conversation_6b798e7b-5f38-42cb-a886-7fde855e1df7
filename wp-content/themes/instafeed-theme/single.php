<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Billabong&display=swap" rel="stylesheet">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
    <?php wp_body_open(); ?>

    <!-- Header -->
    <header class="site-header">
        <div class="header-content">
            <h1 class="site-title">
                <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                    <?php bloginfo('name'); ?>
                </a>
            </h1>
            
            <nav class="main-navigation">
                <a href="<?php echo esc_url(home_url('/')); ?>" class="back-to-grid">← Back to Grid</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="site-main single-post">
        <div class="container">
            <?php while (have_posts()) : the_post(); ?>
                <article class="single-post-content">
                    <header class="post-header">
                        <h1 class="post-title"><?php the_title(); ?></h1>
                        <div class="post-meta">
                            <span class="post-date"><?php echo get_the_date('F j, Y'); ?></span>
                            <span class="post-author">by <?php the_author(); ?></span>
                            <span class="post-comments">
                                <?php comments_number('No comments', '1 comment', '% comments'); ?>
                            </span>
                        </div>
                    </header>

                    <?php if (has_post_thumbnail()) : ?>
                        <div class="featured-image">
                            <?php the_post_thumbnail('large'); ?>
                        </div>
                    <?php endif; ?>

                    <div class="post-content">
                        <?php the_content(); ?>
                    </div>

                    <footer class="post-footer">
                        <div class="post-tags">
                            <?php the_tags('Tags: ', ', ', ''); ?>
                        </div>
                        
                        <div class="post-navigation">
                            <div class="nav-previous">
                                <?php previous_post_link('%link', '← Previous Post'); ?>
                            </div>
                            <div class="nav-next">
                                <?php next_post_link('%link', 'Next Post →'); ?>
                            </div>
                        </div>
                    </footer>
                </article>

                <?php
                // Comments section
                if (comments_open() || get_comments_number()) :
                    comments_template();
                endif;
                ?>

            <?php endwhile; ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. All rights reserved.</p>
            <p>Powered by <a href="https://wordpress.org" target="_blank">WordPress</a> | InstaFeed Theme</p>
        </div>
    </footer>

    <?php wp_footer(); ?>

    <style>
        /* Single Post Styles */
        .single-post {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .single-post-content {
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .post-header {
            padding: 30px 30px 20px;
            border-bottom: 1px solid #dbdbdb;
        }

        .post-header .post-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.3;
            color: #262626;
        }

        .post-header .post-meta {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #8e8e8e;
        }

        .featured-image {
            width: 100%;
            max-height: 500px;
            overflow: hidden;
        }

        .featured-image img {
            width: 100%;
            height: auto;
            object-fit: cover;
        }

        .post-content {
            padding: 30px;
            line-height: 1.7;
            font-size: 16px;
        }

        .post-content p {
            margin-bottom: 20px;
        }

        .post-footer {
            padding: 20px 30px 30px;
            border-top: 1px solid #dbdbdb;
        }

        .post-tags {
            margin-bottom: 20px;
            font-size: 14px;
        }

        .post-tags a {
            color: #0095f6;
            text-decoration: none;
        }

        .post-navigation {
            display: flex;
            justify-content: space-between;
        }

        .post-navigation a {
            color: #0095f6;
            text-decoration: none;
            font-weight: 500;
        }

        .back-to-grid {
            color: #0095f6;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .single-post {
                padding: 20px 15px;
            }

            .post-header,
            .post-content,
            .post-footer {
                padding: 20px;
            }

            .post-header .post-title {
                font-size: 24px;
            }

            .post-header .post-meta {
                flex-direction: column;
                gap: 5px;
            }

            .post-navigation {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</body>
</html>
