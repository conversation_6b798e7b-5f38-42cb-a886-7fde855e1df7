<?php
/**
 * Plugin Name: InstaFeed Interactions
 * Plugin URI: https://example.com/instafeed-interactions
 * Description: Complete user interaction system for InstaFeed. Handles likes, dislikes, comments, shares, and bookmarks with Instagram-style interface and admin controls.
 * Version: 1.0.0
 * Author: InstaFeed Development Team
 * License: GPL v2 or later
 * Text Domain: instafeed-interactions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('INSTAFEED_INTERACTIONS_URL', plugin_dir_url(__FILE__));
define('INSTAFEED_INTERACTIONS_PATH', plugin_dir_path(__FILE__));
define('INSTAFEED_INTERACTIONS_VERSION', '1.0.0');

// Include required files
require_once INSTAFEED_INTERACTIONS_PATH . 'includes/class-instafeed-interactions-core.php';
require_once INSTAFEED_INTERACTIONS_PATH . 'includes/class-instafeed-interactions-ajax.php';
require_once INSTAFEED_INTERACTIONS_PATH . 'includes/class-instafeed-interactions-admin.php';

/**
 * Main InstaFeed Interactions Plugin Class
 */
class InstaFeed_Interactions_Plugin {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Initialize plugin components
        new InstaFeed_Interactions_Core();
        new InstaFeed_Interactions_Ajax();
        new InstaFeed_Interactions_Admin();
        
        // Load text domain
        load_plugin_textdomain('instafeed-interactions', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Add interaction capabilities
        $this->add_interaction_capabilities();
    }
    
    public function enqueue_scripts() {
        // Enqueue CSS
        wp_enqueue_style(
            'instafeed-interactions-style',
            INSTAFEED_INTERACTIONS_URL . 'assets/css/instafeed-interactions.css',
            array(),
            INSTAFEED_INTERACTIONS_VERSION
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'instafeed-interactions-script',
            INSTAFEED_INTERACTIONS_URL . 'assets/js/instafeed-interactions.js',
            array('jquery'),
            INSTAFEED_INTERACTIONS_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('instafeed-interactions-script', 'instafeed_interactions_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('instafeed_interactions_nonce'),
            'user_id' => get_current_user_id(),
            'is_logged_in' => is_user_logged_in(),
            'strings' => array(
                'login_required' => __('Please log in to interact with posts', 'instafeed-interactions'),
                'like_added' => __('Post liked!', 'instafeed-interactions'),
                'like_removed' => __('Like removed', 'instafeed-interactions'),
                'dislike_added' => __('Post disliked', 'instafeed-interactions'),
                'dislike_removed' => __('Dislike removed', 'instafeed-interactions'),
                'bookmark_added' => __('Post bookmarked!', 'instafeed-interactions'),
                'bookmark_removed' => __('Bookmark removed', 'instafeed-interactions'),
                'link_copied' => __('Link copied to clipboard!', 'instafeed-interactions'),
                'share_failed' => __('Sharing failed', 'instafeed-interactions'),
                'error_occurred' => __('An error occurred', 'instafeed-interactions'),
            ),
            'settings' => array(
                'enable_likes' => get_option('instafeed_interactions_enable_likes', 1),
                'enable_dislikes' => get_option('instafeed_interactions_enable_dislikes', 1),
                'enable_comments' => get_option('instafeed_interactions_enable_comments', 1),
                'enable_shares' => get_option('instafeed_interactions_enable_shares', 1),
                'enable_bookmarks' => get_option('instafeed_interactions_enable_bookmarks', 1),
                'require_login' => get_option('instafeed_interactions_require_login', 1),
                'show_counts' => get_option('instafeed_interactions_show_counts', 1),
                'animation_enabled' => get_option('instafeed_interactions_animations', 1),
            )
        ));
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'instafeed-interactions') !== false) {
            wp_enqueue_style('instafeed-interactions-admin', INSTAFEED_INTERACTIONS_URL . 'assets/css/admin.css', array(), INSTAFEED_INTERACTIONS_VERSION);
            wp_enqueue_script('instafeed-interactions-admin', INSTAFEED_INTERACTIONS_URL . 'assets/js/admin.js', array('jquery'), INSTAFEED_INTERACTIONS_VERSION, true);
        }
    }
    
    public function activate() {
        // Create custom tables
        $this->create_tables();
        
        // Set default options
        add_option('instafeed_interactions_enabled', 1);
        add_option('instafeed_interactions_enable_likes', 1);
        add_option('instafeed_interactions_enable_dislikes', 1);
        add_option('instafeed_interactions_enable_comments', 1);
        add_option('instafeed_interactions_enable_shares', 1);
        add_option('instafeed_interactions_enable_bookmarks', 1);
        add_option('instafeed_interactions_require_login', 1);
        add_option('instafeed_interactions_show_counts', 1);
        add_option('instafeed_interactions_animations', 1);
        add_option('instafeed_interactions_moderate_interactions', 0);
        add_option('instafeed_interactions_rate_limit', 10); // interactions per minute
        add_option('instafeed_interactions_auto_approve', 1);
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    private function add_interaction_capabilities() {
        $roles = array('subscriber', 'author', 'editor', 'administrator');
        
        foreach ($roles as $role_name) {
            $role = get_role($role_name);
            if ($role) {
                $role->add_cap('instafeed_like_posts');
                $role->add_cap('instafeed_dislike_posts');
                $role->add_cap('instafeed_bookmark_posts');
                $role->add_cap('instafeed_share_posts');
                $role->add_cap('instafeed_comment_posts');
            }
        }
    }
    
    private function create_tables() {
        global $wpdb;
        
        // Post interactions table
        $interactions_table = $wpdb->prefix . 'instafeed_post_interactions';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql1 = "CREATE TABLE $interactions_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            post_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            interaction_type varchar(20) NOT NULL,
            interaction_value tinyint(1) DEFAULT 1,
            ip_address varchar(45),
            user_agent text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_interaction (post_id, user_id, interaction_type),
            KEY post_id (post_id),
            KEY user_id (user_id),
            KEY interaction_type (interaction_type),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // Interaction statistics table
        $stats_table = $wpdb->prefix . 'instafeed_interaction_stats';
        $sql2 = "CREATE TABLE $stats_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            post_id bigint(20) NOT NULL,
            likes_count int(11) DEFAULT 0,
            dislikes_count int(11) DEFAULT 0,
            comments_count int(11) DEFAULT 0,
            shares_count int(11) DEFAULT 0,
            bookmarks_count int(11) DEFAULT 0,
            total_interactions int(11) DEFAULT 0,
            last_interaction datetime,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY post_id (post_id),
            KEY total_interactions (total_interactions),
            KEY last_interaction (last_interaction)
        ) $charset_collate;";
        
        // User interaction history table
        $history_table = $wpdb->prefix . 'instafeed_user_interaction_history';
        $sql3 = "CREATE TABLE $history_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            post_id bigint(20) NOT NULL,
            interaction_type varchar(20) NOT NULL,
            action varchar(20) NOT NULL,
            previous_value tinyint(1),
            new_value tinyint(1),
            ip_address varchar(45),
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY post_id (post_id),
            KEY interaction_type (interaction_type),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql1);
        dbDelta($sql2);
        dbDelta($sql3);
    }
}

// Initialize the plugin
function instafeed_interactions_init() {
    return InstaFeed_Interactions_Plugin::get_instance();
}

// Start the plugin
instafeed_interactions_init();
