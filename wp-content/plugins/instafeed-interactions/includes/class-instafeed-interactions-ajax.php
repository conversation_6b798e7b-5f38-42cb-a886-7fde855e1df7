<?php
/**
 * InstaFeed Interactions AJAX Class
 * Handles all AJAX requests for user interactions
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Interactions_Ajax {
    
    public function __construct() {
        // Interaction handlers
        add_action('wp_ajax_instafeed_toggle_interaction', array($this, 'toggle_interaction'));
        add_action('wp_ajax_nopriv_instafeed_toggle_interaction', array($this, 'toggle_interaction'));
        add_action('wp_ajax_instafeed_get_interaction_counts', array($this, 'get_interaction_counts'));
        add_action('wp_ajax_nopriv_instafeed_get_interaction_counts', array($this, 'get_interaction_counts'));
        add_action('wp_ajax_instafeed_share_post', array($this, 'share_post'));
        add_action('wp_ajax_nopriv_instafeed_share_post', array($this, 'share_post'));
    }
    
    public function toggle_interaction() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_interactions_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $post_id = intval($_POST['post_id']);
        $interaction_type = sanitize_text_field($_POST['interaction_type']);
        $user_id = get_current_user_id();
        
        // Check if login is required
        if (get_option('instafeed_interactions_require_login', 1) && !$user_id) {
            wp_send_json_error('Please log in to interact with posts');
        }
        
        // Validate interaction type
        $allowed_types = array('like', 'dislike', 'bookmark');
        if (!in_array($interaction_type, $allowed_types)) {
            wp_send_json_error('Invalid interaction type');
        }
        
        // Check if interaction is enabled
        if (!get_option('instafeed_interactions_enable_' . $interaction_type . 's', 1)) {
            wp_send_json_error('This interaction is disabled');
        }
        
        // Check user capabilities
        if (!current_user_can('instafeed_' . $interaction_type . '_posts')) {
            wp_send_json_error('You do not have permission for this interaction');
        }
        
        // Check rate limiting
        if ($this->is_rate_limited($user_id)) {
            wp_send_json_error('Too many interactions. Please slow down.');
        }
        
        global $wpdb;
        $interactions_table = $wpdb->prefix . 'instafeed_post_interactions';
        
        // Get current interaction
        $current_interaction = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $interactions_table WHERE post_id = %d AND user_id = %d AND interaction_type = %s",
            $post_id, $user_id, $interaction_type
        ));
        
        $new_value = 1;
        $action = 'added';
        $previous_value = 0;
        
        if ($current_interaction) {
            // Toggle existing interaction
            $new_value = $current_interaction->interaction_value ? 0 : 1;
            $action = $new_value ? 'added' : 'removed';
            $previous_value = $current_interaction->interaction_value;
            
            $wpdb->update(
                $interactions_table,
                array(
                    'interaction_value' => $new_value,
                    'updated_at' => current_time('mysql')
                ),
                array(
                    'post_id' => $post_id,
                    'user_id' => $user_id,
                    'interaction_type' => $interaction_type
                ),
                array('%d', '%s'),
                array('%d', '%d', '%s')
            );
        } else {
            // Create new interaction
            $wpdb->insert(
                $interactions_table,
                array(
                    'post_id' => $post_id,
                    'user_id' => $user_id,
                    'interaction_type' => $interaction_type,
                    'interaction_value' => $new_value,
                    'ip_address' => $this->get_user_ip(),
                    'user_agent' => substr($_SERVER['HTTP_USER_AGENT'], 0, 255),
                    'created_at' => current_time('mysql')
                ),
                array('%d', '%d', '%s', '%d', '%s', '%s', '%s')
            );
        }
        
        // Handle mutual exclusivity (like/dislike)
        if ($interaction_type === 'like' && $new_value) {
            $this->remove_conflicting_interaction($post_id, $user_id, 'dislike');
        } elseif ($interaction_type === 'dislike' && $new_value) {
            $this->remove_conflicting_interaction($post_id, $user_id, 'like');
        }
        
        // Update statistics
        $core = new InstaFeed_Interactions_Core();
        $core->update_interaction_stats($post_id);
        
        // Log interaction history
        $core->log_interaction_history($user_id, $post_id, $interaction_type, $action, $previous_value, $new_value);
        
        // Get updated counts
        $stats = $core->get_post_interaction_stats($post_id);
        $user_interactions = $core->get_user_interactions($post_id, $user_id);
        
        wp_send_json_success(array(
            'action' => $action,
            'interaction_type' => $interaction_type,
            'new_value' => $new_value,
            'stats' => $stats,
            'user_interactions' => $user_interactions,
            'message' => $this->get_interaction_message($interaction_type, $action)
        ));
    }
    
    public function get_interaction_counts() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_interactions_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $post_id = intval($_POST['post_id']);
        $user_id = get_current_user_id();
        
        $core = new InstaFeed_Interactions_Core();
        $stats = $core->get_post_interaction_stats($post_id);
        $user_interactions = $core->get_user_interactions($post_id, $user_id);
        
        wp_send_json_success(array_merge($stats, array(
            'user_interactions' => $user_interactions
        )));
    }
    
    public function share_post() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_interactions_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $post_id = intval($_POST['post_id']);
        $user_id = get_current_user_id();
        
        // Check if sharing is enabled
        if (!get_option('instafeed_interactions_enable_shares', 1)) {
            wp_send_json_error('Sharing is disabled');
        }
        
        // Log share interaction
        global $wpdb;
        $interactions_table = $wpdb->prefix . 'instafeed_post_interactions';
        
        $wpdb->insert(
            $interactions_table,
            array(
                'post_id' => $post_id,
                'user_id' => $user_id ?: 0,
                'interaction_type' => 'share',
                'interaction_value' => 1,
                'ip_address' => $this->get_user_ip(),
                'user_agent' => substr($_SERVER['HTTP_USER_AGENT'], 0, 255),
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%d', '%s', '%s', '%s')
        );
        
        // Update statistics
        $core = new InstaFeed_Interactions_Core();
        $core->update_interaction_stats($post_id);
        
        // Log activity
        if ($user_id) {
            $core->log_interaction_history($user_id, $post_id, 'share', 'shared', 0, 1);
        }
        
        wp_send_json_success(array(
            'message' => __('Post shared!', 'instafeed-interactions'),
            'post_url' => get_permalink($post_id),
            'post_title' => get_the_title($post_id)
        ));
    }
    
    private function remove_conflicting_interaction($post_id, $user_id, $conflict_type) {
        global $wpdb;
        $interactions_table = $wpdb->prefix . 'instafeed_post_interactions';
        
        $wpdb->update(
            $interactions_table,
            array('interaction_value' => 0, 'updated_at' => current_time('mysql')),
            array(
                'post_id' => $post_id,
                'user_id' => $user_id,
                'interaction_type' => $conflict_type
            ),
            array('%d', '%s'),
            array('%d', '%d', '%s')
        );
    }
    
    private function is_rate_limited($user_id) {
        if (!$user_id) {
            return false; // No rate limiting for guests
        }
        
        $rate_limit = get_option('instafeed_interactions_rate_limit', 10);
        if ($rate_limit <= 0) {
            return false; // Rate limiting disabled
        }
        
        global $wpdb;
        $interactions_table = $wpdb->prefix . 'instafeed_post_interactions';
        
        $recent_interactions = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $interactions_table 
             WHERE user_id = %d AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)",
            $user_id
        ));
        
        return $recent_interactions >= $rate_limit;
    }
    
    private function get_interaction_message($interaction_type, $action) {
        $messages = array(
            'like' => array(
                'added' => __('Post liked!', 'instafeed-interactions'),
                'removed' => __('Like removed', 'instafeed-interactions')
            ),
            'dislike' => array(
                'added' => __('Post disliked', 'instafeed-interactions'),
                'removed' => __('Dislike removed', 'instafeed-interactions')
            ),
            'bookmark' => array(
                'added' => __('Post bookmarked!', 'instafeed-interactions'),
                'removed' => __('Bookmark removed', 'instafeed-interactions')
            )
        );
        
        return $messages[$interaction_type][$action] ?? __('Interaction updated', 'instafeed-interactions');
    }
    
    private function get_user_ip() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
}
