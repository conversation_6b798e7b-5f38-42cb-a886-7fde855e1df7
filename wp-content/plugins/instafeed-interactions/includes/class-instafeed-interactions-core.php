<?php
/**
 * InstaFeed Interactions Core Class
 * Handles core interaction functionality and display
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Interactions_Core {
    
    public function __construct() {
        add_action('wp_footer', array($this, 'inject_interactions'));
        add_action('wp_head', array($this, 'add_interaction_meta'));
        add_shortcode('instafeed_interactions', array($this, 'interactions_shortcode'));
        add_filter('the_content', array($this, 'add_post_interactions'));
    }
    
    public function inject_interactions() {
        if (!get_option('instafeed_interactions_enabled', 1)) {
            return;
        }
        
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Find all interaction placeholders and inject interactions
            const placeholders = document.querySelectorAll('.instafeed-interactions-placeholder');
            placeholders.forEach(function(placeholder) {
                const postId = placeholder.getAttribute('data-post-id');
                if (postId) {
                    injectPostInteractions(placeholder, postId);
                }
            });
        });
        
        function injectPostInteractions(placeholder, postId) {
            const settings = instafeed_interactions_ajax.settings;
            const isLoggedIn = instafeed_interactions_ajax.is_logged_in;
            
            // Get interaction counts
            fetch(instafeed_interactions_ajax.ajax_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'instafeed_get_interaction_counts',
                    nonce: instafeed_interactions_ajax.nonce,
                    post_id: postId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const counts = data.data;
                    const userInteractions = data.data.user_interactions || {};
                    
                    let interactionsHTML = '<div class="instafeed-post-interactions">';
                    interactionsHTML += '<div class="instafeed-interaction-left">';
                    
                    // Like button
                    if (settings.enable_likes) {
                        const isLiked = userInteractions.like || false;
                        const likeClass = isLiked ? 'instafeed-interaction-btn liked' : 'instafeed-interaction-btn';
                        const likeIcon = isLiked ? '❤️' : '🤍';
                        
                        interactionsHTML += `
                            <button class="${likeClass}" data-post-id="${postId}" data-interaction="like" onclick="handleInteraction(${postId}, 'like')">
                                <span class="instafeed-interaction-icon">${likeIcon}</span>
                                ${settings.show_counts ? `<span class="instafeed-interaction-count">${counts.likes_count || 0}</span>` : ''}
                            </button>
                        `;
                    }
                    
                    // Dislike button
                    if (settings.enable_dislikes) {
                        const isDisliked = userInteractions.dislike || false;
                        const dislikeClass = isDisliked ? 'instafeed-interaction-btn disliked' : 'instafeed-interaction-btn';
                        const dislikeIcon = isDisliked ? '👎🏻' : '👎';
                        
                        interactionsHTML += `
                            <button class="${dislikeClass}" data-post-id="${postId}" data-interaction="dislike" onclick="handleInteraction(${postId}, 'dislike')">
                                <span class="instafeed-interaction-icon">${dislikeIcon}</span>
                                ${settings.show_counts ? `<span class="instafeed-interaction-count">${counts.dislikes_count || 0}</span>` : ''}
                            </button>
                        `;
                    }
                    
                    // Comment button
                    if (settings.enable_comments) {
                        interactionsHTML += `
                            <button class="instafeed-interaction-btn" onclick="scrollToComments(${postId})">
                                <span class="instafeed-interaction-icon">💬</span>
                                ${settings.show_counts ? `<span class="instafeed-interaction-count">${counts.comments_count || 0}</span>` : ''}
                            </button>
                        `;
                    }
                    
                    // Share button
                    if (settings.enable_shares) {
                        interactionsHTML += `
                            <button class="instafeed-interaction-btn" onclick="sharePost(${postId})">
                                <span class="instafeed-interaction-icon">📤</span>
                            </button>
                        `;
                    }
                    
                    interactionsHTML += '</div>';
                    interactionsHTML += '<div class="instafeed-interaction-right">';
                    
                    // Bookmark button
                    if (settings.enable_bookmarks) {
                        const isBookmarked = userInteractions.bookmark || false;
                        const bookmarkClass = isBookmarked ? 'instafeed-interaction-btn bookmarked' : 'instafeed-interaction-btn';
                        const bookmarkIcon = isBookmarked ? '📌' : '🔖';
                        
                        interactionsHTML += `
                            <button class="${bookmarkClass}" data-post-id="${postId}" data-interaction="bookmark" onclick="handleInteraction(${postId}, 'bookmark')">
                                <span class="instafeed-interaction-icon">${bookmarkIcon}</span>
                            </button>
                        `;
                    }
                    
                    interactionsHTML += '</div>';
                    interactionsHTML += '</div>';
                    
                    placeholder.innerHTML = interactionsHTML;
                }
            })
            .catch(error => {
                console.error('Error loading interactions:', error);
            });
        }
        </script>
        <?php
    }
    
    public function add_interaction_meta() {
        if (is_single()) {
            $post_id = get_the_ID();
            $stats = $this->get_post_interaction_stats($post_id);
            
            echo '<meta name="instafeed-post-likes" content="' . ($stats['likes_count'] ?? 0) . '">';
            echo '<meta name="instafeed-post-dislikes" content="' . ($stats['dislikes_count'] ?? 0) . '">';
            echo '<meta name="instafeed-post-comments" content="' . ($stats['comments_count'] ?? 0) . '">';
        }
    }
    
    public function interactions_shortcode($atts) {
        $atts = shortcode_atts(array(
            'post_id' => get_the_ID(),
            'show_counts' => true,
            'style' => 'horizontal'
        ), $atts);
        
        if (!$atts['post_id']) {
            return '';
        }
        
        return $this->render_interactions($atts['post_id'], $atts);
    }
    
    public function add_post_interactions($content) {
        if (is_single() && get_option('instafeed_interactions_auto_inject', 0)) {
            $interactions = $this->render_interactions(get_the_ID());
            $content .= $interactions;
        }
        
        return $content;
    }
    
    public function render_interactions($post_id, $args = array()) {
        $defaults = array(
            'show_counts' => get_option('instafeed_interactions_show_counts', 1),
            'style' => 'horizontal',
            'size' => 'medium'
        );
        
        $args = wp_parse_args($args, $defaults);
        $stats = $this->get_post_interaction_stats($post_id);
        $user_interactions = $this->get_user_interactions($post_id, get_current_user_id());
        
        ob_start();
        ?>
        <div class="instafeed-interactions-container" data-post-id="<?php echo $post_id; ?>">
            <div class="instafeed-post-interactions <?php echo esc_attr($args['style']); ?> <?php echo esc_attr($args['size']); ?>">
                <div class="instafeed-interaction-left">
                    <?php if (get_option('instafeed_interactions_enable_likes', 1)) : ?>
                        <button class="instafeed-interaction-btn <?php echo $user_interactions['like'] ? 'liked' : ''; ?>" 
                                data-post-id="<?php echo $post_id; ?>" 
                                data-interaction="like" 
                                onclick="handleInteraction(<?php echo $post_id; ?>, 'like')">
                            <span class="instafeed-interaction-icon"><?php echo $user_interactions['like'] ? '❤️' : '🤍'; ?></span>
                            <?php if ($args['show_counts']) : ?>
                                <span class="instafeed-interaction-count"><?php echo $stats['likes_count'] ?? 0; ?></span>
                            <?php endif; ?>
                        </button>
                    <?php endif; ?>
                    
                    <?php if (get_option('instafeed_interactions_enable_dislikes', 1)) : ?>
                        <button class="instafeed-interaction-btn <?php echo $user_interactions['dislike'] ? 'disliked' : ''; ?>" 
                                data-post-id="<?php echo $post_id; ?>" 
                                data-interaction="dislike" 
                                onclick="handleInteraction(<?php echo $post_id; ?>, 'dislike')">
                            <span class="instafeed-interaction-icon"><?php echo $user_interactions['dislike'] ? '👎🏻' : '👎'; ?></span>
                            <?php if ($args['show_counts']) : ?>
                                <span class="instafeed-interaction-count"><?php echo $stats['dislikes_count'] ?? 0; ?></span>
                            <?php endif; ?>
                        </button>
                    <?php endif; ?>
                    
                    <?php if (get_option('instafeed_interactions_enable_comments', 1)) : ?>
                        <button class="instafeed-interaction-btn" onclick="scrollToComments(<?php echo $post_id; ?>)">
                            <span class="instafeed-interaction-icon">💬</span>
                            <?php if ($args['show_counts']) : ?>
                                <span class="instafeed-interaction-count"><?php echo get_comments_number($post_id); ?></span>
                            <?php endif; ?>
                        </button>
                    <?php endif; ?>
                    
                    <?php if (get_option('instafeed_interactions_enable_shares', 1)) : ?>
                        <button class="instafeed-interaction-btn" onclick="sharePost(<?php echo $post_id; ?>)">
                            <span class="instafeed-interaction-icon">📤</span>
                        </button>
                    <?php endif; ?>
                </div>
                
                <div class="instafeed-interaction-right">
                    <?php if (get_option('instafeed_interactions_enable_bookmarks', 1)) : ?>
                        <button class="instafeed-interaction-btn <?php echo $user_interactions['bookmark'] ? 'bookmarked' : ''; ?>" 
                                data-post-id="<?php echo $post_id; ?>" 
                                data-interaction="bookmark" 
                                onclick="handleInteraction(<?php echo $post_id; ?>, 'bookmark')">
                            <span class="instafeed-interaction-icon"><?php echo $user_interactions['bookmark'] ? '📌' : '🔖'; ?></span>
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php
        
        return ob_get_clean();
    }
    
    public function get_post_interaction_stats($post_id) {
        global $wpdb;
        
        $stats_table = $wpdb->prefix . 'instafeed_interaction_stats';
        $stats = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $stats_table WHERE post_id = %d",
            $post_id
        ), ARRAY_A);
        
        if (!$stats) {
            // Create default stats
            $default_stats = array(
                'post_id' => $post_id,
                'likes_count' => 0,
                'dislikes_count' => 0,
                'comments_count' => get_comments_number($post_id),
                'shares_count' => 0,
                'bookmarks_count' => 0,
                'total_interactions' => 0
            );
            
            $wpdb->insert($stats_table, $default_stats);
            return $default_stats;
        }
        
        return $stats;
    }
    
    public function get_user_interactions($post_id, $user_id) {
        if (!$user_id) {
            return array();
        }
        
        global $wpdb;
        
        $interactions_table = $wpdb->prefix . 'instafeed_post_interactions';
        $interactions = $wpdb->get_results($wpdb->prepare(
            "SELECT interaction_type, interaction_value FROM $interactions_table 
             WHERE post_id = %d AND user_id = %d",
            $post_id, $user_id
        ));
        
        $user_interactions = array();
        foreach ($interactions as $interaction) {
            $user_interactions[$interaction->interaction_type] = (bool) $interaction->interaction_value;
        }
        
        return $user_interactions;
    }
    
    public function update_interaction_stats($post_id) {
        global $wpdb;
        
        $interactions_table = $wpdb->prefix . 'instafeed_post_interactions';
        $stats_table = $wpdb->prefix . 'instafeed_interaction_stats';
        
        // Count interactions
        $likes = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $interactions_table WHERE post_id = %d AND interaction_type = 'like' AND interaction_value = 1",
            $post_id
        ));
        
        $dislikes = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $interactions_table WHERE post_id = %d AND interaction_type = 'dislike' AND interaction_value = 1",
            $post_id
        ));
        
        $bookmarks = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $interactions_table WHERE post_id = %d AND interaction_type = 'bookmark' AND interaction_value = 1",
            $post_id
        ));
        
        $shares = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $interactions_table WHERE post_id = %d AND interaction_type = 'share'",
            $post_id
        ));
        
        $comments = get_comments_number($post_id);
        $total = $likes + $dislikes + $bookmarks + $shares + $comments;
        
        // Update or insert stats
        $wpdb->query($wpdb->prepare(
            "INSERT INTO $stats_table (post_id, likes_count, dislikes_count, comments_count, shares_count, bookmarks_count, total_interactions, last_interaction) 
             VALUES (%d, %d, %d, %d, %d, %d, %d, NOW()) 
             ON DUPLICATE KEY UPDATE 
             likes_count = %d, dislikes_count = %d, comments_count = %d, shares_count = %d, bookmarks_count = %d, total_interactions = %d, last_interaction = NOW()",
            $post_id, $likes, $dislikes, $comments, $shares, $bookmarks, $total,
            $likes, $dislikes, $comments, $shares, $bookmarks, $total
        ));
    }

    public function log_interaction_history($user_id, $post_id, $interaction_type, $action, $previous_value = null, $new_value = null) {
        global $wpdb;

        $history_table = $wpdb->prefix . 'instafeed_user_interaction_history';

        $wpdb->insert(
            $history_table,
            array(
                'user_id' => $user_id,
                'post_id' => $post_id,
                'interaction_type' => $interaction_type,
                'action' => $action,
                'previous_value' => $previous_value,
                'new_value' => $new_value,
                'ip_address' => $this->get_user_ip(),
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%d', '%d', '%s', '%s')
        );
    }

    private function get_user_ip() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
}
