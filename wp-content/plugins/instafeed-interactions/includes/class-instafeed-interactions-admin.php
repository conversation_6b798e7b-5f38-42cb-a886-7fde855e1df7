<?php
/**
 * InstaFeed Interactions Admin Class
 * Handles admin interface and settings for interactions
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Interactions_Admin {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
        add_action('admin_notices', array($this, 'admin_notices'));
    }
    
    public function add_admin_menu() {
        add_options_page(
            __('InstaFeed Interactions Settings', 'instafeed-interactions'),
            __('InstaFeed Interactions', 'instafeed-interactions'),
            'manage_options',
            'instafeed-interactions-settings',
            array($this, 'settings_page')
        );
        
        add_management_page(
            __('InstaFeed Interaction Analytics', 'instafeed-interactions'),
            __('Interaction Analytics', 'instafeed-interactions'),
            'manage_options',
            'instafeed-interaction-analytics',
            array($this, 'analytics_page')
        );
    }
    
    public function init_settings() {
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_enabled');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_enable_likes');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_enable_dislikes');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_enable_comments');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_enable_shares');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_enable_bookmarks');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_require_login');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_show_counts');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_animations');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_rate_limit');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_moderate_interactions');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_auto_inject');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_guest_interactions');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_notification_system');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_interaction_sounds');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_double_tap_like');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_interaction_history');
        register_setting('instafeed_interactions_settings', 'instafeed_interactions_bulk_actions');
    }
    
    public function admin_notices() {
        if (isset($_GET['page']) && $_GET['page'] === 'instafeed-interactions-settings') {
            if (isset($_GET['settings-updated']) && $_GET['settings-updated']) {
                echo '<div class="notice notice-success is-dismissible"><p>' . 
                     __('InstaFeed Interactions settings saved!', 'instafeed-interactions') . '</p></div>';
            }
        }
    }
    
    public function settings_page() {
        if (isset($_POST['submit'])) {
            update_option('instafeed_interactions_enabled', isset($_POST['instafeed_interactions_enabled']) ? 1 : 0);
            update_option('instafeed_interactions_enable_likes', isset($_POST['instafeed_interactions_enable_likes']) ? 1 : 0);
            update_option('instafeed_interactions_enable_dislikes', isset($_POST['instafeed_interactions_enable_dislikes']) ? 1 : 0);
            update_option('instafeed_interactions_enable_comments', isset($_POST['instafeed_interactions_enable_comments']) ? 1 : 0);
            update_option('instafeed_interactions_enable_shares', isset($_POST['instafeed_interactions_enable_shares']) ? 1 : 0);
            update_option('instafeed_interactions_enable_bookmarks', isset($_POST['instafeed_interactions_enable_bookmarks']) ? 1 : 0);
            update_option('instafeed_interactions_require_login', isset($_POST['instafeed_interactions_require_login']) ? 1 : 0);
            update_option('instafeed_interactions_show_counts', isset($_POST['instafeed_interactions_show_counts']) ? 1 : 0);
            update_option('instafeed_interactions_animations', isset($_POST['instafeed_interactions_animations']) ? 1 : 0);
            update_option('instafeed_interactions_rate_limit', intval($_POST['instafeed_interactions_rate_limit']));
            update_option('instafeed_interactions_moderate_interactions', isset($_POST['instafeed_interactions_moderate_interactions']) ? 1 : 0);
            update_option('instafeed_interactions_auto_inject', isset($_POST['instafeed_interactions_auto_inject']) ? 1 : 0);
            update_option('instafeed_interactions_guest_interactions', isset($_POST['instafeed_interactions_guest_interactions']) ? 1 : 0);
            update_option('instafeed_interactions_notification_system', isset($_POST['instafeed_interactions_notification_system']) ? 1 : 0);
            update_option('instafeed_interactions_interaction_sounds', isset($_POST['instafeed_interactions_interaction_sounds']) ? 1 : 0);
            update_option('instafeed_interactions_double_tap_like', isset($_POST['instafeed_interactions_double_tap_like']) ? 1 : 0);
            update_option('instafeed_interactions_interaction_history', isset($_POST['instafeed_interactions_interaction_history']) ? 1 : 0);
            update_option('instafeed_interactions_bulk_actions', isset($_POST['instafeed_interactions_bulk_actions']) ? 1 : 0);
            
            echo '<div class="notice notice-success"><p>' . __('Settings saved!', 'instafeed-interactions') . '</p></div>';
        }
        
        $enabled = get_option('instafeed_interactions_enabled', 1);
        $enable_likes = get_option('instafeed_interactions_enable_likes', 1);
        $enable_dislikes = get_option('instafeed_interactions_enable_dislikes', 1);
        $enable_comments = get_option('instafeed_interactions_enable_comments', 1);
        $enable_shares = get_option('instafeed_interactions_enable_shares', 1);
        $enable_bookmarks = get_option('instafeed_interactions_enable_bookmarks', 1);
        $require_login = get_option('instafeed_interactions_require_login', 1);
        $show_counts = get_option('instafeed_interactions_show_counts', 1);
        $animations = get_option('instafeed_interactions_animations', 1);
        $rate_limit = get_option('instafeed_interactions_rate_limit', 10);
        $moderate_interactions = get_option('instafeed_interactions_moderate_interactions', 0);
        $auto_inject = get_option('instafeed_interactions_auto_inject', 0);
        $guest_interactions = get_option('instafeed_interactions_guest_interactions', 0);
        $notification_system = get_option('instafeed_interactions_notification_system', 1);
        $interaction_sounds = get_option('instafeed_interactions_interaction_sounds', 0);
        $double_tap_like = get_option('instafeed_interactions_double_tap_like', 1);
        $interaction_history = get_option('instafeed_interactions_interaction_history', 1);
        $bulk_actions = get_option('instafeed_interactions_bulk_actions', 1);
        
        ?>
        <div class="wrap">
            <h1><?php _e('InstaFeed Interactions Settings', 'instafeed-interactions'); ?></h1>
            
            <div class="card">
                <h2><?php _e('User Interaction System', 'instafeed-interactions'); ?></h2>
                <p><?php _e('Configure the Instagram-style interaction system for your posts.', 'instafeed-interactions'); ?></p>
                
                <form method="post" action="">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Interactions', 'instafeed-interactions'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_interactions_enabled" value="1" <?php checked($enabled); ?> />
                                    <?php _e('Enable InstaFeed interaction system', 'instafeed-interactions'); ?>
                                </label>
                                <p class="description"><?php _e('Master switch for all interactions', 'instafeed-interactions'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Interaction Types', 'instafeed-interactions'); ?></th>
                            <td>
                                <fieldset>
                                    <label>
                                        <input type="checkbox" name="instafeed_interactions_enable_likes" value="1" <?php checked($enable_likes); ?> />
                                        <?php _e('Enable Likes ❤️', 'instafeed-interactions'); ?>
                                    </label><br>
                                    
                                    <label>
                                        <input type="checkbox" name="instafeed_interactions_enable_dislikes" value="1" <?php checked($enable_dislikes); ?> />
                                        <?php _e('Enable Dislikes 👎', 'instafeed-interactions'); ?>
                                    </label><br>
                                    
                                    <label>
                                        <input type="checkbox" name="instafeed_interactions_enable_comments" value="1" <?php checked($enable_comments); ?> />
                                        <?php _e('Enable Comments 💬', 'instafeed-interactions'); ?>
                                    </label><br>
                                    
                                    <label>
                                        <input type="checkbox" name="instafeed_interactions_enable_shares" value="1" <?php checked($enable_shares); ?> />
                                        <?php _e('Enable Shares 📤', 'instafeed-interactions'); ?>
                                    </label><br>
                                    
                                    <label>
                                        <input type="checkbox" name="instafeed_interactions_enable_bookmarks" value="1" <?php checked($enable_bookmarks); ?> />
                                        <?php _e('Enable Bookmarks 🔖', 'instafeed-interactions'); ?>
                                    </label>
                                </fieldset>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('User Requirements', 'instafeed-interactions'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_interactions_require_login" value="1" <?php checked($require_login); ?> />
                                    <?php _e('Require login for interactions', 'instafeed-interactions'); ?>
                                </label>
                                <p class="description"><?php _e('If unchecked, guests can interact with posts', 'instafeed-interactions'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Display Options', 'instafeed-interactions'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_interactions_show_counts" value="1" <?php checked($show_counts); ?> />
                                    <?php _e('Show interaction counts', 'instafeed-interactions'); ?>
                                </label><br>
                                
                                <label>
                                    <input type="checkbox" name="instafeed_interactions_animations" value="1" <?php checked($animations); ?> />
                                    <?php _e('Enable animations', 'instafeed-interactions'); ?>
                                </label><br>
                                
                                <label>
                                    <input type="checkbox" name="instafeed_interactions_auto_inject" value="1" <?php checked($auto_inject); ?> />
                                    <?php _e('Auto-inject interactions on single posts', 'instafeed-interactions'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Rate Limiting', 'instafeed-interactions'); ?></th>
                            <td>
                                <input type="number" name="instafeed_interactions_rate_limit" value="<?php echo $rate_limit; ?>" min="0" max="100" />
                                <p class="description"><?php _e('Maximum interactions per minute per user (0 = no limit)', 'instafeed-interactions'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Guest Interactions', 'instafeed-interactions'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_interactions_guest_interactions" value="1" <?php checked($guest_interactions); ?> />
                                    <?php _e('Allow guest users to interact with posts', 'instafeed-interactions'); ?>
                                </label>
                                <p class="description"><?php _e('Non-logged-in users can like, share, and bookmark posts', 'instafeed-interactions'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('User Experience', 'instafeed-interactions'); ?></th>
                            <td>
                                <fieldset>
                                    <label>
                                        <input type="checkbox" name="instafeed_interactions_notification_system" value="1" <?php checked($notification_system); ?> />
                                        <?php _e('Enable notification system', 'instafeed-interactions'); ?>
                                    </label><br>

                                    <label>
                                        <input type="checkbox" name="instafeed_interactions_interaction_sounds" value="1" <?php checked($interaction_sounds); ?> />
                                        <?php _e('Enable interaction sounds', 'instafeed-interactions'); ?>
                                    </label><br>

                                    <label>
                                        <input type="checkbox" name="instafeed_interactions_double_tap_like" value="1" <?php checked($double_tap_like); ?> />
                                        <?php _e('Enable double-tap to like (Instagram style)', 'instafeed-interactions'); ?>
                                    </label><br>

                                    <label>
                                        <input type="checkbox" name="instafeed_interactions_interaction_history" value="1" <?php checked($interaction_history); ?> />
                                        <?php _e('Track interaction history', 'instafeed-interactions'); ?>
                                    </label><br>

                                    <label>
                                        <input type="checkbox" name="instafeed_interactions_bulk_actions" value="1" <?php checked($bulk_actions); ?> />
                                        <?php _e('Enable bulk interaction actions', 'instafeed-interactions'); ?>
                                    </label>
                                </fieldset>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Dynamic Content', 'instafeed-interactions'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_interactions_auto_inject" value="1" <?php checked($auto_inject); ?> />
                                    <?php _e('Auto-inject interactions on dynamically loaded content', 'instafeed-interactions'); ?>
                                </label>
                                <p class="description"><?php _e('Automatically add interactions to posts loaded via AJAX or infinite scroll', 'instafeed-interactions'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Moderation', 'instafeed-interactions'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_interactions_moderate_interactions" value="1" <?php checked($moderate_interactions); ?> />
                                    <?php _e('Moderate interactions before they appear', 'instafeed-interactions'); ?>
                                </label>
                                <p class="description"><?php _e('Interactions will be held for admin approval', 'instafeed-interactions'); ?></p>
                            </td>
                        </tr>
                    </table>
                    
                    <?php submit_button(); ?>
                </form>
            </div>
            
            <div class="card">
                <h2><?php _e('Interaction Features', 'instafeed-interactions'); ?></h2>
                <p><?php _e('This interaction system includes:', 'instafeed-interactions'); ?></p>
                <ul>
                    <li>✅ <?php _e('Instagram-style like/dislike system', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Bookmark posts for later', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Share posts with native sharing', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Comment integration', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Real-time interaction counts', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Rate limiting and spam protection', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Detailed analytics and reporting', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Mobile-responsive design', 'instafeed-interactions'); ?></li>
                </ul>
            </div>
            
            <div class="card">
                <h2><?php _e('Quick Statistics', 'instafeed-interactions'); ?></h2>
                <?php $this->display_quick_stats(); ?>
            </div>

            <div class="card">
                <h2><?php _e('Dynamic Content Integration', 'instafeed-interactions'); ?></h2>
                <p><?php _e('The interaction system now automatically detects and integrates with dynamically loaded content:', 'instafeed-interactions'); ?></p>
                <ul>
                    <li>✅ <?php _e('Auto-detection of new posts loaded via AJAX', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Infinite scroll compatibility', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Load more button integration', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Real-time interaction initialization', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('MutationObserver for DOM changes', 'instafeed-interactions'); ?></li>
                    <li>✅ <?php _e('Theme compatibility layer', 'instafeed-interactions'); ?></li>
                </ul>

                <div style="background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px; padding: 15px; margin-top: 20px;">
                    <h4 style="margin: 0 0 10px; color: #0073aa;"><?php _e('How It Works', 'instafeed-interactions'); ?></h4>
                    <p style="margin: 0;"><?php _e('When your theme loads new posts (via AJAX, infinite scroll, or load more buttons), the interaction system automatically detects these new posts and adds interaction buttons to them. This ensures users can always interact with content, regardless of how it was loaded.', 'instafeed-interactions'); ?></p>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function analytics_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('InstaFeed Interaction Analytics', 'instafeed-interactions'); ?></h1>
            
            <div class="card">
                <h2><?php _e('Interaction Overview', 'instafeed-interactions'); ?></h2>
                <?php $this->display_analytics(); ?>
            </div>
            
            <div class="card">
                <h2><?php _e('Top Posts by Interactions', 'instafeed-interactions'); ?></h2>
                <?php $this->display_top_posts(); ?>
            </div>
            
            <div class="card">
                <h2><?php _e('Recent Activity', 'instafeed-interactions'); ?></h2>
                <?php $this->display_recent_activity(); ?>
            </div>
        </div>
        <?php
    }
    
    private function display_quick_stats() {
        global $wpdb;
        
        $interactions_table = $wpdb->prefix . 'instafeed_post_interactions';
        $stats_table = $wpdb->prefix . 'instafeed_interaction_stats';
        
        $total_interactions = $wpdb->get_var("SELECT COUNT(*) FROM $interactions_table");
        $total_likes = $wpdb->get_var("SELECT SUM(likes_count) FROM $stats_table");
        $total_dislikes = $wpdb->get_var("SELECT SUM(dislikes_count) FROM $stats_table");
        $total_bookmarks = $wpdb->get_var("SELECT SUM(bookmarks_count) FROM $stats_table");
        $total_shares = $wpdb->get_var("SELECT SUM(shares_count) FROM $stats_table");
        
        ?>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; margin: 20px 0;">
            <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="margin: 0 0 10px; color: #0073aa;"><?php echo number_format($total_interactions); ?></h3>
                <p style="margin: 0; color: #666;"><?php _e('Total Interactions', 'instafeed-interactions'); ?></p>
            </div>
            
            <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="margin: 0 0 10px; color: #ff3040;"><?php echo number_format($total_likes); ?></h3>
                <p style="margin: 0; color: #666;"><?php _e('Total Likes', 'instafeed-interactions'); ?></p>
            </div>
            
            <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="margin: 0 0 10px; color: #ff6b6b;"><?php echo number_format($total_dislikes); ?></h3>
                <p style="margin: 0; color: #666;"><?php _e('Total Dislikes', 'instafeed-interactions'); ?></p>
            </div>
            
            <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="margin: 0 0 10px; color: #ffd700;"><?php echo number_format($total_bookmarks); ?></h3>
                <p style="margin: 0; color: #666;"><?php _e('Total Bookmarks', 'instafeed-interactions'); ?></p>
            </div>
            
            <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="margin: 0 0 10px; color: #0095f6;"><?php echo number_format($total_shares); ?></h3>
                <p style="margin: 0; color: #666;"><?php _e('Total Shares', 'instafeed-interactions'); ?></p>
            </div>
        </div>
        <?php
    }
    
    private function display_analytics() {
        echo '<p>' . __('Detailed analytics coming soon!', 'instafeed-interactions') . '</p>';
    }
    
    private function display_top_posts() {
        echo '<p>' . __('Top posts analytics coming soon!', 'instafeed-interactions') . '</p>';
    }
    
    private function display_recent_activity() {
        echo '<p>' . __('Recent activity feed coming soon!', 'instafeed-interactions') . '</p>';
    }
}
