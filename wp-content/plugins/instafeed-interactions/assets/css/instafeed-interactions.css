/* InstaFeed Interactions Plugin Styles */

/* Post Interactions Container */
.instafeed-interactions-container {
    position: relative;
}

.instafeed-post-interactions {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 10;
}

/* Show interactions on hover */
.post-item:hover .instafeed-post-interactions,
.instafeed-interactions-container:hover .instafeed-post-interactions {
    opacity: 1;
    transform: translateY(0);
}

/* Interaction Groups */
.instafeed-interaction-left,
.instafeed-interaction-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Interaction Buttons */
.instafeed-interaction-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    gap: 5px;
    position: relative;
}

.instafeed-interaction-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.instafeed-interaction-btn:active {
    transform: scale(0.95);
}

/* Interaction Icons */
.instafeed-interaction-icon {
    font-size: 18px;
    transition: all 0.3s ease;
}

/* Interaction Counts */
.instafeed-interaction-count {
    font-size: 12px;
    font-weight: 600;
    color: #ffffff;
    margin-left: 2px;
}

/* Active States */
.instafeed-interaction-btn.liked {
    color: #ff3040;
}

.instafeed-interaction-btn.liked .instafeed-interaction-icon {
    animation: heartBeat 0.6s ease-in-out;
}

.instafeed-interaction-btn.disliked {
    color: #ff6b6b;
}

.instafeed-interaction-btn.bookmarked {
    color: #ffd700;
}

/* Animations */
@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.3); }
    50% { transform: scale(1.1); }
    75% { transform: scale(1.25); }
    100% { transform: scale(1); }
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

/* Floating Heart Animation */
.instafeed-floating-heart {
    position: absolute;
    font-size: 20px;
    color: #ff3040;
    pointer-events: none;
    animation: floatHeart 1s ease-out forwards;
    z-index: 1000;
}

@keyframes floatHeart {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-50px) scale(1.5);
        opacity: 0;
    }
}

/* Horizontal Layout */
.instafeed-post-interactions.horizontal {
    position: relative;
    background: transparent;
    backdrop-filter: none;
    padding: 15px 0;
    opacity: 1;
    transform: none;
    border-top: 1px solid #262626;
    margin-top: 15px;
}

.instafeed-post-interactions.horizontal .instafeed-interaction-btn {
    color: #8e8e8e;
    font-size: 20px;
    padding: 8px;
}

.instafeed-post-interactions.horizontal .instafeed-interaction-btn:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
}

/* Size Variations */
.instafeed-post-interactions.small .instafeed-interaction-btn {
    font-size: 14px;
    padding: 3px;
}

.instafeed-post-interactions.small .instafeed-interaction-count {
    font-size: 10px;
}

.instafeed-post-interactions.large .instafeed-interaction-btn {
    font-size: 24px;
    padding: 8px;
}

.instafeed-post-interactions.large .instafeed-interaction-count {
    font-size: 14px;
}

/* Loading States */
.instafeed-interaction-loading {
    opacity: 0.5;
    pointer-events: none;
}

.instafeed-interaction-loading .instafeed-interaction-icon {
    animation: pulse 1s infinite;
}

/* Error States */
.instafeed-interaction-error {
    color: #e74c3c !important;
}

/* Disabled States */
.instafeed-interaction-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    pointer-events: none;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .instafeed-post-interactions {
        bottom: 5px;
        left: 5px;
        right: 5px;
        padding: 6px 10px;
    }
    
    .instafeed-interaction-left,
    .instafeed-interaction-right {
        gap: 8px;
    }
    
    .instafeed-interaction-btn {
        font-size: 16px;
        padding: 4px;
    }
    
    .instafeed-interaction-count {
        font-size: 11px;
    }
    
    .instafeed-post-interactions.horizontal {
        padding: 10px 0;
    }
    
    .instafeed-post-interactions.horizontal .instafeed-interaction-btn {
        font-size: 18px;
        padding: 6px;
    }
}

@media (max-width: 480px) {
    .instafeed-interaction-left,
    .instafeed-interaction-right {
        gap: 6px;
    }
    
    .instafeed-interaction-btn {
        font-size: 14px;
        padding: 3px;
    }
    
    .instafeed-interaction-count {
        font-size: 10px;
    }
}

/* Touch Devices */
@media (hover: none) and (pointer: coarse) {
    .instafeed-post-interactions {
        opacity: 1;
        transform: translateY(0);
    }
    
    .instafeed-interaction-btn:hover {
        transform: none;
        background: rgba(255, 255, 255, 0.1);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .instafeed-post-interactions {
        background: rgba(0, 0, 0, 0.9);
        border: 1px solid #ffffff;
    }
    
    .instafeed-interaction-btn {
        border: 1px solid transparent;
    }
    
    .instafeed-interaction-btn:hover,
    .instafeed-interaction-btn:focus {
        border-color: #ffffff;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .instafeed-post-interactions,
    .instafeed-interaction-btn,
    .instafeed-interaction-icon {
        transition: none;
        animation: none;
    }
    
    .instafeed-interaction-btn:hover {
        transform: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .instafeed-post-interactions.horizontal {
        border-top-color: #404040;
    }
}

/* Print Styles */
@media print {
    .instafeed-post-interactions {
        display: none;
    }
}

/* Focus Styles for Accessibility */
.instafeed-interaction-btn:focus {
    outline: 2px solid #0095f6;
    outline-offset: 2px;
}

/* Screen Reader Only Text */
.instafeed-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Tooltip Styles */
.instafeed-interaction-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1001;
    margin-bottom: 5px;
}

.instafeed-interaction-btn:hover .instafeed-interaction-tooltip {
    opacity: 1;
    visibility: visible;
}

/* Custom Scrollbar for Interaction Lists */
.instafeed-interaction-list::-webkit-scrollbar {
    width: 6px;
}

.instafeed-interaction-list::-webkit-scrollbar-track {
    background: #262626;
}

.instafeed-interaction-list::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 3px;
}

.instafeed-interaction-list::-webkit-scrollbar-thumb:hover {
    background: #0095f6;
}
