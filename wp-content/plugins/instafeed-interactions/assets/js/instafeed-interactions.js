/**
 * InstaFeed Interactions JavaScript
 * Handles all user interaction functionality
 */

(function($) {
    'use strict';

    let isProcessing = false;

    $(document).ready(function() {
        initializeInteractions();

        // Watch for dynamically loaded content
        observeNewContent();
    });

    function initializeInteractions() {
        // Only initialize if interactions are enabled
        if (!instafeed_interactions_ajax.settings.enable_likes &&
            !instafeed_interactions_ajax.settings.enable_dislikes &&
            !instafeed_interactions_ajax.settings.enable_bookmarks) {
            return;
        }

        bindInteractionEvents();
        loadInteractionsForPosts();
    }

    function observeNewContent() {
        // Use MutationObserver to watch for new content
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // Check if the added node contains interaction placeholders
                            const placeholders = node.querySelectorAll ?
                                node.querySelectorAll('.instafeed-interactions-placeholder') : [];

                            // Also check if the node itself is a placeholder
                            if (node.classList && node.classList.contains('instafeed-interactions-placeholder')) {
                                loadPostInteractions($(node), node.getAttribute('data-post-id'));
                            }

                            // Load interactions for any placeholders found in the new content
                            placeholders.forEach(function(placeholder) {
                                const postId = placeholder.getAttribute('data-post-id');
                                if (postId) {
                                    loadPostInteractions($(placeholder), postId);
                                }
                            });
                        }
                    });
                }
            });
        });

        // Start observing the document body for changes
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('InstaFeed Interactions: Watching for dynamic content');
    }

    function bindInteractionEvents() {
        // Handle interaction button clicks
        $(document).on('click', '.instafeed-interaction-btn[data-interaction]', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const button = $(this);
            const postId = button.data('post-id');
            const interactionType = button.data('interaction');
            
            handleInteraction(postId, interactionType, button);
        });
    }

    function loadInteractionsForPosts() {
        // Find all interaction placeholders and load interactions
        $('.instafeed-interactions-placeholder').each(function() {
            const placeholder = $(this);
            const postId = placeholder.data('post-id');
            
            if (postId) {
                loadPostInteractions(placeholder, postId);
            }
        });
    }

    function loadPostInteractions(placeholder, postId) {
        $.ajax({
            url: instafeed_interactions_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_get_interaction_counts',
                nonce: instafeed_interactions_ajax.nonce,
                post_id: postId
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    const interactionsHTML = buildInteractionsHTML(postId, data);
                    placeholder.html(interactionsHTML);
                }
            },
            error: function() {
                console.error('Failed to load interactions for post', postId);
            }
        });
    }

    function buildInteractionsHTML(postId, data) {
        const settings = instafeed_interactions_ajax.settings;
        const userInteractions = data.user_interactions || {};
        
        let html = '<div class="instafeed-post-interactions">';
        html += '<div class="instafeed-interaction-left">';
        
        // Like button
        if (settings.enable_likes) {
            const isLiked = userInteractions.like || false;
            const likeClass = isLiked ? 'instafeed-interaction-btn liked' : 'instafeed-interaction-btn';
            const likeIcon = isLiked ? '❤️' : '🤍';
            
            html += `
                <button class="${likeClass}" data-post-id="${postId}" data-interaction="like" title="Like this post">
                    <span class="instafeed-interaction-icon">${likeIcon}</span>
                    ${settings.show_counts ? `<span class="instafeed-interaction-count">${data.likes_count || 0}</span>` : ''}
                    <span class="instafeed-sr-only">Like this post</span>
                </button>
            `;
        }
        
        // Dislike button
        if (settings.enable_dislikes) {
            const isDisliked = userInteractions.dislike || false;
            const dislikeClass = isDisliked ? 'instafeed-interaction-btn disliked' : 'instafeed-interaction-btn';
            const dislikeIcon = isDisliked ? '👎🏻' : '👎';
            
            html += `
                <button class="${dislikeClass}" data-post-id="${postId}" data-interaction="dislike" title="Dislike this post">
                    <span class="instafeed-interaction-icon">${dislikeIcon}</span>
                    ${settings.show_counts ? `<span class="instafeed-interaction-count">${data.dislikes_count || 0}</span>` : ''}
                    <span class="instafeed-sr-only">Dislike this post</span>
                </button>
            `;
        }
        
        // Comment button
        if (settings.enable_comments) {
            html += `
                <button class="instafeed-interaction-btn" onclick="scrollToComments(${postId})" title="View comments">
                    <span class="instafeed-interaction-icon">💬</span>
                    ${settings.show_counts ? `<span class="instafeed-interaction-count">${data.comments_count || 0}</span>` : ''}
                    <span class="instafeed-sr-only">View comments</span>
                </button>
            `;
        }
        
        // Share button
        if (settings.enable_shares) {
            html += `
                <button class="instafeed-interaction-btn" onclick="sharePost(${postId})" title="Share this post">
                    <span class="instafeed-interaction-icon">📤</span>
                    <span class="instafeed-sr-only">Share this post</span>
                </button>
            `;
        }
        
        html += '</div>';
        html += '<div class="instafeed-interaction-right">';
        
        // Bookmark button
        if (settings.enable_bookmarks) {
            const isBookmarked = userInteractions.bookmark || false;
            const bookmarkClass = isBookmarked ? 'instafeed-interaction-btn bookmarked' : 'instafeed-interaction-btn';
            const bookmarkIcon = isBookmarked ? '📌' : '🔖';
            
            html += `
                <button class="${bookmarkClass}" data-post-id="${postId}" data-interaction="bookmark" title="Bookmark this post">
                    <span class="instafeed-interaction-icon">${bookmarkIcon}</span>
                    <span class="instafeed-sr-only">Bookmark this post</span>
                </button>
            `;
        }
        
        html += '</div>';
        html += '</div>';
        
        return html;
    }

    // Global function for handling interactions
    window.handleInteraction = function(postId, interactionType, buttonElement) {
        if (isProcessing) {
            return;
        }

        // Check if user is logged in (if required)
        if (instafeed_interactions_ajax.settings.require_login && !instafeed_interactions_ajax.is_logged_in) {
            showNotification(instafeed_interactions_ajax.strings.login_required, 'error');
            return;
        }

        const button = buttonElement || $(`.instafeed-interaction-btn[data-post-id="${postId}"][data-interaction="${interactionType}"]`);
        
        if (!button.length) {
            return;
        }

        isProcessing = true;
        button.addClass('instafeed-interaction-loading');

        $.ajax({
            url: instafeed_interactions_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_toggle_interaction',
                nonce: instafeed_interactions_ajax.nonce,
                post_id: postId,
                interaction_type: interactionType
            },
            success: function(response) {
                if (response.success) {
                    updateInteractionUI(postId, interactionType, response.data);
                    
                    if (response.data.message) {
                        showNotification(response.data.message, 'success');
                    }
                    
                    // Add animation if enabled
                    if (instafeed_interactions_ajax.settings.animation_enabled && interactionType === 'like' && response.data.action === 'added') {
                        createHeartAnimation(button);
                    }
                } else {
                    showNotification(response.data || instafeed_interactions_ajax.strings.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_interactions_ajax.strings.error_occurred, 'error');
            },
            complete: function() {
                isProcessing = false;
                button.removeClass('instafeed-interaction-loading');
            }
        });
    };

    function updateInteractionUI(postId, interactionType, data) {
        const button = $(`.instafeed-interaction-btn[data-post-id="${postId}"][data-interaction="${interactionType}"]`);
        const icon = button.find('.instafeed-interaction-icon');
        const count = button.find('.instafeed-interaction-count');
        
        // Update button state
        if (data.new_value) {
            button.addClass(getActiveClass(interactionType));
            icon.text(getActiveIcon(interactionType));
        } else {
            button.removeClass(getActiveClass(interactionType));
            icon.text(getInactiveIcon(interactionType));
        }
        
        // Update count
        if (count.length && data.stats) {
            const countKey = interactionType + 's_count';
            count.text(data.stats[countKey] || 0);
        }
        
        // Handle mutual exclusivity (like/dislike)
        if (interactionType === 'like' && data.new_value) {
            updateConflictingInteraction(postId, 'dislike', false);
        } else if (interactionType === 'dislike' && data.new_value) {
            updateConflictingInteraction(postId, 'like', false);
        }
    }

    function updateConflictingInteraction(postId, conflictType, isActive) {
        const conflictButton = $(`.instafeed-interaction-btn[data-post-id="${postId}"][data-interaction="${conflictType}"]`);
        const conflictIcon = conflictButton.find('.instafeed-interaction-icon');
        
        if (isActive) {
            conflictButton.addClass(getActiveClass(conflictType));
            conflictIcon.text(getActiveIcon(conflictType));
        } else {
            conflictButton.removeClass(getActiveClass(conflictType));
            conflictIcon.text(getInactiveIcon(conflictType));
        }
    }

    function getActiveClass(interactionType) {
        const classes = {
            'like': 'liked',
            'dislike': 'disliked',
            'bookmark': 'bookmarked'
        };
        return classes[interactionType] || '';
    }

    function getActiveIcon(interactionType) {
        const icons = {
            'like': '❤️',
            'dislike': '👎🏻',
            'bookmark': '📌'
        };
        return icons[interactionType] || '';
    }

    function getInactiveIcon(interactionType) {
        const icons = {
            'like': '🤍',
            'dislike': '👎',
            'bookmark': '🔖'
        };
        return icons[interactionType] || '';
    }

    function createHeartAnimation(button) {
        const heart = $('<div class="instafeed-floating-heart">❤️</div>');
        
        // Position relative to button
        const buttonOffset = button.offset();
        heart.css({
            position: 'fixed',
            left: buttonOffset.left + button.width() / 2,
            top: buttonOffset.top,
            zIndex: 1000,
            pointerEvents: 'none'
        });
        
        $('body').append(heart);
        
        // Remove after animation
        setTimeout(() => {
            heart.remove();
        }, 1000);
    }

    // Global functions for theme integration
    window.scrollToComments = function(postId) {
        // Get post URL and navigate to comments
        const postElement = $(`.post-item[data-post-id="${postId}"], .instafeed-interactions-placeholder[data-post-id="${postId}"]`).closest('.post-item');
        const postLink = postElement.find('.post-title a').attr('href');
        
        if (postLink) {
            window.location.href = postLink + '#comments';
        }
    };

    window.sharePost = function(postId) {
        // Log share interaction
        $.ajax({
            url: instafeed_interactions_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_share_post',
                nonce: instafeed_interactions_ajax.nonce,
                post_id: postId
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    
                    // Try native sharing first
                    if (navigator.share) {
                        navigator.share({
                            title: data.post_title,
                            url: data.post_url
                        }).then(() => {
                            showNotification(instafeed_interactions_ajax.strings.link_copied, 'success');
                        }).catch(() => {
                            // Fallback to clipboard
                            copyToClipboard(data.post_url);
                        });
                    } else {
                        // Fallback to clipboard
                        copyToClipboard(data.post_url);
                    }
                } else {
                    showNotification(instafeed_interactions_ajax.strings.share_failed, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_interactions_ajax.strings.share_failed, 'error');
            }
        });
    };

    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification(instafeed_interactions_ajax.strings.link_copied, 'success');
            }).catch(() => {
                fallbackCopyToClipboard(text);
            });
        } else {
            fallbackCopyToClipboard(text);
        }
    }

    function fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            showNotification(instafeed_interactions_ajax.strings.link_copied, 'success');
        } catch (err) {
            showNotification(instafeed_interactions_ajax.strings.share_failed, 'error');
        }
        
        document.body.removeChild(textArea);
    }

    function showNotification(message, type = 'info') {
        // Use existing notification system from theme
        if (typeof window.showNotification === 'function') {
            window.showNotification(message, type);
        } else {
            // Fallback notification
            const notification = $(`
                <div class="instafeed-notification ${type}" style="position: fixed; top: 20px; right: 20px; padding: 15px; border-radius: 8px; color: white; z-index: 10001;">
                    ${message}
                </div>
            `);

            if (type === 'success') notification.css('background', '#27ae60');
            else if (type === 'error') notification.css('background', '#e74c3c');
            else notification.css('background', '#3498db');

            $('body').append(notification);

            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 3000);
        }
    }

    // Global functions for theme integration
    window.initializeInteractionsForNewContent = function(container) {
        console.log('Initializing interactions for new content:', container);

        const $container = $(container || document);
        const placeholders = $container.find('.instafeed-interactions-placeholder');

        placeholders.each(function() {
            const $placeholder = $(this);
            const postId = $placeholder.data('post-id');

            if (postId && !$placeholder.hasClass('interactions-loaded')) {
                $placeholder.addClass('interactions-loaded');
                loadPostInteractions($placeholder, postId);
            }
        });

        console.log(`Initialized interactions for ${placeholders.length} new posts`);
    };

    // Make sure the function is available globally
    window.instaFeedInteractionsReady = true;

    // Auto-initialize when DOM changes (fallback)
    $(document).on('DOMNodeInserted', function(e) {
        if (e.target.classList && e.target.classList.contains('post-item')) {
            setTimeout(() => {
                window.initializeInteractionsForNewContent(e.target);
            }, 100);
        }
    });

})(jQuery);
