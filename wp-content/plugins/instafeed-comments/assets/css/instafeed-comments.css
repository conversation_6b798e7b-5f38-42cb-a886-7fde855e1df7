/* InstaFeed Comments - Instagram-style Comment System */

.instafeed-comments-section {
    background: #fff;
    border-radius: 12px;
    margin: 30px 0;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Comments Header */
.instafeed-comments-header {
    padding: 20px;
    border-bottom: 1px solid #efefef;
    background: #fafafa;
}

.instafeed-comments-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
}

/* Comments List */
.instafeed-comments-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
}

.instafeed-comment {
    padding: 15px 20px;
    border-bottom: 1px solid #efefef;
    transition: background-color 0.2s ease;
}

.instafeed-comment:hover {
    background: #fafafa;
}

.instafeed-comment:last-child {
    border-bottom: none;
}

.instafeed-comment-content {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.instafeed-comment-avatar {
    flex-shrink: 0;
}

.instafeed-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid #dbdbdb;
}

.instafeed-comment-reply .instafeed-avatar {
    width: 32px;
    height: 32px;
}

.instafeed-comment-body {
    flex: 1;
    min-width: 0;
}

.instafeed-comment-main {
    margin-bottom: 8px;
}

.instafeed-comment-text {
    line-height: 1.4;
    word-wrap: break-word;
}

.instafeed-comment-author {
    font-weight: 600;
    color: #262626;
    margin-right: 8px;
    font-size: 14px;
}

.instafeed-comment-content-text {
    color: #262626;
    font-size: 14px;
}

/* Comment Meta */
.instafeed-comment-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 12px;
    color: #8e8e8e;
}

.instafeed-comment-time {
    font-weight: 400;
}

.instafeed-reply-btn {
    background: none;
    border: none;
    color: #8e8e8e;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    padding: 0;
}

.instafeed-reply-btn:hover {
    color: #262626;
}

/* Reaction Buttons */
.instafeed-reaction-buttons {
    display: flex;
    gap: 8px;
}

.instafeed-react-btn {
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.instafeed-react-btn:hover {
    background: #f0f0f0;
}

.instafeed-comment-reactions {
    display: flex;
    gap: 8px;
}

.instafeed-reaction {
    font-size: 12px;
    color: #8e8e8e;
}

/* Comment Replies */
.instafeed-comment-replies {
    margin-left: 52px;
    margin-top: 10px;
}

.instafeed-comment-reply {
    padding: 10px 0;
    border-bottom: none;
}

.instafeed-comment-reply:hover {
    background: transparent;
}

/* Comment Form */
.instafeed-comment-form-container {
    padding: 20px;
    border-top: 1px solid #efefef;
    background: #fff;
}

.instafeed-comment-form-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-start;
}

.instafeed-comment-form-avatar {
    flex-shrink: 0;
}

.instafeed-comment-form-input {
    flex: 1;
    position: relative;
}

.instafeed-comment-form-input textarea {
    width: 100%;
    border: 1px solid #dbdbdb;
    border-radius: 20px;
    padding: 12px 50px 12px 16px;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    outline: none;
    transition: border-color 0.2s ease;
    font-family: inherit;
    background: #fafafa;
}

.instafeed-comment-form-input textarea:focus {
    border-color: #0095f6;
    background: #fff;
}

.instafeed-comment-form-input textarea::placeholder {
    color: #8e8e8e;
}

.instafeed-comment-form-actions {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Emoji Picker */
.instafeed-emoji-picker {
    position: relative;
}

.instafeed-emoji-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.instafeed-emoji-btn:hover {
    background: #f0f0f0;
}

.instafeed-emoji-panel {
    position: absolute;
    bottom: 100%;
    right: 0;
    background: #fff;
    border: 1px solid #dbdbdb;
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: none;
    z-index: 1000;
    width: 200px;
}

.instafeed-emoji-panel.show {
    display: block;
}

.instafeed-emoji-panel .emoji {
    display: inline-block;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.instafeed-emoji-panel .emoji:hover {
    background: #f0f0f0;
}

/* Submit Button */
.instafeed-submit-comment {
    background: #0095f6;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.instafeed-submit-comment:hover {
    background: #1877f2;
}

.instafeed-submit-comment:disabled {
    background: #dbdbdb;
    cursor: not-allowed;
}

.instafeed-comment-form-submit {
    margin-top: 12px;
    text-align: right;
}

/* Load More Button */
.instafeed-load-more-btn {
    width: 100%;
    background: none;
    border: none;
    color: #8e8e8e;
    font-size: 14px;
    font-weight: 600;
    padding: 15px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.instafeed-load-more-btn:hover {
    color: #262626;
}

/* No Comments */
.instafeed-no-comments {
    padding: 40px 20px;
    text-align: center;
    color: #8e8e8e;
}

/* Comments Closed */
.instafeed-comments-closed {
    padding: 20px;
    text-align: center;
    color: #8e8e8e;
    background: #fafafa;
}

/* Login Required */
.instafeed-login-required {
    padding: 20px;
    text-align: center;
    background: #fafafa;
}

.instafeed-login-required a {
    color: #0095f6;
    text-decoration: none;
    font-weight: 600;
}

.instafeed-login-required a:hover {
    text-decoration: underline;
}

/* Hashtags and Mentions */
.instafeed-hashtag,
.instafeed-mention {
    color: #0095f6;
    text-decoration: none;
    font-weight: 500;
}

.instafeed-hashtag:hover,
.instafeed-mention:hover {
    text-decoration: underline;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .instafeed-comments-section {
        margin: 20px 0;
        border-radius: 8px;
    }
    
    .instafeed-comment {
        padding: 12px 15px;
    }
    
    .instafeed-comments-header,
    .instafeed-comment-form-container {
        padding: 15px;
    }
    
    .instafeed-comment-content {
        gap: 10px;
    }
    
    .instafeed-avatar {
        width: 36px;
        height: 36px;
    }
    
    .instafeed-comment-reply .instafeed-avatar {
        width: 28px;
        height: 28px;
    }
    
    .instafeed-comment-replies {
        margin-left: 46px;
    }
    
    .instafeed-comment-form-input textarea {
        padding: 10px 45px 10px 14px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .instafeed-emoji-panel {
        width: 180px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .instafeed-comments-section {
        background: #262626;
        color: #fff;
    }
    
    .instafeed-comments-header {
        background: #1a1a1a;
        border-bottom-color: #404040;
    }
    
    .instafeed-comments-title {
        color: #fff;
    }
    
    .instafeed-comment {
        border-bottom-color: #404040;
    }
    
    .instafeed-comment:hover {
        background: #1a1a1a;
    }
    
    .instafeed-comment-author,
    .instafeed-comment-content-text {
        color: #fff;
    }
    
    .instafeed-comment-form-input textarea {
        background: #1a1a1a;
        border-color: #404040;
        color: #fff;
    }
    
    .instafeed-comment-form-input textarea:focus {
        background: #262626;
    }
    
    .instafeed-emoji-panel {
        background: #262626;
        border-color: #404040;
    }
}
