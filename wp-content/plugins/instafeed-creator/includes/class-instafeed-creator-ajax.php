<?php
/**
 * InstaFeed Creator AJAX Class
 * Handles all AJAX requests for post creation and management
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Creator_Ajax {
    
    public function __construct() {
        // Post creation and management
        add_action('wp_ajax_instafeed_create_post', array($this, 'create_post'));
        add_action('wp_ajax_instafeed_update_post', array($this, 'update_post'));
        add_action('wp_ajax_instafeed_delete_post', array($this, 'delete_post'));
        add_action('wp_ajax_instafeed_get_user_posts', array($this, 'get_user_posts'));
        add_action('wp_ajax_instafeed_get_post_data', array($this, 'get_post_data'));
        
        // Image upload
        add_action('wp_ajax_instafeed_upload_image', array($this, 'upload_image'));
    }
    
    public function create_post() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_creator_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to create posts');
        }
        
        // Check user capabilities
        if (!current_user_can('publish_instafeed_posts')) {
            wp_send_json_error('You do not have permission to create posts');
        }
        
        $title = sanitize_text_field($_POST['post_title']);
        $content = sanitize_textarea_field($_POST['post_content']);
        $tags = sanitize_text_field($_POST['post_tags']);
        $category = sanitize_text_field($_POST['post_category']);
        
        if (empty($title)) {
            wp_send_json_error('Post title is required');
        }
        
        // Handle image upload
        $attachment_id = 0;
        if (!empty($_FILES['post_image']['name'])) {
            $attachment_id = $this->handle_image_upload($_FILES['post_image']);
            if (is_wp_error($attachment_id)) {
                wp_send_json_error($attachment_id->get_error_message());
            }
        }
        
        // Create the post
        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_type' => 'instafeed_post',
            'post_status' => get_option('instafeed_creator_require_approval') ? 'pending' : 'publish',
            'post_author' => $user_id,
            'meta_input' => array(
                'instafeed_category' => $category,
                'instafeed_tags' => $tags,
                'instafeed_created_via' => 'instafeed_creator'
            )
        );
        
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            wp_send_json_error('Failed to create post: ' . $post_id->get_error_message());
        }
        
        // Set featured image
        if ($attachment_id) {
            set_post_thumbnail($post_id, $attachment_id);
        }
        
        // Add tags as taxonomy terms
        if (!empty($tags)) {
            $tag_array = array_map('trim', explode(',', $tags));
            wp_set_post_terms($post_id, $tag_array, 'post_tag');
        }
        
        // Set category
        if (!empty($category)) {
            wp_set_post_terms($post_id, array($category), 'category');
        }
        
        // Log activity
        $this->log_user_activity($user_id, 'post_created', $post_id);
        
        wp_send_json_success(array(
            'post_id' => $post_id,
            'message' => __('Post created successfully!', 'instafeed-creator'),
            'post_url' => get_permalink($post_id),
            'edit_url' => get_edit_post_link($post_id)
        ));
    }
    
    public function update_post() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_creator_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to update posts');
        }
        
        $post_id = intval($_POST['post_id']);
        $post = get_post($post_id);
        
        if (!$post || $post->post_type !== 'instafeed_post') {
            wp_send_json_error('Invalid post');
        }
        
        // Check if user can edit this post
        if ($post->post_author != $user_id && !current_user_can('edit_others_posts')) {
            wp_send_json_error('You do not have permission to edit this post');
        }
        
        $title = sanitize_text_field($_POST['post_title']);
        $content = sanitize_textarea_field($_POST['post_content']);
        $tags = sanitize_text_field($_POST['post_tags']);
        $category = sanitize_text_field($_POST['post_category']);
        
        if (empty($title)) {
            wp_send_json_error('Post title is required');
        }
        
        // Update the post
        $post_data = array(
            'ID' => $post_id,
            'post_title' => $title,
            'post_content' => $content,
            'meta_input' => array(
                'instafeed_category' => $category,
                'instafeed_tags' => $tags
            )
        );
        
        $result = wp_update_post($post_data);
        
        if (is_wp_error($result)) {
            wp_send_json_error('Failed to update post: ' . $result->get_error_message());
        }
        
        // Update tags
        if (!empty($tags)) {
            $tag_array = array_map('trim', explode(',', $tags));
            wp_set_post_terms($post_id, $tag_array, 'post_tag');
        }
        
        // Update category
        if (!empty($category)) {
            wp_set_post_terms($post_id, array($category), 'category');
        }
        
        // Log activity
        $this->log_user_activity($user_id, 'post_updated', $post_id);
        
        wp_send_json_success(array(
            'message' => __('Post updated successfully!', 'instafeed-creator'),
            'post_url' => get_permalink($post_id)
        ));
    }
    
    public function delete_post() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_creator_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to delete posts');
        }
        
        $post_id = intval($_POST['post_id']);
        $post = get_post($post_id);
        
        if (!$post || $post->post_type !== 'instafeed_post') {
            wp_send_json_error('Invalid post');
        }
        
        // Check if user can delete this post
        if ($post->post_author != $user_id && !current_user_can('delete_others_posts')) {
            wp_send_json_error('You do not have permission to delete this post');
        }
        
        // Delete the post
        $result = wp_delete_post($post_id, true);
        
        if (!$result) {
            wp_send_json_error('Failed to delete post');
        }
        
        // Log activity
        $this->log_user_activity($user_id, 'post_deleted', $post_id);
        
        wp_send_json_success(array(
            'message' => __('Post deleted successfully!', 'instafeed-creator')
        ));
    }
    
    public function get_user_posts() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_creator_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to view posts');
        }
        
        $status = sanitize_text_field($_POST['status']) ?: 'any';
        $page = intval($_POST['page']) ?: 1;
        $per_page = 12;
        $offset = ($page - 1) * $per_page;
        
        $args = array(
            'post_type' => 'instafeed_post',
            'author' => $user_id,
            'post_status' => $status,
            'posts_per_page' => $per_page,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        $posts = get_posts($args);
        $total_posts = $this->get_user_posts_count($user_id, $status);
        
        $posts_data = array();
        foreach ($posts as $post) {
            $thumbnail_url = get_the_post_thumbnail_url($post->ID, 'medium');
            if (!$thumbnail_url) {
                $thumbnail_url = 'https://via.placeholder.com/300x300?text=No+Image';
            }
            
            $posts_data[] = array(
                'id' => $post->ID,
                'title' => $post->post_title,
                'content' => wp_trim_words($post->post_content, 20),
                'status' => $post->post_status,
                'date' => get_the_date('M j, Y', $post->ID),
                'thumbnail' => $thumbnail_url,
                'url' => get_permalink($post->ID),
                'edit_url' => get_edit_post_link($post->ID),
                'tags' => get_post_meta($post->ID, 'instafeed_tags', true),
                'category' => get_post_meta($post->ID, 'instafeed_category', true),
                'likes' => get_post_meta($post->ID, 'instafeed_likes', true) ?: 0,
                'comments' => get_comments_number($post->ID)
            );
        }
        
        wp_send_json_success(array(
            'posts' => $posts_data,
            'total' => $total_posts,
            'page' => $page,
            'per_page' => $per_page,
            'has_more' => ($offset + $per_page) < $total_posts
        ));
    }
    
    public function get_post_data() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_creator_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in');
        }
        
        $post_id = intval($_POST['post_id']);
        $post = get_post($post_id);
        
        if (!$post || $post->post_type !== 'instafeed_post') {
            wp_send_json_error('Invalid post');
        }
        
        // Check if user can edit this post
        if ($post->post_author != $user_id && !current_user_can('edit_others_posts')) {
            wp_send_json_error('You do not have permission to edit this post');
        }
        
        $post_data = array(
            'id' => $post->ID,
            'title' => $post->post_title,
            'content' => $post->post_content,
            'tags' => get_post_meta($post->ID, 'instafeed_tags', true),
            'category' => get_post_meta($post->ID, 'instafeed_category', true),
            'status' => $post->post_status,
            'thumbnail' => get_the_post_thumbnail_url($post->ID, 'medium')
        );
        
        wp_send_json_success($post_data);
    }
    
    private function handle_image_upload($file) {
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }
        
        $upload_overrides = array('test_form' => false);
        $movefile = wp_handle_upload($file, $upload_overrides);
        
        if ($movefile && !isset($movefile['error'])) {
            $attachment = array(
                'post_mime_type' => $movefile['type'],
                'post_title' => preg_replace('/\.[^.]+$/', '', basename($movefile['file'])),
                'post_content' => '',
                'post_status' => 'inherit'
            );
            
            $attachment_id = wp_insert_attachment($attachment, $movefile['file']);
            
            if (!is_wp_error($attachment_id)) {
                require_once(ABSPATH . 'wp-admin/includes/image.php');
                $attachment_data = wp_generate_attachment_metadata($attachment_id, $movefile['file']);
                wp_update_attachment_metadata($attachment_id, $attachment_data);
                return $attachment_id;
            }
        }
        
        return new WP_Error('upload_error', 'Failed to upload image');
    }
    
    private function get_user_posts_count($user_id, $status = 'any') {
        $args = array(
            'post_type' => 'instafeed_post',
            'author' => $user_id,
            'post_status' => $status,
            'posts_per_page' => -1,
            'fields' => 'ids'
        );
        
        $posts = get_posts($args);
        return count($posts);
    }
    
    private function log_user_activity($user_id, $action, $post_id) {
        // Log user activity for analytics
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'instafeed_user_activity';
        
        $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'action' => $action,
                'object_id' => $post_id,
                'object_type' => 'post',
                'created_at' => current_time('mysql')
            ),
            array('%d', '%s', '%d', '%s', '%s')
        );
    }
}
