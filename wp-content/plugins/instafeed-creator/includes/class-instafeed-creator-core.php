<?php
/**
 * InstaFeed Creator Core Class
 * Handles core post creation and management functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Creator_Core {
    
    public function __construct() {
        add_action('wp_footer', array($this, 'add_creator_modal'));
        add_action('wp_footer', array($this, 'add_my_posts_modal'));
        add_shortcode('instafeed_creator', array($this, 'creator_shortcode'));
        add_shortcode('instafeed_my_posts', array($this, 'my_posts_shortcode'));
        add_filter('the_content', array($this, 'add_post_actions'));
    }
    
    public function add_creator_modal() {
        if (!is_user_logged_in()) {
            return;
        }
        ?>
        <!-- InstaFeed Creator Modal -->
        <div id="instafeed-creator-modal" class="instafeed-modal" style="display: none;">
            <div class="instafeed-modal-overlay" onclick="closeInstaFeedModal('instafeed-creator-modal')"></div>
            <div class="instafeed-modal-content">
                <div class="instafeed-modal-header">
                    <h2><?php _e('Create New Post', 'instafeed-creator'); ?></h2>
                    <button class="instafeed-modal-close" onclick="closeInstaFeedModal('instafeed-creator-modal')">&times;</button>
                </div>
                <div class="instafeed-modal-body">
                    <form id="instafeed-creator-form" enctype="multipart/form-data">
                        <div class="instafeed-form-group">
                            <label for="post-title"><?php _e('Post Title', 'instafeed-creator'); ?></label>
                            <input type="text" id="post-title" name="post_title" required maxlength="100">
                            <small class="instafeed-help-text"><?php _e('Give your post an engaging title', 'instafeed-creator'); ?></small>
                        </div>
                        
                        <div class="instafeed-form-group">
                            <label for="post-image"><?php _e('Upload Image', 'instafeed-creator'); ?></label>
                            <div class="instafeed-image-upload-area" id="image-upload-area">
                                <div class="instafeed-upload-placeholder">
                                    <span class="instafeed-upload-icon">📷</span>
                                    <p><?php _e('Click to upload or drag and drop', 'instafeed-creator'); ?></p>
                                    <small><?php _e('JPG, PNG, GIF up to 5MB', 'instafeed-creator'); ?></small>
                                </div>
                                <input type="file" id="post-image" name="post_image" accept="image/*" style="display: none;">
                                <div class="instafeed-image-preview" id="image-preview" style="display: none;">
                                    <img id="preview-img" src="" alt="Preview">
                                    <button type="button" class="instafeed-remove-image" onclick="removeImagePreview()">&times;</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="instafeed-form-group">
                            <label for="post-content"><?php _e('Caption', 'instafeed-creator'); ?></label>
                            <textarea id="post-content" name="post_content" rows="4" maxlength="500" placeholder="<?php _e('Write a caption for your post...', 'instafeed-creator'); ?>"></textarea>
                            <small class="instafeed-help-text">
                                <span id="char-count">0</span>/500 <?php _e('characters', 'instafeed-creator'); ?>
                            </small>
                        </div>
                        
                        <div class="instafeed-form-group">
                            <label for="post-tags"><?php _e('Tags', 'instafeed-creator'); ?></label>
                            <input type="text" id="post-tags" name="post_tags" placeholder="<?php _e('photography, nature, inspiration...', 'instafeed-creator'); ?>">
                            <small class="instafeed-help-text"><?php _e('Separate tags with commas', 'instafeed-creator'); ?></small>
                        </div>
                        
                        <div class="instafeed-form-group">
                            <label for="post-category"><?php _e('Category', 'instafeed-creator'); ?></label>
                            <select id="post-category" name="post_category">
                                <option value="photography"><?php _e('Photography', 'instafeed-creator'); ?></option>
                                <option value="lifestyle"><?php _e('Lifestyle', 'instafeed-creator'); ?></option>
                                <option value="travel"><?php _e('Travel', 'instafeed-creator'); ?></option>
                                <option value="food"><?php _e('Food', 'instafeed-creator'); ?></option>
                                <option value="motivation"><?php _e('Motivation', 'instafeed-creator'); ?></option>
                                <option value="art"><?php _e('Art', 'instafeed-creator'); ?></option>
                                <option value="nature"><?php _e('Nature', 'instafeed-creator'); ?></option>
                                <option value="other"><?php _e('Other', 'instafeed-creator'); ?></option>
                            </select>
                        </div>
                        
                        <div class="instafeed-form-actions">
                            <button type="button" class="instafeed-btn instafeed-btn-secondary" onclick="closeInstaFeedModal('instafeed-creator-modal')">
                                <?php _e('Cancel', 'instafeed-creator'); ?>
                            </button>
                            <button type="submit" class="instafeed-btn instafeed-btn-primary" id="create-post-btn">
                                <?php _e('Create Post', 'instafeed-creator'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function add_my_posts_modal() {
        if (!is_user_logged_in()) {
            return;
        }
        ?>
        <!-- My Posts Modal -->
        <div id="instafeed-my-posts-modal" class="instafeed-modal" style="display: none;">
            <div class="instafeed-modal-overlay" onclick="closeInstaFeedModal('instafeed-my-posts-modal')"></div>
            <div class="instafeed-modal-content instafeed-modal-large">
                <div class="instafeed-modal-header">
                    <h2><?php _e('My Posts', 'instafeed-creator'); ?></h2>
                    <button class="instafeed-modal-close" onclick="closeInstaFeedModal('instafeed-my-posts-modal')">&times;</button>
                </div>
                <div class="instafeed-modal-body">
                    <div class="instafeed-posts-header">
                        <div class="instafeed-posts-stats">
                            <span id="total-posts-count">0</span> <?php _e('posts', 'instafeed-creator'); ?>
                        </div>
                        <div class="instafeed-posts-actions">
                            <button class="instafeed-btn instafeed-btn-primary" onclick="openInstaFeedModal('instafeed-creator-modal')">
                                <?php _e('Create New Post', 'instafeed-creator'); ?>
                            </button>
                        </div>
                    </div>
                    
                    <div class="instafeed-posts-filter">
                        <select id="posts-filter" onchange="filterMyPosts()">
                            <option value="all"><?php _e('All Posts', 'instafeed-creator'); ?></option>
                            <option value="published"><?php _e('Published', 'instafeed-creator'); ?></option>
                            <option value="draft"><?php _e('Drafts', 'instafeed-creator'); ?></option>
                            <option value="pending"><?php _e('Pending Review', 'instafeed-creator'); ?></option>
                        </select>
                    </div>
                    
                    <div id="my-posts-grid" class="instafeed-my-posts-grid">
                        <div class="instafeed-loading" id="posts-loading">
                            <span><?php _e('Loading your posts...', 'instafeed-creator'); ?></span>
                        </div>
                    </div>
                    
                    <div class="instafeed-posts-pagination" id="posts-pagination" style="display: none;">
                        <button class="instafeed-btn instafeed-btn-secondary" id="load-more-posts">
                            <?php _e('Load More Posts', 'instafeed-creator'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Edit Post Modal -->
        <div id="instafeed-edit-post-modal" class="instafeed-modal" style="display: none;">
            <div class="instafeed-modal-overlay" onclick="closeInstaFeedModal('instafeed-edit-post-modal')"></div>
            <div class="instafeed-modal-content">
                <div class="instafeed-modal-header">
                    <h2><?php _e('Edit Post', 'instafeed-creator'); ?></h2>
                    <button class="instafeed-modal-close" onclick="closeInstaFeedModal('instafeed-edit-post-modal')">&times;</button>
                </div>
                <div class="instafeed-modal-body">
                    <form id="instafeed-edit-form">
                        <input type="hidden" id="edit-post-id" name="post_id">
                        
                        <div class="instafeed-form-group">
                            <label for="edit-post-title"><?php _e('Post Title', 'instafeed-creator'); ?></label>
                            <input type="text" id="edit-post-title" name="post_title" required maxlength="100">
                        </div>
                        
                        <div class="instafeed-form-group">
                            <label for="edit-post-content"><?php _e('Caption', 'instafeed-creator'); ?></label>
                            <textarea id="edit-post-content" name="post_content" rows="4" maxlength="500"></textarea>
                            <small class="instafeed-help-text">
                                <span id="edit-char-count">0</span>/500 <?php _e('characters', 'instafeed-creator'); ?>
                            </small>
                        </div>
                        
                        <div class="instafeed-form-group">
                            <label for="edit-post-tags"><?php _e('Tags', 'instafeed-creator'); ?></label>
                            <input type="text" id="edit-post-tags" name="post_tags">
                        </div>
                        
                        <div class="instafeed-form-group">
                            <label for="edit-post-category"><?php _e('Category', 'instafeed-creator'); ?></label>
                            <select id="edit-post-category" name="post_category">
                                <option value="photography"><?php _e('Photography', 'instafeed-creator'); ?></option>
                                <option value="lifestyle"><?php _e('Lifestyle', 'instafeed-creator'); ?></option>
                                <option value="travel"><?php _e('Travel', 'instafeed-creator'); ?></option>
                                <option value="food"><?php _e('Food', 'instafeed-creator'); ?></option>
                                <option value="motivation"><?php _e('Motivation', 'instafeed-creator'); ?></option>
                                <option value="art"><?php _e('Art', 'instafeed-creator'); ?></option>
                                <option value="nature"><?php _e('Nature', 'instafeed-creator'); ?></option>
                                <option value="other"><?php _e('Other', 'instafeed-creator'); ?></option>
                            </select>
                        </div>
                        
                        <div class="instafeed-form-actions">
                            <button type="button" class="instafeed-btn instafeed-btn-secondary" onclick="closeInstaFeedModal('instafeed-edit-post-modal')">
                                <?php _e('Cancel', 'instafeed-creator'); ?>
                            </button>
                            <button type="submit" class="instafeed-btn instafeed-btn-primary">
                                <?php _e('Update Post', 'instafeed-creator'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function creator_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please log in to create posts.', 'instafeed-creator') . '</p>';
        }
        
        return '<button class="instafeed-btn instafeed-btn-primary" onclick="openInstaFeedModal(\'instafeed-creator-modal\')">' . 
               __('Create New Post', 'instafeed-creator') . '</button>';
    }
    
    public function my_posts_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please log in to view your posts.', 'instafeed-creator') . '</p>';
        }
        
        return '<button class="instafeed-btn instafeed-btn-primary" onclick="openInstaFeedModal(\'instafeed-my-posts-modal\')">' . 
               __('My Posts', 'instafeed-creator') . '</button>';
    }
    
    public function add_post_actions($content) {
        if (is_single() && get_post_type() === 'instafeed_post' && is_user_logged_in()) {
            $post_id = get_the_ID();
            $current_user_id = get_current_user_id();
            $post_author_id = get_post_field('post_author', $post_id);
            
            if ($current_user_id == $post_author_id || current_user_can('edit_others_posts')) {
                $actions = '<div class="instafeed-post-actions">';
                $actions .= '<button class="instafeed-btn instafeed-btn-secondary" onclick="editInstaFeedPost(' . $post_id . ')">' . __('Edit', 'instafeed-creator') . '</button>';
                $actions .= '<button class="instafeed-btn instafeed-btn-danger" onclick="deleteInstaFeedPost(' . $post_id . ')">' . __('Delete', 'instafeed-creator') . '</button>';
                $actions .= '</div>';
                
                $content .= $actions;
            }
        }
        
        return $content;
    }
    
    public function get_user_posts($user_id, $status = 'any', $limit = 12, $offset = 0) {
        $args = array(
            'post_type' => 'instafeed_post',
            'author' => $user_id,
            'post_status' => $status,
            'posts_per_page' => $limit,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        return get_posts($args);
    }
    
    public function get_user_posts_count($user_id, $status = 'any') {
        $args = array(
            'post_type' => 'instafeed_post',
            'author' => $user_id,
            'post_status' => $status,
            'posts_per_page' => -1,
            'fields' => 'ids'
        );
        
        $posts = get_posts($args);
        return count($posts);
    }
}
