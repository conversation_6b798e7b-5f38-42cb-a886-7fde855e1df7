<?php
/**
 * Plugin Name: InstaFeed Creator
 * Plugin URI: https://example.com/instafeed-creator
 * Description: Complete post creation and management system for InstaFeed. Allows users to create, edit, and manage their posts with Instagram-style interface.
 * Version: 1.0.0
 * Author: InstaFeed Development Team
 * License: GPL v2 or later
 * Text Domain: instafeed-creator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('INSTAFEED_CREATOR_URL', plugin_dir_url(__FILE__));
define('INSTAFEED_CREATOR_PATH', plugin_dir_path(__FILE__));
define('INSTAFEED_CREATOR_VERSION', '1.0.0');

// Include required files
require_once INSTAFEED_CREATOR_PATH . 'includes/class-instafeed-creator-core.php';
require_once INSTAFEED_CREATOR_PATH . 'includes/class-instafeed-creator-ajax.php';
require_once INSTAFEED_CREATOR_PATH . 'includes/class-instafeed-creator-admin.php';

/**
 * Main InstaFeed Creator Plugin Class
 */
class InstaFeed_Creator_Plugin {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Initialize plugin components
        new InstaFeed_Creator_Core();
        new InstaFeed_Creator_Ajax();
        new InstaFeed_Creator_Admin();
        
        // Load text domain
        load_plugin_textdomain('instafeed-creator', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Add custom post type for user posts
        $this->register_post_types();
        
        // Add custom capabilities
        $this->add_capabilities();
    }
    
    public function enqueue_scripts() {
        if (is_user_logged_in()) {
            // Enqueue CSS
            wp_enqueue_style(
                'instafeed-creator-style',
                INSTAFEED_CREATOR_URL . 'assets/css/instafeed-creator.css',
                array(),
                INSTAFEED_CREATOR_VERSION
            );
            
            // Enqueue JavaScript
            wp_enqueue_script(
                'instafeed-creator-script',
                INSTAFEED_CREATOR_URL . 'assets/js/instafeed-creator.js',
                array('jquery', 'wp-util'),
                INSTAFEED_CREATOR_VERSION,
                true
            );
            
            // Localize script
            wp_localize_script('instafeed-creator-script', 'instafeed_creator_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('instafeed_creator_nonce'),
                'user_id' => get_current_user_id(),
                'max_file_size' => wp_max_upload_size(),
                'allowed_types' => array('jpg', 'jpeg', 'png', 'gif', 'webp'),
                'strings' => array(
                    'creating_post' => __('Creating post...', 'instafeed-creator'),
                    'uploading_image' => __('Uploading image...', 'instafeed-creator'),
                    'post_created' => __('Post created successfully!', 'instafeed-creator'),
                    'error_occurred' => __('An error occurred', 'instafeed-creator'),
                    'invalid_file_type' => __('Invalid file type', 'instafeed-creator'),
                    'file_too_large' => __('File is too large', 'instafeed-creator'),
                    'title_required' => __('Title is required', 'instafeed-creator'),
                    'image_required' => __('Image is required', 'instafeed-creator'),
                    'confirm_delete' => __('Are you sure you want to delete this post?', 'instafeed-creator'),
                    'post_deleted' => __('Post deleted successfully', 'instafeed-creator'),
                    'post_updated' => __('Post updated successfully', 'instafeed-creator'),
                )
            ));
            
            // Enqueue media uploader
            wp_enqueue_media();
        }
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'instafeed-creator') !== false) {
            wp_enqueue_style('instafeed-creator-admin', INSTAFEED_CREATOR_URL . 'assets/css/admin.css', array(), INSTAFEED_CREATOR_VERSION);
            wp_enqueue_script('instafeed-creator-admin', INSTAFEED_CREATOR_URL . 'assets/js/admin.js', array('jquery'), INSTAFEED_CREATOR_VERSION, true);
        }
    }
    
    public function activate() {
        // Create custom tables if needed
        $this->create_tables();
        
        // Set default options
        add_option('instafeed_creator_enabled', 1);
        add_option('instafeed_creator_max_posts_per_user', 100);
        add_option('instafeed_creator_require_approval', 0);
        add_option('instafeed_creator_allowed_file_types', 'jpg,jpeg,png,gif,webp');
        add_option('instafeed_creator_max_file_size', 5242880); // 5MB
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    private function register_post_types() {
        register_post_type('instafeed_post', array(
            'labels' => array(
                'name' => __('InstaFeed Posts', 'instafeed-creator'),
                'singular_name' => __('InstaFeed Post', 'instafeed-creator'),
                'add_new' => __('Add New Post', 'instafeed-creator'),
                'add_new_item' => __('Add New InstaFeed Post', 'instafeed-creator'),
                'edit_item' => __('Edit InstaFeed Post', 'instafeed-creator'),
                'new_item' => __('New InstaFeed Post', 'instafeed-creator'),
                'view_item' => __('View InstaFeed Post', 'instafeed-creator'),
                'search_items' => __('Search InstaFeed Posts', 'instafeed-creator'),
                'not_found' => __('No InstaFeed posts found', 'instafeed-creator'),
                'not_found_in_trash' => __('No InstaFeed posts found in trash', 'instafeed-creator'),
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'thumbnail', 'author', 'comments'),
            'menu_icon' => 'dashicons-camera',
            'rewrite' => array('slug' => 'instafeed'),
            'show_in_rest' => true,
            'capability_type' => 'instafeed_post',
            'map_meta_cap' => true,
        ));
    }
    
    private function add_capabilities() {
        $role = get_role('subscriber');
        if ($role) {
            $role->add_cap('edit_instafeed_posts');
            $role->add_cap('edit_published_instafeed_posts');
            $role->add_cap('delete_instafeed_posts');
            $role->add_cap('delete_published_instafeed_posts');
            $role->add_cap('publish_instafeed_posts');
            $role->add_cap('read_instafeed_posts');
        }
        
        $role = get_role('author');
        if ($role) {
            $role->add_cap('edit_instafeed_posts');
            $role->add_cap('edit_published_instafeed_posts');
            $role->add_cap('delete_instafeed_posts');
            $role->add_cap('delete_published_instafeed_posts');
            $role->add_cap('publish_instafeed_posts');
            $role->add_cap('read_instafeed_posts');
        }
    }
    
    private function create_tables() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'instafeed_post_meta';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            post_id bigint(20) NOT NULL,
            meta_key varchar(255) NOT NULL,
            meta_value longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY post_id (post_id),
            KEY meta_key (meta_key)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

// Initialize the plugin
function instafeed_creator_init() {
    return InstaFeed_Creator_Plugin::get_instance();
}

// Start the plugin
instafeed_creator_init();
