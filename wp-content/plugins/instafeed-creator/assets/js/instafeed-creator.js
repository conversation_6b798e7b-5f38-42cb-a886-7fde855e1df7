/**
 * InstaFeed Creator JavaScript
 * Handles post creation and management functionality
 */

(function($) {
    'use strict';

    let currentPage = 1;
    let currentFilter = 'all';
    let isLoading = false;

    $(document).ready(function() {
        initializeCreator();
    });

    function initializeCreator() {
        bindEvents();
        setupImageUpload();
        setupCharacterCount();
    }

    function bindEvents() {
        // Form submissions
        $(document).on('submit', '#instafeed-creator-form', handleCreatePost);
        $(document).on('submit', '#instafeed-edit-form', handleUpdatePost);
        
        // Modal events
        $(document).on('click', '.instafeed-modal-overlay', function(e) {
            if (e.target === this) {
                closeInstaFeedModal($(this).closest('.instafeed-modal').attr('id'));
            }
        });
        
        // Post management
        $(document).on('click', '#load-more-posts', loadMorePosts);
        $(document).on('change', '#posts-filter', function() {
            currentFilter = $(this).val();
            currentPage = 1;
            loadUserPosts();
        });
        
        // Post actions
        $(document).on('click', '.edit-post-btn', function() {
            const postId = $(this).data('post-id');
            editInstaFeedPost(postId);
        });
        
        $(document).on('click', '.delete-post-btn', function() {
            const postId = $(this).data('post-id');
            deleteInstaFeedPost(postId);
        });
    }

    function setupImageUpload() {
        const uploadArea = $('#image-upload-area');
        const fileInput = $('#post-image');
        
        uploadArea.on('click', function() {
            fileInput.click();
        });
        
        uploadArea.on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('dragover');
        });
        
        uploadArea.on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
        });
        
        uploadArea.on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
            
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                handleImageSelect(files[0]);
            }
        });
        
        fileInput.on('change', function() {
            if (this.files && this.files[0]) {
                handleImageSelect(this.files[0]);
            }
        });
    }

    function handleImageSelect(file) {
        // Validate file type
        const allowedTypes = instafeed_creator_ajax.allowed_types;
        const fileExtension = file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            showNotification(instafeed_creator_ajax.strings.invalid_file_type, 'error');
            return;
        }
        
        // Validate file size
        if (file.size > instafeed_creator_ajax.max_file_size) {
            showNotification(instafeed_creator_ajax.strings.file_too_large, 'error');
            return;
        }
        
        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#preview-img').attr('src', e.target.result);
            $('.instafeed-upload-placeholder').hide();
            $('#image-preview').show();
        };
        reader.readAsDataURL(file);
    }

    function removeImagePreview() {
        $('#post-image').val('');
        $('#image-preview').hide();
        $('.instafeed-upload-placeholder').show();
    }

    function setupCharacterCount() {
        $(document).on('input', '#post-content', function() {
            const count = $(this).val().length;
            $('#char-count').text(count);
            
            if (count > 450) {
                $('#char-count').css('color', '#e74c3c');
            } else {
                $('#char-count').css('color', '#8e8e8e');
            }
        });
        
        $(document).on('input', '#edit-post-content', function() {
            const count = $(this).val().length;
            $('#edit-char-count').text(count);
            
            if (count > 450) {
                $('#edit-char-count').css('color', '#e74c3c');
            } else {
                $('#edit-char-count').css('color', '#8e8e8e');
            }
        });
    }

    function handleCreatePost(e) {
        e.preventDefault();
        
        if (isLoading) return;
        
        const form = $(this);
        const formData = new FormData(form[0]);
        const submitBtn = $('#create-post-btn');
        
        // Validate required fields
        const title = $('#post-title').val().trim();
        if (!title) {
            showNotification(instafeed_creator_ajax.strings.title_required, 'error');
            return;
        }
        
        // Add AJAX data
        formData.append('action', 'instafeed_create_post');
        formData.append('nonce', instafeed_creator_ajax.nonce);
        
        isLoading = true;
        submitBtn.prop('disabled', true).text(instafeed_creator_ajax.strings.creating_post);
        
        $.ajax({
            url: instafeed_creator_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    closeInstaFeedModal('instafeed-creator-modal');
                    form[0].reset();
                    removeImagePreview();
                    $('#char-count').text('0');
                    
                    // Refresh posts if my posts modal is open
                    if ($('#instafeed-my-posts-modal').is(':visible')) {
                        loadUserPosts();
                    }
                } else {
                    showNotification(response.data || instafeed_creator_ajax.strings.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_creator_ajax.strings.error_occurred, 'error');
            },
            complete: function() {
                isLoading = false;
                submitBtn.prop('disabled', false).text(instafeed_creator_ajax.strings.post_created);
            }
        });
    }

    function handleUpdatePost(e) {
        e.preventDefault();
        
        if (isLoading) return;
        
        const form = $(this);
        const formData = new FormData(form[0]);
        const submitBtn = form.find('button[type="submit"]');
        
        // Validate required fields
        const title = $('#edit-post-title').val().trim();
        if (!title) {
            showNotification(instafeed_creator_ajax.strings.title_required, 'error');
            return;
        }
        
        // Add AJAX data
        formData.append('action', 'instafeed_update_post');
        formData.append('nonce', instafeed_creator_ajax.nonce);
        
        isLoading = true;
        submitBtn.prop('disabled', true).text('Updating...');
        
        $.ajax({
            url: instafeed_creator_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    closeInstaFeedModal('instafeed-edit-post-modal');
                    loadUserPosts();
                } else {
                    showNotification(response.data || instafeed_creator_ajax.strings.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_creator_ajax.strings.error_occurred, 'error');
            },
            complete: function() {
                isLoading = false;
                submitBtn.prop('disabled', false).text(instafeed_creator_ajax.strings.post_updated);
            }
        });
    }

    function loadUserPosts() {
        if (isLoading) return;
        
        isLoading = true;
        $('#posts-loading').show();
        $('#my-posts-grid .instafeed-post-card').remove();
        
        $.ajax({
            url: instafeed_creator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_get_user_posts',
                nonce: instafeed_creator_ajax.nonce,
                status: currentFilter,
                page: 1
            },
            success: function(response) {
                if (response.success) {
                    displayUserPosts(response.data.posts);
                    updatePostsStats(response.data.total);
                    
                    if (response.data.has_more) {
                        $('#posts-pagination').show();
                    } else {
                        $('#posts-pagination').hide();
                    }
                    
                    currentPage = 1;
                } else {
                    showNotification(response.data || 'Failed to load posts', 'error');
                }
            },
            error: function() {
                showNotification('Failed to load posts', 'error');
            },
            complete: function() {
                isLoading = false;
                $('#posts-loading').hide();
            }
        });
    }

    function loadMorePosts() {
        if (isLoading) return;
        
        isLoading = true;
        const loadMoreBtn = $('#load-more-posts');
        loadMoreBtn.prop('disabled', true).text('Loading...');
        
        $.ajax({
            url: instafeed_creator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_get_user_posts',
                nonce: instafeed_creator_ajax.nonce,
                status: currentFilter,
                page: currentPage + 1
            },
            success: function(response) {
                if (response.success) {
                    displayUserPosts(response.data.posts, true);
                    
                    if (!response.data.has_more) {
                        $('#posts-pagination').hide();
                    }
                    
                    currentPage++;
                } else {
                    showNotification(response.data || 'Failed to load more posts', 'error');
                }
            },
            error: function() {
                showNotification('Failed to load more posts', 'error');
            },
            complete: function() {
                isLoading = false;
                loadMoreBtn.prop('disabled', false).text('Load More Posts');
            }
        });
    }

    function displayUserPosts(posts, append = false) {
        const container = $('#my-posts-grid');
        
        if (!append) {
            container.find('.instafeed-post-card').remove();
        }
        
        posts.forEach(function(post) {
            const postCard = createPostCard(post);
            container.append(postCard);
        });
        
        if (posts.length === 0 && !append) {
            container.append('<div class="instafeed-no-posts"><p>No posts found.</p></div>');
        }
    }

    function createPostCard(post) {
        const statusClass = post.status.toLowerCase();
        const statusText = post.status.charAt(0).toUpperCase() + post.status.slice(1);
        
        return `
            <div class="instafeed-post-card" data-post-id="${post.id}">
                <img src="${post.thumbnail}" alt="${post.title}" class="instafeed-post-card-image">
                <div class="instafeed-post-card-content">
                    <h4 class="instafeed-post-card-title">${post.title}</h4>
                    <div class="instafeed-post-card-meta">
                        <span class="instafeed-post-card-date">${post.date}</span>
                        <span class="instafeed-post-card-status ${statusClass}">${statusText}</span>
                    </div>
                    <div class="instafeed-post-card-stats">
                        <small>❤️ ${post.likes} | 💬 ${post.comments}</small>
                    </div>
                    <div class="instafeed-post-card-actions">
                        <button class="instafeed-btn instafeed-btn-secondary edit-post-btn" data-post-id="${post.id}">Edit</button>
                        <button class="instafeed-btn instafeed-btn-danger delete-post-btn" data-post-id="${post.id}">Delete</button>
                    </div>
                </div>
            </div>
        `;
    }

    function updatePostsStats(total) {
        $('#total-posts-count').text(total);
    }

    // Global functions for theme integration
    window.openInstaFeedModal = function(modalId) {
        const modal = $('#' + modalId);
        modal.show().addClass('show');
        $('body').css('overflow', 'hidden');
        
        // Load posts if opening my posts modal
        if (modalId === 'instafeed-my-posts-modal') {
            loadUserPosts();
        }
    };

    window.closeInstaFeedModal = function(modalId) {
        const modal = $('#' + modalId);
        modal.removeClass('show');
        setTimeout(() => {
            modal.hide();
            $('body').css('overflow', 'auto');
        }, 300);
    };

    window.editInstaFeedPost = function(postId) {
        // Load post data and open edit modal
        $.ajax({
            url: instafeed_creator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_get_post_data',
                nonce: instafeed_creator_ajax.nonce,
                post_id: postId
            },
            success: function(response) {
                if (response.success) {
                    const post = response.data;
                    $('#edit-post-id').val(post.id);
                    $('#edit-post-title').val(post.title);
                    $('#edit-post-content').val(post.content);
                    $('#edit-post-tags').val(post.tags);
                    $('#edit-post-category').val(post.category);
                    $('#edit-char-count').text(post.content.length);
                    
                    openInstaFeedModal('instafeed-edit-post-modal');
                } else {
                    showNotification(response.data || 'Failed to load post data', 'error');
                }
            },
            error: function() {
                showNotification('Failed to load post data', 'error');
            }
        });
    };

    window.deleteInstaFeedPost = function(postId) {
        if (!confirm(instafeed_creator_ajax.strings.confirm_delete)) {
            return;
        }
        
        $.ajax({
            url: instafeed_creator_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_delete_post',
                nonce: instafeed_creator_ajax.nonce,
                post_id: postId
            },
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    $(`.instafeed-post-card[data-post-id="${postId}"]`).fadeOut(300, function() {
                        $(this).remove();
                    });
                    
                    // Update stats
                    const currentCount = parseInt($('#total-posts-count').text());
                    $('#total-posts-count').text(Math.max(0, currentCount - 1));
                } else {
                    showNotification(response.data || 'Failed to delete post', 'error');
                }
            },
            error: function() {
                showNotification('Failed to delete post', 'error');
            }
        });
    };

    window.removeImagePreview = removeImagePreview;
    window.filterMyPosts = loadUserPosts;

    function showNotification(message, type = 'info') {
        const notification = $(`
            <div class="instafeed-notification ${type}">
                ${message}
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(() => {
            notification.addClass('show');
        }, 100);
        
        setTimeout(() => {
            notification.removeClass('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

})(jQuery);
