/* InstaFeed Creator Plugin Styles */

/* Modal Base Styles */
.instafeed-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.instafeed-modal.show {
    opacity: 1;
    visibility: visible;
}

.instafeed-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.instafeed-modal-content {
    background: #1a1a1a;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    border: 1px solid #262626;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.instafeed-modal.show .instafeed-modal-content {
    transform: scale(1);
}

.instafeed-modal-large {
    max-width: 900px;
}

/* Modal Header */
.instafeed-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #262626;
    background: #1a1a1a;
    border-radius: 12px 12px 0 0;
}

.instafeed-modal-header h2 {
    color: #ffffff;
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.instafeed-modal-close {
    background: none;
    border: none;
    color: #8e8e8e;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.instafeed-modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

/* Modal Body */
.instafeed-modal-body {
    padding: 20px;
}

/* Form Styles */
.instafeed-form-group {
    margin-bottom: 20px;
}

.instafeed-form-group label {
    display: block;
    color: #ffffff;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
}

.instafeed-form-group input,
.instafeed-form-group textarea,
.instafeed-form-group select {
    width: 100%;
    padding: 12px;
    background: #262626;
    border: 1px solid #404040;
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.instafeed-form-group input:focus,
.instafeed-form-group textarea:focus,
.instafeed-form-group select:focus {
    outline: none;
    border-color: #0095f6;
    box-shadow: 0 0 0 2px rgba(0, 149, 246, 0.2);
}

.instafeed-form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.instafeed-help-text {
    display: block;
    color: #8e8e8e;
    font-size: 12px;
    margin-top: 5px;
}

/* Image Upload Area */
.instafeed-image-upload-area {
    position: relative;
    border: 2px dashed #404040;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.instafeed-image-upload-area:hover {
    border-color: #0095f6;
    background: rgba(0, 149, 246, 0.05);
}

.instafeed-upload-placeholder {
    color: #8e8e8e;
}

.instafeed-upload-icon {
    font-size: 48px;
    display: block;
    margin-bottom: 10px;
}

.instafeed-upload-placeholder p {
    margin: 10px 0 5px;
    font-size: 16px;
    color: #ffffff;
}

.instafeed-upload-placeholder small {
    color: #8e8e8e;
    font-size: 12px;
}

/* Image Preview */
.instafeed-image-preview {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
}

.instafeed-image-preview img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
}

.instafeed-remove-image {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: #ffffff;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.instafeed-remove-image:hover {
    background: rgba(255, 0, 0, 0.7);
}

/* Buttons */
.instafeed-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.instafeed-btn-primary {
    background: #0095f6;
    color: #ffffff;
}

.instafeed-btn-primary:hover {
    background: #1877f2;
    transform: translateY(-1px);
}

.instafeed-btn-secondary {
    background: #262626;
    color: #ffffff;
    border: 1px solid #404040;
}

.instafeed-btn-secondary:hover {
    background: #404040;
}

.instafeed-btn-danger {
    background: #e74c3c;
    color: #ffffff;
}

.instafeed-btn-danger:hover {
    background: #c0392b;
}

/* Form Actions */
.instafeed-form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #262626;
}

/* My Posts Styles */
.instafeed-posts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #262626;
}

.instafeed-posts-stats {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
}

.instafeed-posts-filter {
    margin-bottom: 20px;
}

.instafeed-posts-filter select {
    max-width: 200px;
}

.instafeed-my-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.instafeed-post-card {
    background: #262626;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
    border: 1px solid #404040;
}

.instafeed-post-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.instafeed-post-card-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.instafeed-post-card-content {
    padding: 15px;
}

.instafeed-post-card-title {
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.3;
}

.instafeed-post-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 12px;
    color: #8e8e8e;
}

.instafeed-post-card-status {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.instafeed-post-card-status.published {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

.instafeed-post-card-status.draft {
    background: rgba(241, 196, 15, 0.2);
    color: #f1c40f;
}

.instafeed-post-card-status.pending {
    background: rgba(230, 126, 34, 0.2);
    color: #e67e22;
}

.instafeed-post-card-actions {
    display: flex;
    gap: 8px;
}

.instafeed-post-card-actions .instafeed-btn {
    padding: 6px 12px;
    font-size: 12px;
    flex: 1;
}

/* Loading States */
.instafeed-loading {
    text-align: center;
    padding: 40px;
    color: #8e8e8e;
}

.instafeed-loading span {
    display: inline-block;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Pagination */
.instafeed-posts-pagination {
    text-align: center;
    margin-top: 20px;
}

/* Post Actions */
.instafeed-post-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #262626;
    display: flex;
    gap: 12px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .instafeed-modal-content {
        width: 95%;
        margin: 20px;
        max-height: 95vh;
    }
    
    .instafeed-modal-header,
    .instafeed-modal-body {
        padding: 15px;
    }
    
    .instafeed-form-actions {
        flex-direction: column;
    }
    
    .instafeed-btn {
        width: 100%;
    }
    
    .instafeed-my-posts-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .instafeed-posts-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .instafeed-post-card-actions {
        flex-direction: column;
    }
}
