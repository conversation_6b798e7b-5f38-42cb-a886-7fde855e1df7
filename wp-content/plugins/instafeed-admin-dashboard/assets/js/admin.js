jQuery(document).ready(function($) {
    
    // Plugin activation function
    window.activatePlugin = function(pluginPath) {
        if (confirm('Are you sure you want to activate this plugin?')) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'activate_instafeed_plugin',
                    plugin: pluginPath,
                    nonce: $('#instafeed_nonce').val()
                },
                beforeSend: function() {
                    $('button[onclick*="' + pluginPath + '"]').text('Activating...').prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Failed to activate plugin: ' + response.data);
                        $('button[onclick*="' + pluginPath + '"]').text('Activate').prop('disabled', false);
                    }
                },
                error: function() {
                    alert('Error activating plugin. Please try again.');
                    $('button[onclick*="' + pluginPath + '"]').text('Activate').prop('disabled', false);
                }
            });
        }
    };
    
    // Plugin deactivation function
    window.deactivatePlugin = function(pluginPath) {
        if (confirm('Are you sure you want to deactivate this plugin?')) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'deactivate_instafeed_plugin',
                    plugin: pluginPath,
                    nonce: $('#instafeed_nonce').val()
                },
                beforeSend: function() {
                    $('button[onclick*="' + pluginPath + '"]').text('Deactivating...').prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Failed to deactivate plugin: ' + response.data);
                        $('button[onclick*="' + pluginPath + '"]').text('Deactivate').prop('disabled', false);
                    }
                },
                error: function() {
                    alert('Error deactivating plugin. Please try again.');
                    $('button[onclick*="' + pluginPath + '"]').text('Deactivate').prop('disabled', false);
                }
            });
        }
    };
    
    // Auto-refresh stats every 30 seconds
    setInterval(function() {
        refreshStats();
    }, 30000);
    
    function refreshStats() {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_instafeed_stats',
                nonce: $('#instafeed_nonce').val()
            },
            success: function(response) {
                if (response.success) {
                    updateStatsDisplay(response.data);
                }
            }
        });
    }
    
    function updateStatsDisplay(stats) {
        $('.instafeed-stat-card').each(function() {
            var $card = $(this);
            var $number = $card.find('.stat-content h3');
            var currentText = $number.text().toLowerCase();
            
            if (currentText.includes('users')) {
                $number.text(numberFormat(stats.users));
            } else if (currentText.includes('posts')) {
                $number.text(numberFormat(stats.posts));
            } else if (currentText.includes('interactions')) {
                $number.text(numberFormat(stats.interactions));
            }
        });
    }
    
    function numberFormat(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
    
    // Tooltips for status badges
    $('.status-badge').each(function() {
        var $badge = $(this);
        var isActive = $badge.hasClass('active');
        var tooltip = isActive ? 'Plugin is active and working' : 'Plugin is not active';
        
        $badge.attr('title', tooltip);
    });
    
    // Enhanced button interactions
    $('.action-button').on('mouseenter', function() {
        $(this).find('.dashicons').addClass('dashicons-spin');
    }).on('mouseleave', function() {
        $(this).find('.dashicons').removeClass('dashicons-spin');
    });
    
    // Plugin status animations
    $('.plugin-status-item').each(function() {
        var $item = $(this);
        var isActive = $item.hasClass('active');
        
        if (isActive) {
            $item.find('.plugin-icon').css({
                'animation': 'pulse 2s infinite',
                'background': 'linear-gradient(45deg, #46b450, #5cbf60)'
            });
        }
    });
    
    // Add CSS animations
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            
            .dashicons-spin {
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            
            .instafeed-stat-card:hover .stat-icon {
                animation: bounce 0.6s ease;
            }
            
            @keyframes bounce {
                0%, 20%, 60%, 100% { transform: translateY(0); }
                40% { transform: translateY(-10px); }
                80% { transform: translateY(-5px); }
            }
        `)
        .appendTo('head');
    
    // Dashboard welcome message
    if (localStorage.getItem('instafeed_welcome_shown') !== 'true') {
        setTimeout(function() {
            if (confirm('Welcome to InstaFeed! Would you like a quick tour of the dashboard features?')) {
                showDashboardTour();
            }
            localStorage.setItem('instafeed_welcome_shown', 'true');
        }, 1000);
    }
    
    function showDashboardTour() {
        var tourSteps = [
            {
                element: '.instafeed-stats-grid',
                message: 'Here you can see your site statistics at a glance.'
            },
            {
                element: '.plugin-status-list',
                message: 'This shows the status of all InstaFeed plugins and lets you manage them.'
            },
            {
                element: '.quick-actions',
                message: 'Quick actions for common tasks.'
            },
            {
                element: '.recent-activity',
                message: 'Recent activity on your site.'
            }
        ];
        
        var currentStep = 0;
        
        function showStep(step) {
            if (step >= tourSteps.length) return;
            
            var tourStep = tourSteps[step];
            var $element = $(tourStep.element);
            
            if ($element.length) {
                $element.css({
                    'position': 'relative',
                    'z-index': '9999'
                });
                
                var $tooltip = $('<div class="tour-tooltip">')
                    .html(tourStep.message + '<br><button onclick="nextTourStep()">Next</button>')
                    .css({
                        'position': 'absolute',
                        'top': '-50px',
                        'left': '50%',
                        'transform': 'translateX(-50%)',
                        'background': '#0073aa',
                        'color': 'white',
                        'padding': '10px',
                        'border-radius': '5px',
                        'z-index': '10000',
                        'max-width': '200px',
                        'text-align': 'center'
                    });
                
                $element.append($tooltip);
                
                window.nextTourStep = function() {
                    $('.tour-tooltip').remove();
                    showStep(currentStep + 1);
                    currentStep++;
                };
            }
        }
        
        showStep(0);
    }
    
    console.log('InstaFeed Admin Dashboard loaded successfully');
});
