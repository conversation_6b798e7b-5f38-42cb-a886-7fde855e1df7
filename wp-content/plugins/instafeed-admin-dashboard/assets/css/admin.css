/* InstaFeed Admin Dashboard Styles */

.instafeed-dashboard {
    background: #f1f1f1;
    margin: 0 -20px;
    padding: 20px;
}

.instafeed-dashboard-header {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.instafeed-dashboard-header h1 {
    color: #0073aa;
    margin: 0 0 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.instafeed-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.instafeed-stat-card {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease;
}

.instafeed-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
}

.stat-content h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #0073aa;
}

.stat-content p {
    margin: 5px 0 0;
    color: #666;
    font-size: 14px;
}

.instafeed-dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.instafeed-card {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.instafeed-card h2 {
    margin: 0 0 20px;
    color: #23282d;
    font-size: 18px;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.plugin-status-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.plugin-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.plugin-status-item.active {
    border-color: #46b450;
    background: #f7fcf7;
}

.plugin-status-item.inactive {
    border-color: #dc3232;
    background: #fef7f7;
}

.plugin-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.plugin-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 50%;
}

.plugin-info h4 {
    margin: 0 0 5px;
    color: #23282d;
}

.plugin-info p {
    margin: 0;
    color: #666;
    font-size: 13px;
}

.plugin-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: #46b450;
    color: #fff;
}

.status-badge.inactive {
    background: #dc3232;
    color: #fff;
}

.quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.action-button {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    text-decoration: none;
    color: #0073aa;
    transition: all 0.2s ease;
}

.action-button:hover {
    background: #0073aa;
    color: #fff;
    text-decoration: none;
}

.recent-activity {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.activity-icon {
    font-size: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 50%;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 5px;
}

.activity-title {
    font-weight: 600;
    color: #0073aa;
}

.activity-time {
    color: #666;
    font-size: 12px;
}

.activity-actions {
    display: flex;
    gap: 5px;
}

.button-small {
    padding: 4px 8px;
    font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .instafeed-dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .instafeed-stats-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .plugin-status-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .plugin-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

@media (max-width: 480px) {
    .instafeed-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }
}
