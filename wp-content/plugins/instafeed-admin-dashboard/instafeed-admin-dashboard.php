<?php
/**
 * Plugin Name: InstaFeed Admin Dashboard
 * Plugin URI: https://example.com
 * Description: Unified admin dashboard for all InstaFeed plugins
 * Version: 1.0.0
 * Author: InstaFeed Team
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('INSTAFEED_ADMIN_VERSION', '1.0.0');
define('INSTAFEED_ADMIN_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('INSTAFEED_ADMIN_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * Main InstaFeed Admin Dashboard Class
 */
class InstaFeed_Admin_Dashboard {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_init', array($this, 'init_settings'));
        add_action('wp_ajax_activate_instafeed_plugin', array($this, 'activate_plugin_ajax'));
        add_action('wp_ajax_deactivate_instafeed_plugin', array($this, 'deactivate_plugin_ajax'));
        add_action('wp_ajax_get_instafeed_stats', array($this, 'get_stats_ajax'));
    }
    
    public function add_admin_menu() {
        // Main menu
        add_menu_page(
            'InstaFeed Dashboard',
            'InstaFeed',
            'manage_options',
            'instafeed-dashboard',
            array($this, 'dashboard_page'),
            'dashicons-camera',
            30
        );
        
        // Submenu pages
        add_submenu_page(
            'instafeed-dashboard',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'instafeed-dashboard',
            array($this, 'dashboard_page')
        );
        
        add_submenu_page(
            'instafeed-dashboard',
            'Profile Settings',
            'Profile',
            'manage_options',
            'instafeed-profile-settings',
            array($this, 'profile_settings_page')
        );
        
        add_submenu_page(
            'instafeed-dashboard',
            'Interactions',
            'Interactions',
            'manage_options',
            'instafeed-interactions-settings',
            array($this, 'interactions_settings_page')
        );
        
        add_submenu_page(
            'instafeed-dashboard',
            'Creator Tools',
            'Creator',
            'manage_options',
            'instafeed-creator-settings',
            array($this, 'creator_settings_page')
        );
        
        add_submenu_page(
            'instafeed-dashboard',
            'Analytics',
            'Analytics',
            'manage_options',
            'instafeed-analytics',
            array($this, 'analytics_page')
        );
        
        add_submenu_page(
            'instafeed-dashboard',
            'Settings',
            'Settings',
            'manage_options',
            'instafeed-global-settings',
            array($this, 'global_settings_page')
        );
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'instafeed') !== false) {
            wp_enqueue_style(
                'instafeed-admin-style',
                INSTAFEED_ADMIN_PLUGIN_URL . 'assets/css/admin.css',
                array(),
                INSTAFEED_ADMIN_VERSION
            );
            
            wp_enqueue_script(
                'instafeed-admin-script',
                INSTAFEED_ADMIN_PLUGIN_URL . 'assets/js/admin.js',
                array('jquery'),
                INSTAFEED_ADMIN_VERSION,
                true
            );
        }
    }
    
    public function init_settings() {
        // Register global settings
        register_setting('instafeed_global_settings', 'instafeed_theme_integration');
        register_setting('instafeed_global_settings', 'instafeed_auto_activate_plugins');
        register_setting('instafeed_global_settings', 'instafeed_debug_mode');
    }
    
    public function dashboard_page() {
        include INSTAFEED_ADMIN_PLUGIN_DIR . 'includes/dashboard-page.php';
    }
    
    public function profile_settings_page() {
        // Redirect to profile plugin settings if available
        if (class_exists('InstaFeed_Profile_Admin')) {
            $profile_admin = new InstaFeed_Profile_Admin();
            $profile_admin->settings_page();
        } else {
            echo '<div class="wrap"><h1>Profile Plugin Not Active</h1><p>Please activate the InstaFeed Profile plugin.</p></div>';
        }
    }
    
    public function interactions_settings_page() {
        // Redirect to interactions plugin settings if available
        if (class_exists('InstaFeed_Interactions_Admin')) {
            $interactions_admin = new InstaFeed_Interactions_Admin();
            $interactions_admin->settings_page();
        } else {
            echo '<div class="wrap"><h1>Interactions Plugin Not Active</h1><p>Please activate the InstaFeed Interactions plugin.</p></div>';
        }
    }
    
    public function creator_settings_page() {
        // Redirect to creator plugin settings if available
        if (class_exists('InstaFeed_Creator_Admin')) {
            $creator_admin = new InstaFeed_Creator_Admin();
            $creator_admin->settings_page();
        } else {
            echo '<div class="wrap"><h1>Creator Plugin Not Active</h1><p>Please activate the InstaFeed Creator plugin.</p></div>';
        }
    }
    
    public function analytics_page() {
        include INSTAFEED_ADMIN_PLUGIN_DIR . 'includes/analytics-page.php';
    }
    
    public function global_settings_page() {
        include INSTAFEED_ADMIN_PLUGIN_DIR . 'includes/global-settings-page.php';
    }

    public function activate_plugin_ajax() {
        check_ajax_referer('instafeed_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $plugin = sanitize_text_field($_POST['plugin']);
        $result = activate_plugin($plugin);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success('Plugin activated successfully');
        }
    }

    public function deactivate_plugin_ajax() {
        check_ajax_referer('instafeed_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $plugin = sanitize_text_field($_POST['plugin']);
        deactivate_plugins($plugin);
        wp_send_json_success('Plugin deactivated successfully');
    }

    public function get_stats_ajax() {
        check_ajax_referer('instafeed_nonce', 'nonce');

        global $wpdb;

        $stats = array(
            'users' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->users}"),
            'posts' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'publish' AND post_type = 'post'"),
            'interactions' => 0
        );

        // Get interaction stats if available
        if (is_plugin_active('instafeed-interactions/instafeed-interactions.php')) {
            $interactions_table = $wpdb->prefix . 'instafeed_post_interactions';
            if ($wpdb->get_var("SHOW TABLES LIKE '$interactions_table'") == $interactions_table) {
                $stats['interactions'] = $wpdb->get_var("SELECT COUNT(*) FROM $interactions_table");
            }
        }

        wp_send_json_success($stats);
    }
}

// Initialize the plugin
new InstaFeed_Admin_Dashboard();
