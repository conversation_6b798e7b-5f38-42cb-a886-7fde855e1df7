<?php
// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get plugin status
$plugins_status = array(
    'profile' => is_plugin_active('instafeed-profile/instafeed-profile.php'),
    'interactions' => is_plugin_active('instafeed-interactions/instafeed-interactions.php'),
    'creator' => is_plugin_active('instafeed-creator/instafeed-creator.php'),
    'comments' => is_plugin_active('instafeed-comments/instafeed-comments.php'),
);

// Get statistics
global $wpdb;
$total_users = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->users}");
$total_posts = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'publish' AND post_type = 'post'");

// Get interaction stats if available
$total_interactions = 0;
if ($plugins_status['interactions']) {
    $interactions_table = $wpdb->prefix . 'instafeed_post_interactions';
    if ($wpdb->get_var("SHOW TABLES LIKE '$interactions_table'") == $interactions_table) {
        $total_interactions = $wpdb->get_var("SELECT COUNT(*) FROM $interactions_table");
    }
}
?>

<div class="wrap instafeed-dashboard">
    <input type="hidden" id="instafeed_nonce" value="<?php echo wp_create_nonce('instafeed_nonce'); ?>">

    <h1 class="wp-heading-inline">
        <span class="dashicons dashicons-camera"></span>
        InstaFeed Dashboard
    </h1>
    
    <div class="instafeed-dashboard-header">
        <p class="description">
            Welcome to your InstaFeed control center. Manage all your Instagram-style features from one place.
        </p>
    </div>

    <!-- Quick Stats -->
    <div class="instafeed-stats-grid">
        <div class="instafeed-stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-content">
                <h3><?php echo number_format($total_users); ?></h3>
                <p>Total Users</p>
            </div>
        </div>
        
        <div class="instafeed-stat-card">
            <div class="stat-icon">📸</div>
            <div class="stat-content">
                <h3><?php echo number_format($total_posts); ?></h3>
                <p>Total Posts</p>
            </div>
        </div>
        
        <div class="instafeed-stat-card">
            <div class="stat-icon">❤️</div>
            <div class="stat-content">
                <h3><?php echo number_format($total_interactions); ?></h3>
                <p>Total Interactions</p>
            </div>
        </div>
        
        <div class="instafeed-stat-card">
            <div class="stat-icon">🔌</div>
            <div class="stat-content">
                <h3><?php echo count(array_filter($plugins_status)); ?>/<?php echo count($plugins_status); ?></h3>
                <p>Active Plugins</p>
            </div>
        </div>
    </div>

    <!-- Plugin Status -->
    <div class="instafeed-dashboard-grid">
        <div class="instafeed-card">
            <h2>Plugin Status</h2>
            <div class="plugin-status-list">
                <div class="plugin-status-item <?php echo $plugins_status['profile'] ? 'active' : 'inactive'; ?>">
                    <div class="plugin-info">
                        <span class="plugin-icon">👤</span>
                        <div>
                            <h4>InstaFeed Profile</h4>
                            <p>User profiles, avatars, and social features</p>
                        </div>
                    </div>
                    <div class="plugin-actions">
                        <?php if ($plugins_status['profile']) : ?>
                            <span class="status-badge active">Active</span>
                            <a href="<?php echo admin_url('admin.php?page=instafeed-profile-settings'); ?>" class="button">Settings</a>
                        <?php else : ?>
                            <span class="status-badge inactive">Inactive</span>
                            <button class="button button-primary" onclick="activatePlugin('instafeed-profile/instafeed-profile.php')">Activate</button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="plugin-status-item <?php echo $plugins_status['interactions'] ? 'active' : 'inactive'; ?>">
                    <div class="plugin-info">
                        <span class="plugin-icon">❤️</span>
                        <div>
                            <h4>InstaFeed Interactions</h4>
                            <p>Likes, dislikes, bookmarks, and sharing</p>
                        </div>
                    </div>
                    <div class="plugin-actions">
                        <?php if ($plugins_status['interactions']) : ?>
                            <span class="status-badge active">Active</span>
                            <a href="<?php echo admin_url('admin.php?page=instafeed-interactions-settings'); ?>" class="button">Settings</a>
                        <?php else : ?>
                            <span class="status-badge inactive">Inactive</span>
                            <button class="button button-primary" onclick="activatePlugin('instafeed-interactions/instafeed-interactions.php')">Activate</button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="plugin-status-item <?php echo $plugins_status['creator'] ? 'active' : 'inactive'; ?>">
                    <div class="plugin-info">
                        <span class="plugin-icon">✏️</span>
                        <div>
                            <h4>InstaFeed Creator</h4>
                            <p>Post creation and content management</p>
                        </div>
                    </div>
                    <div class="plugin-actions">
                        <?php if ($plugins_status['creator']) : ?>
                            <span class="status-badge active">Active</span>
                            <a href="<?php echo admin_url('admin.php?page=instafeed-creator-settings'); ?>" class="button">Settings</a>
                        <?php else : ?>
                            <span class="status-badge inactive">Inactive</span>
                            <button class="button button-primary" onclick="activatePlugin('instafeed-creator/instafeed-creator.php')">Activate</button>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="plugin-status-item <?php echo $plugins_status['comments'] ? 'active' : 'inactive'; ?>">
                    <div class="plugin-info">
                        <span class="plugin-icon">💬</span>
                        <div>
                            <h4>InstaFeed Comments</h4>
                            <p>Enhanced commenting system</p>
                        </div>
                    </div>
                    <div class="plugin-actions">
                        <?php if ($plugins_status['comments']) : ?>
                            <span class="status-badge active">Active</span>
                            <a href="<?php echo admin_url('admin.php?page=instafeed-comments-settings'); ?>" class="button">Settings</a>
                        <?php else : ?>
                            <span class="status-badge inactive">Inactive</span>
                            <button class="button button-primary" onclick="activatePlugin('instafeed-comments/instafeed-comments.php')">Activate</button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="instafeed-card">
            <h2>Quick Actions</h2>
            <div class="quick-actions">
                <a href="<?php echo admin_url('admin.php?page=instafeed-global-settings'); ?>" class="action-button">
                    <span class="dashicons dashicons-admin-settings"></span>
                    Global Settings
                </a>
                
                <a href="<?php echo admin_url('admin.php?page=instafeed-analytics'); ?>" class="action-button">
                    <span class="dashicons dashicons-chart-bar"></span>
                    View Analytics
                </a>
                
                <a href="<?php echo home_url(); ?>" class="action-button" target="_blank">
                    <span class="dashicons dashicons-external"></span>
                    View Site
                </a>
                
                <a href="<?php echo admin_url('customize.php'); ?>" class="action-button">
                    <span class="dashicons dashicons-admin-customizer"></span>
                    Customize Theme
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="instafeed-card">
        <h2>Recent Activity</h2>
        <div class="recent-activity">
            <?php
            // Get recent posts
            $recent_posts = get_posts(array(
                'numberposts' => 5,
                'post_status' => 'publish'
            ));
            
            if ($recent_posts) :
                foreach ($recent_posts as $post) :
            ?>
                <div class="activity-item">
                    <div class="activity-icon">📸</div>
                    <div class="activity-content">
                        <p><strong><?php echo get_the_author_meta('display_name', $post->post_author); ?></strong> published a new post</p>
                        <p class="activity-title"><?php echo esc_html($post->post_title); ?></p>
                        <p class="activity-time"><?php echo human_time_diff(strtotime($post->post_date), current_time('timestamp')) . ' ago'; ?></p>
                    </div>
                    <div class="activity-actions">
                        <a href="<?php echo get_edit_post_link($post->ID); ?>" class="button button-small">Edit</a>
                        <a href="<?php echo get_permalink($post->ID); ?>" class="button button-small" target="_blank">View</a>
                    </div>
                </div>
            <?php 
                endforeach;
            else :
            ?>
                <p>No recent activity found.</p>
            <?php endif; ?>
        </div>
    </div>
</div>
