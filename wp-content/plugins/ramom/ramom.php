<?php
/**
 * Plugin Name: Ramom - Random Post Generator & User Interaction
 * Plugin URI: https://example.com/ramom
 * Description: Generates random posts automatically and enables user interactions like likes, comments, and follows. Perfect for social media-style WordPress sites.
 * Version: 1.0.0
 * Author: Custom Development
 * License: GPL v2 or later
 * Text Domain: ramom
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('RAMOM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('RAMOM_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('RAMOM_VERSION', '1.0.0');

// Include required files
require_once RAMOM_PLUGIN_PATH . 'includes/class-ramom-core.php';
require_once RAMOM_PLUGIN_PATH . 'includes/class-ramom-post-generator.php';
require_once RAMOM_PLUGIN_PATH . 'includes/class-ramom-user-interactions.php';
require_once RAMOM_PLUGIN_PATH . 'includes/class-ramom-admin.php';
require_once RAMOM_PLUGIN_PATH . 'includes/class-ramom-ajax.php';

/**
 * Main Ramom Plugin Class
 */
class Ramom_Plugin {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Initialize plugin components
        new Ramom_Core();
        new Ramom_Post_Generator();
        new Ramom_User_Interactions();
        new Ramom_Admin();
        new Ramom_Ajax();
        
        // Load text domain
        load_plugin_textdomain('ramom', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function enqueue_scripts() {
        // Enqueue CSS
        wp_enqueue_style(
            'ramom-style',
            RAMOM_PLUGIN_URL . 'assets/css/ramom.css',
            array(),
            RAMOM_VERSION
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'ramom-script',
            RAMOM_PLUGIN_URL . 'assets/js/ramom.js',
            array('jquery'),
            RAMOM_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('ramom-script', 'ramom_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ramom_nonce'),
            'user_id' => get_current_user_id(),
            'strings' => array(
                'like' => __('Like', 'ramom'),
                'unlike' => __('Unlike', 'ramom'),
                'follow' => __('Follow', 'ramom'),
                'unfollow' => __('Unfollow', 'ramom'),
                'loading' => __('Loading...', 'ramom'),
                'error' => __('Something went wrong', 'ramom'),
            )
        ));
    }
    
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Set default options
        add_option('ramom_auto_post_enabled', 1);
        add_option('ramom_auto_post_interval', 'hourly');
        add_option('ramom_post_categories', array('random', 'auto-generated'));
        add_option('ramom_enable_likes', 1);
        add_option('ramom_enable_follows', 1);
        add_option('ramom_enable_user_posts', 1);
        
        // Schedule auto-posting
        if (!wp_next_scheduled('ramom_auto_post_event')) {
            wp_schedule_event(time(), 'hourly', 'ramom_auto_post_event');
        }
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('ramom_auto_post_event');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Likes table
        $likes_table = $wpdb->prefix . 'ramom_likes';
        $likes_sql = "CREATE TABLE $likes_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            post_id bigint(20) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_post (user_id, post_id),
            KEY user_id (user_id),
            KEY post_id (post_id)
        ) $charset_collate;";
        
        // Follows table
        $follows_table = $wpdb->prefix . 'ramom_follows';
        $follows_sql = "CREATE TABLE $follows_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            follower_id bigint(20) NOT NULL,
            following_id bigint(20) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY follower_following (follower_id, following_id),
            KEY follower_id (follower_id),
            KEY following_id (following_id)
        ) $charset_collate;";
        
        // User activity table
        $activity_table = $wpdb->prefix . 'ramom_user_activity';
        $activity_sql = "CREATE TABLE $activity_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            activity_type varchar(50) NOT NULL,
            object_id bigint(20) NOT NULL,
            object_type varchar(50) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY activity_type (activity_type),
            KEY object_id (object_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($likes_sql);
        dbDelta($follows_sql);
        dbDelta($activity_sql);
    }
}

// Initialize the plugin
function ramom_init() {
    return Ramom_Plugin::get_instance();
}

// Start the plugin
ramom_init();

// Hook for auto-posting
add_action('ramom_auto_post_event', 'ramom_create_auto_post');

function ramom_create_auto_post() {
    if (get_option('ramom_auto_post_enabled')) {
        $generator = new Ramom_Post_Generator();
        $generator->create_random_post();
    }
}
