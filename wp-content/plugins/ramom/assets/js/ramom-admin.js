/**
 * Ramom Admin JavaScript
 * Handles admin interface interactions and AJAX requests
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        initializeRamomAdmin();
    });

    function initializeRamomAdmin() {
        bindAdminEvents();
        initializeCharts();
        loadInitialStats();
    }

    function bindAdminEvents() {
        // Generate post button
        $(document).on('click', '#ramom-generate-single-post', function(e) {
            e.preventDefault();
            generateSinglePost();
        });

        // Bulk generate posts
        $(document).on('click', '#ramom-bulk-generate', function(e) {
            e.preventDefault();
            bulkGeneratePosts();
        });

        // Delete all generated posts
        $(document).on('click', '#ramom-delete-generated', function(e) {
            e.preventDefault();
            if (confirm(ramom_admin_ajax.strings.confirm_delete)) {
                deleteGeneratedPosts();
            }
        });

        // Media library image selector
        $(document).on('click', '.ramom-select-image', function(e) {
            e.preventDefault();
            openMediaLibrary($(this));
        });

        // Refresh stats
        $(document).on('click', '#ramom-refresh-stats', function(e) {
            e.preventDefault();
            loadStats();
        });

        // Auto-refresh toggle
        $(document).on('change', '#ramom-auto-refresh', function() {
            toggleAutoRefresh($(this).is(':checked'));
        });

        // Quick actions
        $(document).on('click', '.ramom-quick-action', function() {
            const action = $(this).data('action');
            handleQuickAction(action);
        });
    }

    function generateSinglePost() {
        showLoadingOverlay('Generating post...');

        $.ajax({
            url: ramom_admin_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ramom_admin_generate_post',
                nonce: ramom_admin_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showAlert('Post generated successfully!', 'success');
                    updatePostCount();
                } else {
                    showAlert(response.data || 'Failed to generate post', 'error');
                }
            },
            error: function() {
                showAlert('Error generating post', 'error');
            },
            complete: function() {
                hideLoadingOverlay();
            }
        });
    }

    function bulkGeneratePosts() {
        const count = $('#ramom-bulk-count').val() || 10;
        const maxCount = 50; // Safety limit

        if (count > maxCount) {
            showAlert(`Maximum ${maxCount} posts allowed at once`, 'warning');
            return;
        }

        showLoadingOverlay(`Generating ${count} posts...`);
        
        let generated = 0;
        let errors = 0;

        function generateNext() {
            if (generated + errors >= count) {
                hideLoadingOverlay();
                showAlert(`Generated ${generated} posts successfully${errors > 0 ? ` (${errors} errors)` : ''}`, 'success');
                updatePostCount();
                return;
            }

            $.ajax({
                url: ramom_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'ramom_admin_generate_post',
                    nonce: ramom_admin_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        generated++;
                    } else {
                        errors++;
                    }
                    updateProgress((generated + errors) / count * 100);
                    setTimeout(generateNext, 500); // Delay to prevent server overload
                },
                error: function() {
                    errors++;
                    updateProgress((generated + errors) / count * 100);
                    setTimeout(generateNext, 500);
                }
            });
        }

        generateNext();
    }

    function deleteGeneratedPosts() {
        showLoadingOverlay(ramom_admin_ajax.strings.deleting);

        $.ajax({
            url: ramom_admin_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ramom_delete_generated_posts',
                nonce: ramom_admin_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showAlert(response.data.message, 'success');
                    updatePostCount();
                    // Refresh page to update post list
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    showAlert(response.data || 'Failed to delete posts', 'error');
                }
            },
            error: function() {
                showAlert('Error deleting posts', 'error');
            },
            complete: function() {
                hideLoadingOverlay();
            }
        });
    }

    function openMediaLibrary($button) {
        var mediaUploader;

        if (mediaUploader) {
            mediaUploader.open();
            return;
        }

        mediaUploader = wp.media({
            title: ramom_admin_ajax.strings.select_image,
            button: {
                text: ramom_admin_ajax.strings.use_image
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });

        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            $button.siblings('.ramom-selected-image').remove();
            $button.after('<div class="ramom-selected-image"><img src="' + attachment.sizes.thumbnail.url + '" style="max-width: 100px; margin: 10px 0;" /><input type="hidden" name="selected_image_id" value="' + attachment.id + '" /></div>');
        });

        mediaUploader.open();
    }

    function loadStats() {
        $.ajax({
            url: ramom_admin_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ramom_admin_get_stats',
                nonce: ramom_admin_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateStatsDisplay(response.data);
                }
            }
        });
    }

    function loadInitialStats() {
        // Load stats on page load
        loadStats();
    }

    function updateStatsDisplay(stats) {
        // Update stat cards
        $('.ramom-stat-card').each(function() {
            const $card = $(this);
            const statType = $card.data('stat-type');
            const period = $card.data('period') || 'total';
            
            if (stats[period] && stats[period][statType] !== undefined) {
                $card.find('.ramom-stat-number').text(formatNumber(stats[period][statType]));
            }
        });

        // Update charts if they exist
        if (window.ramomCharts) {
            updateCharts(stats);
        }
    }

    function updatePostCount() {
        // Refresh the generated posts count
        $.ajax({
            url: ramom_admin_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'ramom_get_generated_count',
                nonce: ramom_admin_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#ramom-generated-count').text(response.data.count);
                }
            }
        });
    }

    function handleQuickAction(action) {
        switch (action) {
            case 'generate_post':
                generateSinglePost();
                break;
            case 'view_activity':
                window.location.href = 'admin.php?page=ramom-activity';
                break;
            case 'view_stats':
                window.location.href = 'admin.php?page=ramom-stats';
                break;
            case 'settings':
                window.location.href = 'admin.php?page=ramom-settings';
                break;
        }
    }

    function toggleAutoRefresh(enabled) {
        if (enabled) {
            window.ramomAutoRefresh = setInterval(loadStats, 30000); // Refresh every 30 seconds
        } else {
            if (window.ramomAutoRefresh) {
                clearInterval(window.ramomAutoRefresh);
            }
        }
    }

    function initializeCharts() {
        // Initialize charts if Chart.js is available
        if (typeof Chart !== 'undefined') {
            initActivityChart();
            initEngagementChart();
        }
    }

    function initActivityChart() {
        const ctx = document.getElementById('ramom-activity-chart');
        if (!ctx) return;

        window.ramomCharts = window.ramomCharts || {};
        window.ramomCharts.activity = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Daily Activity',
                    data: [],
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    function initEngagementChart() {
        const ctx = document.getElementById('ramom-engagement-chart');
        if (!ctx) return;

        window.ramomCharts = window.ramomCharts || {};
        window.ramomCharts.engagement = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Likes', 'Follows', 'Shares'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: ['#e91e63', '#2196F3', '#4caf50']
                }]
            },
            options: {
                responsive: true
            }
        });
    }

    function updateCharts(stats) {
        // Update charts with new data
        if (window.ramomCharts && window.ramomCharts.engagement) {
            window.ramomCharts.engagement.data.datasets[0].data = [
                stats.total.likes || 0,
                stats.total.follows || 0,
                stats.total.shares || 0
            ];
            window.ramomCharts.engagement.update();
        }
    }

    function showLoadingOverlay(message) {
        const overlay = `
            <div class="ramom-loading-overlay">
                <div>
                    <div class="ramom-spinner"></div>
                    <p style="margin-top: 15px; font-weight: 500;">${message}</p>
                    <div class="ramom-progress-bar" style="width: 200px; margin-top: 10px;">
                        <div class="ramom-progress-fill" id="ramom-progress-fill" style="width: 0%;"></div>
                    </div>
                </div>
            </div>
        `;
        $('body').append(overlay);
    }

    function hideLoadingOverlay() {
        $('.ramom-loading-overlay').remove();
    }

    function updateProgress(percentage) {
        $('#ramom-progress-fill').css('width', percentage + '%');
    }

    function showAlert(message, type = 'info') {
        const alert = `
            <div class="ramom-alert ${type}" style="margin: 15px 0;">
                ${message}
            </div>
        `;
        
        // Remove existing alerts
        $('.ramom-alert').remove();
        
        // Add new alert
        $('.wrap h1').after(alert);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            $('.ramom-alert').fadeOut(() => {
                $('.ramom-alert').remove();
            });
        }, 5000);
    }

    function formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    // Export functions for global access
    window.ramomAdmin = {
        generatePost: generateSinglePost,
        loadStats: loadStats,
        showAlert: showAlert
    };

})(jQuery);
