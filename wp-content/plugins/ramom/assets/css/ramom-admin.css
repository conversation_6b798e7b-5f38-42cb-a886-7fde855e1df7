/* Ramom Admin Styles */

.ramom-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.ramom-stat-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease;
}

.ramom-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ramom-stat-card h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ramom-stat-number {
    font-size: 32px;
    font-weight: 600;
    color: #1d2327;
    margin: 0;
}

.ramom-dashboard-stats ul {
    margin: 10px 0;
    padding-left: 20px;
}

.ramom-dashboard-stats li {
    margin: 5px 0;
    color: #646970;
}

.ramom-admin-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    margin: 20px 0;
    padding: 20px;
}

.ramom-admin-section h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.ramom-generate-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    margin: 15px 0;
}

.ramom-generate-controls input[type="number"] {
    width: 100px;
}

.ramom-activity-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.ramom-activity-table th,
.ramom-activity-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.ramom-activity-table th {
    background: #f6f7f7;
    font-weight: 600;
    color: #1d2327;
}

.ramom-activity-table tr:hover {
    background: #f9f9f9;
}

.ramom-status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.ramom-status-indicator.active {
    background: #00a32a;
}

.ramom-status-indicator.inactive {
    background: #dba617;
}

.ramom-status-indicator.error {
    background: #d63638;
}

.ramom-settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 20px 0;
}

.ramom-settings-section {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.ramom-settings-section h3 {
    margin-top: 0;
    color: #1d2327;
}

.ramom-toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-right: 10px;
}

.ramom-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ramom-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.ramom-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .ramom-toggle-slider {
    background-color: #2196F3;
}

input:checked + .ramom-toggle-slider:before {
    transform: translateX(26px);
}

.ramom-progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.ramom-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2196F3, #21CBF3);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.ramom-alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin: 15px 0;
    border-left: 4px solid;
}

.ramom-alert.info {
    background: #e7f3ff;
    border-color: #2196F3;
    color: #0c5aa6;
}

.ramom-alert.success {
    background: #e8f5e8;
    border-color: #00a32a;
    color: #00a32a;
}

.ramom-alert.warning {
    background: #fff8e1;
    border-color: #dba617;
    color: #b8860b;
}

.ramom-alert.error {
    background: #ffeaea;
    border-color: #d63638;
    color: #d63638;
}

.ramom-button-group {
    display: flex;
    gap: 10px;
    margin: 15px 0;
}

.ramom-button-group .button {
    flex: 1;
}

.ramom-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100000;
}

.ramom-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2196F3;
    border-radius: 50%;
    animation: ramom-spin 1s linear infinite;
}

@keyframes ramom-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.ramom-chart-container {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.ramom-chart-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #1d2327;
}

.ramom-quick-actions {
    display: flex;
    gap: 15px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.ramom-quick-action {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 150px;
}

.ramom-quick-action:hover {
    border-color: #2196F3;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

.ramom-quick-action-icon {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.ramom-quick-action-label {
    font-weight: 500;
    color: #1d2327;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ramom-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .ramom-settings-grid {
        grid-template-columns: 1fr;
    }
    
    .ramom-generate-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .ramom-button-group {
        flex-direction: column;
    }
    
    .ramom-quick-actions {
        flex-direction: column;
    }
    
    .ramom-quick-action {
        min-width: auto;
    }
}
