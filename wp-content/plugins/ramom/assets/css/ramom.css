/* Ramom Plugin Styles */

/* Interaction Buttons */
.ramom-interactions {
    display: flex;
    gap: 15px;
    align-items: center;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.ramom-interactions button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    background: #fff;
    color: #495057;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ramom-interactions button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Like Button */
.ramom-like-btn {
    border: 2px solid #e91e63 !important;
    color: #e91e63 !important;
}

.ramom-like-btn:hover {
    background: #e91e63 !important;
    color: white !important;
}

.ramom-like-btn.ramom-liked {
    background: #e91e63 !important;
    color: white !important;
}

.ramom-like-icon {
    font-size: 16px;
}

/* Follow Button */
.ramom-follow-btn {
    border: 2px solid #007bff !important;
    color: #007bff !important;
}

.ramom-follow-btn:hover {
    background: #007bff !important;
    color: white !important;
}

.ramom-follow-btn.ramom-following {
    background: #007bff !important;
    color: white !important;
}

/* Share Button */
.ramom-share-btn {
    border: 2px solid #28a745 !important;
    color: #28a745 !important;
}

.ramom-share-btn:hover {
    background: #28a745 !important;
    color: white !important;
}

/* User Post Button */
.ramom-user-post-btn {
    border: 2px solid #6f42c1 !important;
    color: #6f42c1 !important;
}

.ramom-user-post-btn:hover {
    background: #6f42c1 !important;
    color: white !important;
}

/* Modal Styles */
.ramom-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ramom-modal-content {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.ramom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.ramom-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #212529;
}

.ramom-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.ramom-modal-close:hover {
    background: #f8f9fa;
    color: #495057;
}

.ramom-modal-body {
    padding: 20px;
}

/* Form Styles */
.ramom-form-group {
    margin-bottom: 20px;
}

.ramom-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.ramom-form-group input,
.ramom-form-group textarea,
.ramom-form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.ramom-form-group input:focus,
.ramom-form-group textarea:focus,
.ramom-form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.ramom-form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.ramom-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.ramom-btn-primary {
    background: #007bff;
    color: white;
}

.ramom-btn-primary:hover {
    background: #0056b3;
}

.ramom-btn-secondary {
    background: #6c757d;
    color: white;
}

.ramom-btn-secondary:hover {
    background: #545b62;
}

/* Loading States */
.ramom-loading {
    opacity: 0.6;
    pointer-events: none;
}

.ramom-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: ramom-spin 1s linear infinite;
}

@keyframes ramom-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* User Stats */
.ramom-user-stats {
    display: flex;
    gap: 20px;
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.ramom-stat {
    text-align: center;
}

.ramom-stat-number {
    font-size: 18px;
    font-weight: 600;
    color: #212529;
}

.ramom-stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Notifications */
.ramom-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 10001;
    animation: ramom-slide-in 0.3s ease;
}

.ramom-notification.success {
    background: #28a745;
}

.ramom-notification.error {
    background: #dc3545;
}

.ramom-notification.info {
    background: #17a2b8;
}

@keyframes ramom-slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .ramom-interactions {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .ramom-interactions button {
        flex: 1;
        min-width: calc(50% - 5px);
        justify-content: center;
    }
    
    .ramom-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .ramom-modal-header,
    .ramom-modal-body {
        padding: 15px;
    }
    
    .ramom-form-actions {
        flex-direction: column;
    }
    
    .ramom-btn {
        width: 100%;
    }
    
    .ramom-user-stats {
        flex-direction: column;
        gap: 10px;
    }
}

/* Integration with InstaFeed Theme */
.instafeed-theme .ramom-interactions {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(219, 219, 219, 0.5);
}

.instafeed-theme .ramom-interactions button {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .ramom-interactions {
        background: #2c2c2c;
        border-color: #404040;
    }
    
    .ramom-interactions button {
        background: #404040;
        color: #e0e0e0;
    }
    
    .ramom-modal-content {
        background: #2c2c2c;
        color: #e0e0e0;
    }
    
    .ramom-form-group input,
    .ramom-form-group textarea,
    .ramom-form-group select {
        background: #404040;
        border-color: #555;
        color: #e0e0e0;
    }
    
    .ramom-user-stats {
        background: #2c2c2c;
    }
}
