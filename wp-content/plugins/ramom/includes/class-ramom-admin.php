<?php
/**
 * Ramom Admin Class
 * Handles admin interface and settings
 */

if (!defined('ABSPATH')) {
    exit;
}

class Ramom_Admin {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_dashboard_setup', array($this, 'add_dashboard_widgets'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            __('Ramom Dashboard', 'ramom'),
            __('Ramom', 'ramom'),
            'manage_options',
            'ramom-dashboard',
            array($this, 'dashboard_page'),
            'dashicons-heart',
            30
        );

        add_submenu_page(
            'ramom-dashboard',
            __('Dashboard', 'ramom'),
            __('Dashboard', 'ramom'),
            'manage_options',
            'ramom-dashboard',
            array($this, 'dashboard_page')
        );

        add_submenu_page(
            'ramom-dashboard',
            __('Post Generator', 'ramom'),
            __('Post Generator', 'ramom'),
            'manage_options',
            'ramom-generator',
            array($this, 'generator_page')
        );

        add_submenu_page(
            'ramom-dashboard',
            __('Content Manager', 'ramom'),
            __('Content Manager', 'ramom'),
            'manage_options',
            'ramom-content-manager',
            array($this, 'content_manager_page')
        );

        add_submenu_page(
            'ramom-dashboard',
            __('User Activity', 'ramom'),
            __('User Activity', 'ramom'),
            'manage_options',
            'ramom-activity',
            array($this, 'activity_page')
        );

        add_submenu_page(
            'ramom-dashboard',
            __('Statistics', 'ramom'),
            __('Statistics', 'ramom'),
            'manage_options',
            'ramom-stats',
            array($this, 'stats_page')
        );

        add_submenu_page(
            'ramom-dashboard',
            __('Settings', 'ramom'),
            __('Settings', 'ramom'),
            'manage_options',
            'ramom-settings',
            array($this, 'settings_page')
        );
    }
    
    public function init_settings() {
        register_setting('ramom_settings', 'ramom_auto_post_enabled');
        register_setting('ramom_settings', 'ramom_auto_post_interval');
        register_setting('ramom_settings', 'ramom_enable_likes');
        register_setting('ramom_settings', 'ramom_enable_follows');
        register_setting('ramom_settings', 'ramom_enable_user_posts');
        register_setting('ramom_settings', 'ramom_max_posts_per_user');
        register_setting('ramom_settings', 'ramom_moderation_enabled');
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'ramom') !== false) {
            wp_enqueue_script('ramom-admin', RAMOM_PLUGIN_URL . 'assets/js/ramom-admin.js', array('jquery'), RAMOM_VERSION, true);
            wp_enqueue_style('ramom-admin', RAMOM_PLUGIN_URL . 'assets/css/ramom-admin.css', array(), RAMOM_VERSION);

            // Enqueue media library scripts for image selection
            wp_enqueue_media();

            wp_localize_script('ramom-admin', 'ramom_admin_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('ramom_admin_nonce'),
                'strings' => array(
                    'select_image' => __('Select Image', 'ramom'),
                    'use_image' => __('Use This Image', 'ramom'),
                    'confirm_delete' => __('Are you sure you want to delete all generated posts? This action cannot be undone.', 'ramom'),
                    'deleting' => __('Deleting posts...', 'ramom'),
                    'generating' => __('Generating posts...', 'ramom'),
                )
            ));
        }
    }
    
    public function add_dashboard_widgets() {
        wp_add_dashboard_widget(
            'ramom_stats_widget',
            __('Ramom Statistics', 'ramom'),
            array($this, 'dashboard_stats_widget')
        );
    }

    public function dashboard_page() {
        global $wpdb;

        // Get quick stats
        $likes_table = $wpdb->prefix . 'ramom_likes';
        $follows_table = $wpdb->prefix . 'ramom_follows';
        $activity_table = $wpdb->prefix . 'ramom_user_activity';

        $total_likes = $wpdb->get_var("SELECT COUNT(*) FROM $likes_table");
        $total_follows = $wpdb->get_var("SELECT COUNT(*) FROM $follows_table");
        $total_activities = $wpdb->get_var("SELECT COUNT(*) FROM $activity_table");

        $generator = new Ramom_Post_Generator();
        $generated_posts = $generator->get_generated_posts_count();

        // Get recent activity
        $recent_activities = $wpdb->get_results(
            "SELECT a.*, u.display_name
             FROM $activity_table a
             LEFT JOIN {$wpdb->users} u ON a.user_id = u.ID
             ORDER BY a.created_at DESC
             LIMIT 10"
        );

        ?>
        <div class="wrap">
            <h1><?php _e('Ramom Dashboard', 'ramom'); ?></h1>

            <!-- Quick Stats -->
            <div class="ramom-stats-grid">
                <div class="ramom-stat-card">
                    <h3><?php _e('Total Likes', 'ramom'); ?></h3>
                    <div class="ramom-stat-number"><?php echo number_format($total_likes); ?></div>
                </div>

                <div class="ramom-stat-card">
                    <h3><?php _e('Total Follows', 'ramom'); ?></h3>
                    <div class="ramom-stat-number"><?php echo number_format($total_follows); ?></div>
                </div>

                <div class="ramom-stat-card">
                    <h3><?php _e('Generated Posts', 'ramom'); ?></h3>
                    <div class="ramom-stat-number"><?php echo number_format($generated_posts); ?></div>
                </div>

                <div class="ramom-stat-card">
                    <h3><?php _e('Total Activities', 'ramom'); ?></h3>
                    <div class="ramom-stat-number"><?php echo number_format($total_activities); ?></div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="ramom-quick-actions">
                <div class="ramom-quick-action" onclick="location.href='<?php echo admin_url('admin.php?page=ramom-generator'); ?>'">
                    <span class="ramom-quick-action-icon">🎲</span>
                    <span class="ramom-quick-action-label"><?php _e('Generate Posts', 'ramom'); ?></span>
                </div>

                <div class="ramom-quick-action" onclick="location.href='<?php echo admin_url('admin.php?page=ramom-content-manager'); ?>'">
                    <span class="ramom-quick-action-icon">📝</span>
                    <span class="ramom-quick-action-label"><?php _e('Manage Content', 'ramom'); ?></span>
                </div>

                <div class="ramom-quick-action" onclick="location.href='<?php echo admin_url('admin.php?page=ramom-activity'); ?>'">
                    <span class="ramom-quick-action-icon">📊</span>
                    <span class="ramom-quick-action-label"><?php _e('View Activity', 'ramom'); ?></span>
                </div>

                <div class="ramom-quick-action" onclick="location.href='<?php echo admin_url('admin.php?page=ramom-settings'); ?>'">
                    <span class="ramom-quick-action-icon">⚙️</span>
                    <span class="ramom-quick-action-label"><?php _e('Settings', 'ramom'); ?></span>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="ramom-admin-section">
                <h2><?php _e('Recent Activity', 'ramom'); ?></h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('User', 'ramom'); ?></th>
                            <th><?php _e('Activity', 'ramom'); ?></th>
                            <th><?php _e('Date', 'ramom'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($recent_activities)) : ?>
                            <?php foreach ($recent_activities as $activity) : ?>
                                <tr>
                                    <td><?php echo esc_html($activity->display_name ?: 'Unknown User'); ?></td>
                                    <td><?php echo esc_html(str_replace('_', ' ', ucfirst($activity->activity_type))); ?></td>
                                    <td><?php echo human_time_diff(strtotime($activity->created_at), current_time('timestamp')) . ' ago'; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else : ?>
                            <tr>
                                <td colspan="3"><?php _e('No recent activity', 'ramom'); ?></td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php
    }
    
    public function settings_page() {
        if (isset($_POST['submit'])) {
            // Handle form submission
            update_option('ramom_auto_post_enabled', isset($_POST['ramom_auto_post_enabled']) ? 1 : 0);
            update_option('ramom_auto_post_interval', sanitize_text_field($_POST['ramom_auto_post_interval']));
            update_option('ramom_enable_likes', isset($_POST['ramom_enable_likes']) ? 1 : 0);
            update_option('ramom_enable_follows', isset($_POST['ramom_enable_follows']) ? 1 : 0);
            update_option('ramom_enable_user_posts', isset($_POST['ramom_enable_user_posts']) ? 1 : 0);
            update_option('ramom_max_posts_per_user', intval($_POST['ramom_max_posts_per_user']));
            update_option('ramom_moderation_enabled', isset($_POST['ramom_moderation_enabled']) ? 1 : 0);
            
            echo '<div class="notice notice-success"><p>' . __('Settings saved!', 'ramom') . '</p></div>';
        }
        
        $auto_post_enabled = get_option('ramom_auto_post_enabled', 1);
        $auto_post_interval = get_option('ramom_auto_post_interval', 'hourly');
        $enable_likes = get_option('ramom_enable_likes', 1);
        $enable_follows = get_option('ramom_enable_follows', 1);
        $enable_user_posts = get_option('ramom_enable_user_posts', 1);
        $max_posts_per_user = get_option('ramom_max_posts_per_user', 10);
        $moderation_enabled = get_option('ramom_moderation_enabled', 0);
        
        ?>
        <div class="wrap">
            <h1><?php _e('Ramom Settings', 'ramom'); ?></h1>
            
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Auto Post Generation', 'ramom'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="ramom_auto_post_enabled" value="1" <?php checked($auto_post_enabled); ?> />
                                <?php _e('Enable automatic post generation', 'ramom'); ?>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Auto Post Interval', 'ramom'); ?></th>
                        <td>
                            <select name="ramom_auto_post_interval">
                                <option value="hourly" <?php selected($auto_post_interval, 'hourly'); ?>><?php _e('Hourly', 'ramom'); ?></option>
                                <option value="twicedaily" <?php selected($auto_post_interval, 'twicedaily'); ?>><?php _e('Twice Daily', 'ramom'); ?></option>
                                <option value="daily" <?php selected($auto_post_interval, 'daily'); ?>><?php _e('Daily', 'ramom'); ?></option>
                                <option value="weekly" <?php selected($auto_post_interval, 'weekly'); ?>><?php _e('Weekly', 'ramom'); ?></option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('User Interactions', 'ramom'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="ramom_enable_likes" value="1" <?php checked($enable_likes); ?> />
                                <?php _e('Enable likes', 'ramom'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="ramom_enable_follows" value="1" <?php checked($enable_follows); ?> />
                                <?php _e('Enable follows', 'ramom'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="ramom_enable_user_posts" value="1" <?php checked($enable_user_posts); ?> />
                                <?php _e('Enable user-generated posts', 'ramom'); ?>
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Max Posts Per User', 'ramom'); ?></th>
                        <td>
                            <input type="number" name="ramom_max_posts_per_user" value="<?php echo $max_posts_per_user; ?>" min="1" max="100" />
                            <p class="description"><?php _e('Maximum number of posts a user can create per day', 'ramom'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Content Moderation', 'ramom'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="ramom_moderation_enabled" value="1" <?php checked($moderation_enabled); ?> />
                                <?php _e('Enable content moderation (user posts require approval)', 'ramom'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }
    
    public function generator_page() {
        if (isset($_POST['generate_posts'])) {
            $count = intval($_POST['post_count']);
            $category = sanitize_text_field($_POST['post_category']);
            $use_media_library = isset($_POST['use_media_library']);
            $generator = new Ramom_Post_Generator();

            $created = 0;
            for ($i = 0; $i < $count; $i++) {
                if ($generator->create_random_post($category, $use_media_library)) {
                    $created++;
                }
            }

            echo '<div class="notice notice-success"><p>' . sprintf(__('Created %d random posts!', 'ramom'), $created) . '</p></div>';
        }

        $generator = new Ramom_Post_Generator();
        $generated_count = $generator->get_generated_posts_count();

        // Get media library images count
        $media_count = wp_count_attachments('image');
        $media_total = array_sum((array) $media_count);

        ?>
        <div class="wrap">
            <h1><?php _e('Post Generator', 'ramom'); ?></h1>

            <div class="ramom-admin-section">
                <h2><?php _e('Generate Random Posts', 'ramom'); ?></h2>
                <p><?php printf(__('Currently have %d generated posts.', 'ramom'), $generated_count); ?></p>
                <p><?php printf(__('Media Library has %d images available.', 'ramom'), $media_total); ?></p>

                <form method="post" id="ramom-generator-form">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Number of Posts', 'ramom'); ?></th>
                            <td>
                                <input type="number" name="post_count" value="10" min="1" max="50" />
                                <p class="description"><?php _e('Number of random posts to generate (1-50)', 'ramom'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Category', 'ramom'); ?></th>
                            <td>
                                <select name="post_category">
                                    <option value="random"><?php _e('Random Category', 'ramom'); ?></option>
                                    <option value="photography"><?php _e('Photography', 'ramom'); ?></option>
                                    <option value="lifestyle"><?php _e('Lifestyle', 'ramom'); ?></option>
                                    <option value="travel"><?php _e('Travel', 'ramom'); ?></option>
                                    <option value="food"><?php _e('Food', 'ramom'); ?></option>
                                    <option value="motivation"><?php _e('Motivation', 'ramom'); ?></option>
                                </select>
                                <p class="description"><?php _e('Choose content category for generated posts', 'ramom'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Image Source', 'ramom'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="use_media_library" value="1" <?php checked($media_total > 0); ?> <?php disabled($media_total, 0); ?> />
                                    <?php _e('Use images from WordPress Media Library', 'ramom'); ?>
                                    <?php if ($media_total == 0) : ?>
                                        <span style="color: #d63638;"><?php _e('(No images available)', 'ramom'); ?></span>
                                    <?php endif; ?>
                                </label>
                                <p class="description">
                                    <?php _e('When checked, posts will use random images from your media library. Otherwise, placeholder images will be used.', 'ramom'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>

                    <div class="ramom-button-group">
                        <input type="submit" name="generate_posts" class="button-primary" value="<?php _e('Generate Posts', 'ramom'); ?>" />
                        <button type="button" id="ramom-bulk-generate" class="button-secondary"><?php _e('Bulk Generate (Advanced)', 'ramom'); ?></button>
                    </div>
                </form>
            </div>

            <div class="ramom-admin-section">
                <h2><?php _e('Media Library Management', 'ramom'); ?></h2>
                <p><?php _e('Upload images to your WordPress Media Library to use them in generated posts.', 'ramom'); ?></p>
                <div class="ramom-button-group">
                    <a href="<?php echo admin_url('media-new.php'); ?>" class="button"><?php _e('Upload Images', 'ramom'); ?></a>
                    <a href="<?php echo admin_url('upload.php'); ?>" class="button"><?php _e('Manage Media Library', 'ramom'); ?></a>
                </div>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#ramom-bulk-generate').on('click', function() {
                var count = $('input[name="post_count"]').val();
                if (confirm('Generate ' + count + ' posts? This may take a few moments.')) {
                    $(this).prop('disabled', true).text('<?php _e('Generating...', 'ramom'); ?>');
                    $('#ramom-generator-form').submit();
                }
            });
        });
        </script>
        <?php
    }

    public function content_manager_page() {
        if (isset($_POST['delete_all_generated'])) {
            $generator = new Ramom_Post_Generator();
            $deleted = $generator->delete_generated_posts();
            echo '<div class="notice notice-success"><p>' . sprintf(__('Deleted %d generated posts!', 'ramom'), $deleted) . '</p></div>';
        }

        if (isset($_POST['delete_selected']) && !empty($_POST['selected_posts'])) {
            $selected_posts = array_map('intval', $_POST['selected_posts']);
            $deleted = 0;
            foreach ($selected_posts as $post_id) {
                if (wp_delete_post($post_id, true)) {
                    $deleted++;
                }
            }
            echo '<div class="notice notice-success"><p>' . sprintf(__('Deleted %d selected posts!', 'ramom'), $deleted) . '</p></div>';
        }

        // Get generated posts
        $generated_posts = get_posts(array(
            'meta_key' => '_ramom_generated',
            'meta_value' => 'yes',
            'numberposts' => 50,
            'post_status' => 'any'
        ));

        $generator = new Ramom_Post_Generator();
        $total_generated = $generator->get_generated_posts_count();

        ?>
        <div class="wrap">
            <h1><?php _e('Content Manager', 'ramom'); ?></h1>

            <div class="ramom-admin-section">
                <h2><?php _e('Generated Posts Management', 'ramom'); ?></h2>
                <p><?php printf(__('Total generated posts: %d', 'ramom'), $total_generated); ?></p>

                <div class="ramom-button-group">
                    <form method="post" style="display: inline-block;">
                        <input type="submit" name="delete_all_generated" class="button-secondary"
                               value="<?php _e('Delete All Generated Posts', 'ramom'); ?>"
                               onclick="return confirm('<?php _e('Are you sure you want to delete ALL generated posts? This action cannot be undone.', 'ramom'); ?>')" />
                    </form>
                    <a href="<?php echo admin_url('admin.php?page=ramom-generator'); ?>" class="button-primary">
                        <?php _e('Generate New Posts', 'ramom'); ?>
                    </a>
                </div>
            </div>

            <?php if (!empty($generated_posts)) : ?>
            <div class="ramom-admin-section">
                <h2><?php _e('Recent Generated Posts', 'ramom'); ?></h2>
                <form method="post" id="ramom-bulk-delete-form">
                    <div class="tablenav top">
                        <div class="alignleft actions">
                            <input type="submit" name="delete_selected" class="button-secondary"
                                   value="<?php _e('Delete Selected', 'ramom'); ?>"
                                   onclick="return confirm('<?php _e('Delete selected posts?', 'ramom'); ?>')" />
                        </div>
                    </div>

                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <td class="manage-column column-cb check-column">
                                    <input type="checkbox" id="cb-select-all" />
                                </td>
                                <th><?php _e('Title', 'ramom'); ?></th>
                                <th><?php _e('Category', 'ramom'); ?></th>
                                <th><?php _e('Date', 'ramom'); ?></th>
                                <th><?php _e('Status', 'ramom'); ?></th>
                                <th><?php _e('Actions', 'ramom'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($generated_posts as $post) : ?>
                                <?php
                                $category = get_post_meta($post->ID, '_ramom_category', true);
                                $post_status = get_post_status($post->ID);
                                ?>
                                <tr>
                                    <th scope="row" class="check-column">
                                        <input type="checkbox" name="selected_posts[]" value="<?php echo $post->ID; ?>" />
                                    </th>
                                    <td>
                                        <strong>
                                            <a href="<?php echo get_edit_post_link($post->ID); ?>">
                                                <?php echo esc_html($post->post_title); ?>
                                            </a>
                                        </strong>
                                    </td>
                                    <td><?php echo esc_html(ucfirst($category)); ?></td>
                                    <td><?php echo get_the_date('M j, Y g:i A', $post); ?></td>
                                    <td>
                                        <span class="ramom-status-indicator <?php echo $post_status; ?>"></span>
                                        <?php echo ucfirst($post_status); ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo get_permalink($post->ID); ?>" target="_blank" class="button-small">
                                            <?php _e('View', 'ramom'); ?>
                                        </a>
                                        <a href="<?php echo get_edit_post_link($post->ID); ?>" class="button-small">
                                            <?php _e('Edit', 'ramom'); ?>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </form>
            </div>
            <?php else : ?>
            <div class="ramom-admin-section">
                <h2><?php _e('No Generated Posts', 'ramom'); ?></h2>
                <p><?php _e('No posts have been generated yet.', 'ramom'); ?></p>
                <a href="<?php echo admin_url('admin.php?page=ramom-generator'); ?>" class="button-primary">
                    <?php _e('Generate Your First Posts', 'ramom'); ?>
                </a>
            </div>
            <?php endif; ?>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // Select all checkbox functionality
            $('#cb-select-all').on('change', function() {
                $('input[name="selected_posts[]"]').prop('checked', this.checked);
            });

            // Update select all when individual checkboxes change
            $('input[name="selected_posts[]"]').on('change', function() {
                var total = $('input[name="selected_posts[]"]').length;
                var checked = $('input[name="selected_posts[]"]:checked').length;
                $('#cb-select-all').prop('checked', total === checked);
            });
        });
        </script>
        <?php
    }
    
    public function activity_page() {
        global $wpdb;
        
        $activity_table = $wpdb->prefix . 'ramom_user_activity';
        $activities = $wpdb->get_results(
            "SELECT a.*, u.display_name 
             FROM $activity_table a 
             LEFT JOIN {$wpdb->users} u ON a.user_id = u.ID 
             ORDER BY a.created_at DESC 
             LIMIT 50"
        );
        
        ?>
        <div class="wrap">
            <h1><?php _e('User Activity', 'ramom'); ?></h1>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('User', 'ramom'); ?></th>
                        <th><?php _e('Activity', 'ramom'); ?></th>
                        <th><?php _e('Object', 'ramom'); ?></th>
                        <th><?php _e('Date', 'ramom'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($activities as $activity) : ?>
                        <tr>
                            <td><?php echo esc_html($activity->display_name ?: 'Unknown User'); ?></td>
                            <td><?php echo esc_html(str_replace('_', ' ', $activity->activity_type)); ?></td>
                            <td>
                                <?php if ($activity->object_type === 'post') : ?>
                                    <a href="<?php echo get_edit_post_link($activity->object_id); ?>">
                                        <?php echo get_the_title($activity->object_id) ?: 'Post #' . $activity->object_id; ?>
                                    </a>
                                <?php else : ?>
                                    <?php echo ucfirst($activity->object_type) . ' #' . $activity->object_id; ?>
                                <?php endif; ?>
                            </td>
                            <td><?php echo date('M j, Y g:i A', strtotime($activity->created_at)); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php
    }
    
    public function stats_page() {
        global $wpdb;
        
        // Get statistics
        $likes_table = $wpdb->prefix . 'ramom_likes';
        $follows_table = $wpdb->prefix . 'ramom_follows';
        $activity_table = $wpdb->prefix . 'ramom_user_activity';
        
        $total_likes = $wpdb->get_var("SELECT COUNT(*) FROM $likes_table");
        $total_follows = $wpdb->get_var("SELECT COUNT(*) FROM $follows_table");
        $total_activities = $wpdb->get_var("SELECT COUNT(*) FROM $activity_table");
        
        $generator = new Ramom_Post_Generator();
        $generated_posts = $generator->get_generated_posts_count();
        
        ?>
        <div class="wrap">
            <h1><?php _e('Ramom Statistics', 'ramom'); ?></h1>
            
            <div class="ramom-stats-grid">
                <div class="ramom-stat-card">
                    <h3><?php _e('Total Likes', 'ramom'); ?></h3>
                    <div class="ramom-stat-number"><?php echo number_format($total_likes); ?></div>
                </div>
                
                <div class="ramom-stat-card">
                    <h3><?php _e('Total Follows', 'ramom'); ?></h3>
                    <div class="ramom-stat-number"><?php echo number_format($total_follows); ?></div>
                </div>
                
                <div class="ramom-stat-card">
                    <h3><?php _e('Generated Posts', 'ramom'); ?></h3>
                    <div class="ramom-stat-number"><?php echo number_format($generated_posts); ?></div>
                </div>
                
                <div class="ramom-stat-card">
                    <h3><?php _e('Total Activities', 'ramom'); ?></h3>
                    <div class="ramom-stat-number"><?php echo number_format($total_activities); ?></div>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function dashboard_stats_widget() {
        global $wpdb;
        
        $likes_table = $wpdb->prefix . 'ramom_likes';
        $follows_table = $wpdb->prefix . 'ramom_follows';
        
        $today_likes = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $likes_table WHERE DATE(created_at) = %s",
            current_time('Y-m-d')
        ));
        
        $today_follows = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $follows_table WHERE DATE(created_at) = %s",
            current_time('Y-m-d')
        ));
        
        ?>
        <div class="ramom-dashboard-stats">
            <p><strong><?php _e('Today\'s Activity:', 'ramom'); ?></strong></p>
            <ul>
                <li><?php printf(__('Likes: %d', 'ramom'), $today_likes); ?></li>
                <li><?php printf(__('New Follows: %d', 'ramom'), $today_follows); ?></li>
            </ul>
            <p><a href="<?php echo admin_url('admin.php?page=ramom-stats'); ?>"><?php _e('View Full Statistics', 'ramom'); ?></a></p>
        </div>
        <?php
    }
}
