<?php
/**
 * Ramom Core Class
 * Handles core plugin functionality
 */

if (!defined('ABSPATH')) {
    exit;
}

class Ramom_Core {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_head', array($this, 'add_meta_tags'));
        add_filter('the_content', array($this, 'add_interaction_buttons'));
        add_action('wp_footer', array($this, 'add_user_modal'));
    }
    
    public function init() {
        // Register custom post types if needed
        $this->register_post_types();
        
        // Add custom user fields
        add_action('show_user_profile', array($this, 'add_user_profile_fields'));
        add_action('edit_user_profile', array($this, 'add_user_profile_fields'));
        add_action('personal_options_update', array($this, 'save_user_profile_fields'));
        add_action('edit_user_profile_update', array($this, 'save_user_profile_fields'));
    }
    
    public function register_post_types() {
        // Register user story post type
        register_post_type('ramom_story', array(
            'labels' => array(
                'name' => __('Stories', 'ramom'),
                'singular_name' => __('Story', 'ramom'),
                'add_new' => __('Add New Story', 'ramom'),
                'add_new_item' => __('Add New Story', 'ramom'),
                'edit_item' => __('Edit Story', 'ramom'),
                'new_item' => __('New Story', 'ramom'),
                'view_item' => __('View Story', 'ramom'),
                'search_items' => __('Search Stories', 'ramom'),
                'not_found' => __('No stories found', 'ramom'),
                'not_found_in_trash' => __('No stories found in trash', 'ramom'),
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'thumbnail', 'author', 'comments'),
            'menu_icon' => 'dashicons-format-status',
            'show_in_rest' => true,
        ));
    }
    
    public function add_meta_tags() {
        if (is_single()) {
            global $post;
            $likes_count = $this->get_post_likes_count($post->ID);
            echo '<meta name="ramom-post-id" content="' . $post->ID . '">';
            echo '<meta name="ramom-likes-count" content="' . $likes_count . '">';
        }
    }
    
    public function add_interaction_buttons($content) {
        if (is_single() && in_the_loop() && is_main_query()) {
            global $post;
            
            $likes_count = $this->get_post_likes_count($post->ID);
            $user_liked = $this->user_liked_post(get_current_user_id(), $post->ID);
            $author_id = $post->post_author;
            $current_user_id = get_current_user_id();
            $is_following = $this->is_user_following($current_user_id, $author_id);
            
            $buttons_html = '<div class="ramom-interactions">';
            
            // Like button
            $like_class = $user_liked ? 'ramom-liked' : '';
            $like_text = $user_liked ? __('Unlike', 'ramom') : __('Like', 'ramom');
            $buttons_html .= '<button class="ramom-like-btn ' . $like_class . '" data-post-id="' . $post->ID . '">';
            $buttons_html .= '<span class="ramom-like-icon">❤️</span>';
            $buttons_html .= '<span class="ramom-like-text">' . $like_text . '</span>';
            $buttons_html .= '<span class="ramom-like-count">(' . $likes_count . ')</span>';
            $buttons_html .= '</button>';
            
            // Follow button (only if not own post)
            if ($current_user_id && $current_user_id != $author_id) {
                $follow_class = $is_following ? 'ramom-following' : '';
                $follow_text = $is_following ? __('Unfollow', 'ramom') : __('Follow', 'ramom');
                $author_name = get_the_author_meta('display_name', $author_id);
                
                $buttons_html .= '<button class="ramom-follow-btn ' . $follow_class . '" data-user-id="' . $author_id . '">';
                $buttons_html .= '<span class="ramom-follow-text">' . $follow_text . ' ' . $author_name . '</span>';
                $buttons_html .= '</button>';
            }
            
            // Share button
            $buttons_html .= '<button class="ramom-share-btn" data-post-id="' . $post->ID . '">';
            $buttons_html .= '<span class="ramom-share-icon">📤</span>';
            $buttons_html .= '<span class="ramom-share-text">' . __('Share', 'ramom') . '</span>';
            $buttons_html .= '</button>';
            
            // User post button (for logged-in users)
            if ($current_user_id) {
                $buttons_html .= '<button class="ramom-user-post-btn" data-toggle="modal">';
                $buttons_html .= '<span class="ramom-post-icon">➕</span>';
                $buttons_html .= '<span class="ramom-post-text">' . __('Create Post', 'ramom') . '</span>';
                $buttons_html .= '</button>';
            }
            
            $buttons_html .= '</div>';
            
            $content .= $buttons_html;
        }
        
        return $content;
    }
    
    public function add_user_modal() {
        if (is_user_logged_in()) {
            ?>
            <div id="ramom-user-post-modal" class="ramom-modal" style="display: none;">
                <div class="ramom-modal-content">
                    <div class="ramom-modal-header">
                        <h3><?php _e('Create New Post', 'ramom'); ?></h3>
                        <button class="ramom-modal-close">&times;</button>
                    </div>
                    <div class="ramom-modal-body">
                        <form id="ramom-user-post-form">
                            <div class="ramom-form-group">
                                <label for="ramom-post-title"><?php _e('Title', 'ramom'); ?></label>
                                <input type="text" id="ramom-post-title" name="post_title" required>
                            </div>
                            <div class="ramom-form-group">
                                <label for="ramom-post-content"><?php _e('Content', 'ramom'); ?></label>
                                <textarea id="ramom-post-content" name="post_content" rows="5" required></textarea>
                            </div>
                            <div class="ramom-form-group">
                                <label for="ramom-post-image"><?php _e('Image URL (optional)', 'ramom'); ?></label>
                                <input type="url" id="ramom-post-image" name="post_image" placeholder="https://example.com/image.jpg">
                            </div>
                            <div class="ramom-form-group">
                                <label for="ramom-post-category"><?php _e('Category', 'ramom'); ?></label>
                                <select id="ramom-post-category" name="post_category">
                                    <option value="general"><?php _e('General', 'ramom'); ?></option>
                                    <option value="photography"><?php _e('Photography', 'ramom'); ?></option>
                                    <option value="lifestyle"><?php _e('Lifestyle', 'ramom'); ?></option>
                                    <option value="travel"><?php _e('Travel', 'ramom'); ?></option>
                                    <option value="food"><?php _e('Food', 'ramom'); ?></option>
                                    <option value="art"><?php _e('Art', 'ramom'); ?></option>
                                </select>
                            </div>
                            <div class="ramom-form-actions">
                                <button type="submit" class="ramom-btn ramom-btn-primary">
                                    <?php _e('Create Post', 'ramom'); ?>
                                </button>
                                <button type="button" class="ramom-btn ramom-btn-secondary ramom-modal-close">
                                    <?php _e('Cancel', 'ramom'); ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php
        }
    }
    
    public function add_user_profile_fields($user) {
        ?>
        <h3><?php _e('Ramom Social Settings', 'ramom'); ?></h3>
        <table class="form-table">
            <tr>
                <th><label for="ramom_bio"><?php _e('Bio', 'ramom'); ?></label></th>
                <td>
                    <textarea name="ramom_bio" id="ramom_bio" rows="3" cols="30"><?php echo esc_attr(get_user_meta($user->ID, 'ramom_bio', true)); ?></textarea>
                    <p class="description"><?php _e('Tell people about yourself', 'ramom'); ?></p>
                </td>
            </tr>
            <tr>
                <th><label for="ramom_website"><?php _e('Website', 'ramom'); ?></label></th>
                <td>
                    <input type="url" name="ramom_website" id="ramom_website" value="<?php echo esc_attr(get_user_meta($user->ID, 'ramom_website', true)); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th><label for="ramom_location"><?php _e('Location', 'ramom'); ?></label></th>
                <td>
                    <input type="text" name="ramom_location" id="ramom_location" value="<?php echo esc_attr(get_user_meta($user->ID, 'ramom_location', true)); ?>" class="regular-text" />
                </td>
            </tr>
        </table>
        <?php
    }
    
    public function save_user_profile_fields($user_id) {
        if (!current_user_can('edit_user', $user_id)) {
            return false;
        }
        
        update_user_meta($user_id, 'ramom_bio', sanitize_textarea_field($_POST['ramom_bio']));
        update_user_meta($user_id, 'ramom_website', esc_url_raw($_POST['ramom_website']));
        update_user_meta($user_id, 'ramom_location', sanitize_text_field($_POST['ramom_location']));
    }
    
    // Helper methods
    public function get_post_likes_count($post_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'ramom_likes';
        return $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table WHERE post_id = %d", $post_id));
    }
    
    public function user_liked_post($user_id, $post_id) {
        if (!$user_id) return false;
        
        global $wpdb;
        $table = $wpdb->prefix . 'ramom_likes';
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table WHERE user_id = %d AND post_id = %d",
            $user_id, $post_id
        ));
        return !empty($result);
    }
    
    public function is_user_following($follower_id, $following_id) {
        if (!$follower_id || $follower_id == $following_id) return false;
        
        global $wpdb;
        $table = $wpdb->prefix . 'ramom_follows';
        $result = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table WHERE follower_id = %d AND following_id = %d",
            $follower_id, $following_id
        ));
        return !empty($result);
    }
}
