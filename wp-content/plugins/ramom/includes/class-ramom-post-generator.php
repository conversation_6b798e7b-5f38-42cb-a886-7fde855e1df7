<?php
/**
 * Ramom Post Generator Class
 * Handles automatic random post generation
 */

if (!defined('ABSPATH')) {
    exit;
}

class Ramom_Post_Generator {
    
    private $post_templates;
    private $image_categories;
    
    public function __construct() {
        $this->init_templates();
        add_action('wp_ajax_ramom_generate_post', array($this, 'ajax_generate_post'));
        add_action('wp_ajax_nopriv_ramom_generate_post', array($this, 'ajax_generate_post'));
    }
    
    private function init_templates() {
        $this->post_templates = array(
            'photography' => array(
                'titles' => array(
                    'Captured this amazing moment',
                    'Perfect lighting today',
                    'Street photography vibes',
                    'Golden hour magic',
                    'Urban exploration',
                    'Nature\'s beauty',
                    'Candid moments',
                    'Architecture appreciation',
                    'Color palette inspiration',
                    'Minimalist composition'
                ),
                'content' => array(
                    'Sometimes the best shots happen when you least expect them. This moment caught my eye and I had to capture it.',
                    'The way light plays with shadows never ceases to amaze me. Photography is all about finding beauty in everyday moments.',
                    'Walking through the city with my camera, always looking for that perfect shot that tells a story.',
                    'There\'s something magical about the golden hour. The warm light transforms everything it touches.',
                    'Every corner of the city has a story to tell. Street photography helps me discover these hidden narratives.',
                    'Nature provides the most incredible compositions. No filter needed when you have natural beauty like this.',
                    'Candid photography captures the essence of real human emotions and authentic moments.',
                    'Architecture tells the story of human creativity and engineering excellence through time.',
                    'Colors have the power to evoke emotions and create powerful visual narratives.',
                    'Sometimes less is more. Minimalist photography focuses on the essential elements of a scene.'
                )
            ),
            'lifestyle' => array(
                'titles' => array(
                    'Living my best life',
                    'Simple pleasures',
                    'Weekend vibes',
                    'Self-care Sunday',
                    'Morning routine',
                    'Coffee shop moments',
                    'Home sweet home',
                    'Mindful living',
                    'Daily inspiration',
                    'Life updates'
                ),
                'content' => array(
                    'Taking time to appreciate the small things that make life beautiful. Gratitude changes everything.',
                    'Finding joy in simple moments and everyday experiences. Life is made up of these precious details.',
                    'Weekends are for recharging, exploring, and spending time with the people who matter most.',
                    'Self-care isn\'t selfish, it\'s essential. Taking time to nurture your mind, body, and soul.',
                    'Starting the day with intention and positive energy. Morning routines set the tone for everything.',
                    'There\'s something special about coffee shop atmosphere. Perfect for reflection and creativity.',
                    'Creating a space that feels like home, where you can truly be yourself and find peace.',
                    'Living mindfully means being present in each moment and appreciating what you have.',
                    'Finding inspiration in everyday moments and sharing positivity with the world.',
                    'Life is constantly evolving, and I\'m here for all the adventures and growth that come with it.'
                )
            ),
            'travel' => array(
                'titles' => array(
                    'Wanderlust calling',
                    'New destination discovered',
                    'Travel memories',
                    'Adventure awaits',
                    'Cultural exploration',
                    'Hidden gems',
                    'Local experiences',
                    'Journey continues',
                    'Travel inspiration',
                    'Exploring the unknown'
                ),
                'content' => array(
                    'Travel opens your mind to new perspectives and shows you how beautiful and diverse our world is.',
                    'Every destination has its own unique charm and stories waiting to be discovered.',
                    'Collecting memories from around the world, each place leaving its mark on my heart.',
                    'The best adventures happen when you step outside your comfort zone and embrace the unknown.',
                    'Immersing yourself in different cultures teaches you so much about humanity and connection.',
                    'Sometimes the most amazing places are the ones that aren\'t in any guidebook.',
                    'The best way to experience a place is through the eyes of locals who call it home.',
                    'Every journey teaches you something new about the world and about yourself.',
                    'Travel isn\'t just about the destinations, it\'s about the transformation that happens along the way.',
                    'There\'s a whole world out there waiting to be explored, and life is too short to stay in one place.'
                )
            ),
            'food' => array(
                'titles' => array(
                    'Foodie adventures',
                    'Homemade goodness',
                    'Culinary exploration',
                    'Fresh ingredients',
                    'Comfort food vibes',
                    'Healthy eating',
                    'Food art',
                    'Local flavors',
                    'Cooking experiments',
                    'Delicious discoveries'
                ),
                'content' => array(
                    'Food is love made visible. Every meal is an opportunity to nourish both body and soul.',
                    'There\'s nothing quite like the satisfaction of creating something delicious with your own hands.',
                    'Exploring different cuisines opens up a world of flavors and cultural experiences.',
                    'Fresh, quality ingredients are the foundation of any great dish. Taste the difference.',
                    'Sometimes you just need comfort food that reminds you of home and happier times.',
                    'Eating well is a form of self-respect. Nourishing your body with wholesome, nutritious food.',
                    'Food presentation is an art form. We eat with our eyes first, then our taste buds.',
                    'Every region has its own unique flavors and traditional dishes that tell cultural stories.',
                    'Cooking is creativity in action. Experimenting with new recipes and flavor combinations.',
                    'The joy of discovering a new favorite dish or restaurant that becomes part of your story.'
                )
            ),
            'motivation' => array(
                'titles' => array(
                    'Monday motivation',
                    'Keep pushing forward',
                    'Believe in yourself',
                    'Growth mindset',
                    'Positive vibes only',
                    'Dream big',
                    'Success journey',
                    'Overcome obstacles',
                    'Stay focused',
                    'Never give up'
                ),
                'content' => array(
                    'Every new week is a fresh start and a new opportunity to work towards your goals.',
                    'Progress isn\'t always linear, but every step forward counts. Keep moving in the right direction.',
                    'The most important relationship you\'ll ever have is the one with yourself. Believe in your potential.',
                    'Embrace challenges as opportunities to grow. Your mindset determines your success.',
                    'Surround yourself with positivity and watch how it transforms your perspective on life.',
                    'Your dreams are valid, no matter how big they seem. Take action and make them reality.',
                    'Success is a journey, not a destination. Enjoy the process and celebrate small wins.',
                    'Obstacles are not roadblocks, they\'re stepping stones to becoming stronger and wiser.',
                    'In a world full of distractions, staying focused on your goals is a superpower.',
                    'Persistence and determination are the keys to achieving anything you set your mind to.'
                )
            )
        );
        
        $this->image_categories = array(
            'photography' => array('camera', 'photo', 'art', 'creative'),
            'lifestyle' => array('lifestyle', 'home', 'relax', 'people'),
            'travel' => array('travel', 'landscape', 'city', 'adventure'),
            'food' => array('food', 'cooking', 'restaurant', 'fresh'),
            'motivation' => array('success', 'motivation', 'business', 'growth')
        );
    }
    
    public function create_random_post($category = null, $use_media_library = false) {
        $categories = array_keys($this->post_templates);

        if ($category && $category !== 'random' && isset($this->post_templates[$category])) {
            $selected_category = $category;
        } else {
            $selected_category = $categories[array_rand($categories)];
        }

        $titles = $this->post_templates[$selected_category]['titles'];
        $contents = $this->post_templates[$selected_category]['content'];

        $title = $titles[array_rand($titles)];
        $content = $contents[array_rand($contents)];

        // Add some randomization to make posts unique
        $title .= ' #' . rand(1, 999);

        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_excerpt' => substr($content, 0, 100) . '...',
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => $this->get_random_author(),
            'meta_input' => array(
                '_ramom_generated' => 'yes',
                '_ramom_category' => $selected_category,
                '_ramom_generated_date' => current_time('mysql'),
                '_ramom_use_media_library' => $use_media_library ? 'yes' : 'no'
            )
        );

        $post_id = wp_insert_post($post_data);

        if ($post_id && !is_wp_error($post_id)) {
            // Add featured image
            $this->add_random_featured_image($post_id, $selected_category, $use_media_library);

            // Add to category
            $this->assign_post_category($post_id, $selected_category);

            // Log activity
            $this->log_activity('post_created', $post_id, 'post');

            return $post_id;
        }

        return false;
    }
    
    public function ajax_generate_post() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check user permissions
        if (!current_user_can('publish_posts')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $category = sanitize_text_field($_POST['category'] ?? 'photography');
        $post_id = $this->create_random_post();
        
        if ($post_id) {
            wp_send_json_success(array(
                'post_id' => $post_id,
                'message' => __('Post created successfully!', 'ramom'),
                'post_url' => get_permalink($post_id)
            ));
        } else {
            wp_send_json_error('Failed to create post');
        }
    }
    
    private function get_random_author() {
        // Get random user with author capabilities
        $users = get_users(array(
            'capability' => 'publish_posts',
            'number' => 10
        ));
        
        if (!empty($users)) {
            $random_user = $users[array_rand($users)];
            return $random_user->ID;
        }
        
        // Fallback to admin user
        return 1;
    }
    
    private function add_random_featured_image($post_id, $category, $use_media_library = false) {
        if ($use_media_library) {
            // Try to get a random image from media library
            $media_image_id = $this->get_random_media_image();
            if ($media_image_id) {
                set_post_thumbnail($post_id, $media_image_id);
                return;
            }
        }

        // Fallback to placeholder image
        $image_keywords = $this->image_categories[$category] ?? array('random');
        $keyword = $image_keywords[array_rand($image_keywords)];

        // Generate random image URL
        $image_url = "https://picsum.photos/800/800?random=" . $post_id;

        // Store as meta for placeholder handling
        update_post_meta($post_id, '_placeholder_image_url', $image_url);
        update_post_meta($post_id, '_thumbnail_id', 'placeholder_' . $post_id);
    }

    private function get_random_media_image() {
        // Get random image attachments
        $images = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'numberposts' => 50,
            'orderby' => 'rand'
        ));

        if (!empty($images)) {
            $random_image = $images[array_rand($images)];
            return $random_image->ID;
        }

        return false;
    }
    
    private function assign_post_category($post_id, $category) {
        // Create category if it doesn't exist
        $term = get_term_by('slug', $category, 'category');
        if (!$term) {
            $term = wp_insert_term(ucfirst($category), 'category', array('slug' => $category));
            if (!is_wp_error($term)) {
                $term_id = $term['term_id'];
            }
        } else {
            $term_id = $term->term_id;
        }
        
        if (isset($term_id)) {
            wp_set_post_categories($post_id, array($term_id));
        }
    }
    
    private function log_activity($activity_type, $object_id, $object_type) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'ramom_user_activity';
        $wpdb->insert(
            $table,
            array(
                'user_id' => get_current_user_id() ?: 1,
                'activity_type' => $activity_type,
                'object_id' => $object_id,
                'object_type' => $object_type,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%s', '%d', '%s', '%s')
        );
    }
    
    public function get_generated_posts_count() {
        $posts = get_posts(array(
            'meta_key' => '_ramom_generated',
            'meta_value' => 'yes',
            'numberposts' => -1,
            'post_status' => 'any'
        ));
        
        return count($posts);
    }
    
    public function delete_generated_posts() {
        $posts = get_posts(array(
            'meta_key' => '_ramom_generated',
            'meta_value' => 'yes',
            'numberposts' => -1,
            'post_status' => 'any'
        ));
        
        $deleted = 0;
        foreach ($posts as $post) {
            if (wp_delete_post($post->ID, true)) {
                $deleted++;
            }
        }
        
        return $deleted;
    }
}
