<?php
/**
 * Ramom Post Generator Class
 * Handles automatic random post generation
 */

if (!defined('ABSPATH')) {
    exit;
}

class Ramom_Post_Generator {
    
    private $post_templates;
    private $image_categories;
    
    public function __construct() {
        $this->init_templates();
        add_action('wp_ajax_ramom_generate_post', array($this, 'ajax_generate_post'));
        add_action('wp_ajax_nopriv_ramom_generate_post', array($this, 'ajax_generate_post'));
    }
    
    private function init_templates() {
        $this->post_templates = array(
            'photography' => array(
                'titles' => array(
                    'Chasing light and capturing dreams ✨',
                    'When the universe aligns perfectly 📸',
                    'Found poetry in this moment',
                    'Light whispers stories only cameras can hear',
                    'Every frame tells a thousand stories',
                    'Caught between reality and magic',
                    'This is why I love photography',
                    'When art meets life spontaneously',
                    'Freezing time, one click at a time',
                    'The world through my lens today',
                    'Sometimes you don\'t take the photo, it takes you',
                    'Dancing with shadows and light',
                    'Pure visual poetry in motion',
                    'When ordinary becomes extraordinary',
                    'Capturing souls, not just faces'
                ),
                'content' => array(
                    'There\'s something deeply spiritual about photography - it\'s not just about capturing what you see, but revealing what others might miss. This moment spoke to my soul, and I knew I had to preserve it forever. 🌟',
                    'Photography taught me to see beauty in the mundane, magic in the ordinary, and stories in silence. Every click of the shutter is a heartbeat, every frame a breath of life captured in time. ✨',
                    'They say a picture is worth a thousand words, but I believe it\'s worth a thousand feelings. This image carries emotions I can\'t quite put into words - sometimes the heart sees what the eyes cannot. 💫',
                    'In a world that moves so fast, photography gives us permission to pause, to breathe, to really see. This moment reminded me why I fell in love with this art form in the first place. 📷',
                    'Light is the language of photography, and today it was speaking fluently. The way it danced across this scene was pure poetry - no words needed, just pure visual emotion. 🌅',
                    'Every photographer knows that feeling when everything aligns perfectly - the light, the moment, the emotion. This was one of those magical instances that reminds you why you picked up a camera. ✨',
                    'Photography is my meditation, my way of connecting with the world around me. Through my lens, I find stories waiting to be told, beauty waiting to be shared. 🎨',
                    'There\'s magic in the mundane if you know how to look for it. This ordinary moment became extraordinary through the simple act of paying attention. That\'s the power of photography. 🌟',
                    'Sometimes the best photographs aren\'t planned - they\'re felt. This image captured not just a scene, but a feeling, a moment of pure connection with the world around me. 💝',
                    'Photography is about more than technical perfection; it\'s about emotional connection. This frame holds a piece of my heart, a fragment of time that moved me deeply. 📸'
                )
            ),
            'lifestyle' => array(
                'titles' => array(
                    'Living my best life',
                    'Simple pleasures',
                    'Weekend vibes',
                    'Self-care Sunday',
                    'Morning routine',
                    'Coffee shop moments',
                    'Home sweet home',
                    'Mindful living',
                    'Daily inspiration',
                    'Life updates'
                ),
                'content' => array(
                    'Taking time to appreciate the small things that make life beautiful. Gratitude changes everything.',
                    'Finding joy in simple moments and everyday experiences. Life is made up of these precious details.',
                    'Weekends are for recharging, exploring, and spending time with the people who matter most.',
                    'Self-care isn\'t selfish, it\'s essential. Taking time to nurture your mind, body, and soul.',
                    'Starting the day with intention and positive energy. Morning routines set the tone for everything.',
                    'There\'s something special about coffee shop atmosphere. Perfect for reflection and creativity.',
                    'Creating a space that feels like home, where you can truly be yourself and find peace.',
                    'Living mindfully means being present in each moment and appreciating what you have.',
                    'Finding inspiration in everyday moments and sharing positivity with the world.',
                    'Life is constantly evolving, and I\'m here for all the adventures and growth that come with it.'
                )
            ),
            'travel' => array(
                'titles' => array(
                    'Wanderlust calling',
                    'New destination discovered',
                    'Travel memories',
                    'Adventure awaits',
                    'Cultural exploration',
                    'Hidden gems',
                    'Local experiences',
                    'Journey continues',
                    'Travel inspiration',
                    'Exploring the unknown'
                ),
                'content' => array(
                    'Travel opens your mind to new perspectives and shows you how beautiful and diverse our world is.',
                    'Every destination has its own unique charm and stories waiting to be discovered.',
                    'Collecting memories from around the world, each place leaving its mark on my heart.',
                    'The best adventures happen when you step outside your comfort zone and embrace the unknown.',
                    'Immersing yourself in different cultures teaches you so much about humanity and connection.',
                    'Sometimes the most amazing places are the ones that aren\'t in any guidebook.',
                    'The best way to experience a place is through the eyes of locals who call it home.',
                    'Every journey teaches you something new about the world and about yourself.',
                    'Travel isn\'t just about the destinations, it\'s about the transformation that happens along the way.',
                    'There\'s a whole world out there waiting to be explored, and life is too short to stay in one place.'
                )
            ),
            'food' => array(
                'titles' => array(
                    'Foodie adventures',
                    'Homemade goodness',
                    'Culinary exploration',
                    'Fresh ingredients',
                    'Comfort food vibes',
                    'Healthy eating',
                    'Food art',
                    'Local flavors',
                    'Cooking experiments',
                    'Delicious discoveries'
                ),
                'content' => array(
                    'Food is love made visible. Every meal is an opportunity to nourish both body and soul.',
                    'There\'s nothing quite like the satisfaction of creating something delicious with your own hands.',
                    'Exploring different cuisines opens up a world of flavors and cultural experiences.',
                    'Fresh, quality ingredients are the foundation of any great dish. Taste the difference.',
                    'Sometimes you just need comfort food that reminds you of home and happier times.',
                    'Eating well is a form of self-respect. Nourishing your body with wholesome, nutritious food.',
                    'Food presentation is an art form. We eat with our eyes first, then our taste buds.',
                    'Every region has its own unique flavors and traditional dishes that tell cultural stories.',
                    'Cooking is creativity in action. Experimenting with new recipes and flavor combinations.',
                    'The joy of discovering a new favorite dish or restaurant that becomes part of your story.'
                )
            ),
            'motivation' => array(
                'titles' => array(
                    'Your potential is limitless 🌟',
                    'Today is your canvas - paint it beautifully',
                    'Rise above the storm and find the sunshine ☀️',
                    'You are stronger than you think 💪',
                    'Dreams don\'t work unless you do ✨',
                    'Be the energy you want to attract',
                    'Your journey is uniquely yours 🦋',
                    'Embrace the magic within you',
                    'Turn your wounds into wisdom',
                    'You are exactly where you need to be',
                    'Let your light shine brighter today',
                    'Progress over perfection, always',
                    'Your story is still being written 📖',
                    'Believe in the power of your dreams',
                    'You are capable of amazing things'
                ),
                'content' => array(
                    'Some days you need to remind yourself that you are a masterpiece in progress. Every challenge you face is sculpting you into the person you\'re meant to become. Trust the process, embrace the journey, and remember that your potential knows no bounds. 🌟',
                    'Life isn\'t about waiting for the storm to pass - it\'s about learning to dance in the rain. Every setback is a setup for a comeback. You have survived 100% of your worst days, and that\'s a pretty amazing track record. Keep going. 💫',
                    'Your dreams are not too big; the world is just waiting for someone brave enough to pursue them. Every great achievement started with someone who believed it was possible. That someone could be you. Today could be the day everything changes. ✨',
                    'You are not just existing; you are creating, growing, and becoming. Every breath is an opportunity, every moment a chance to write a new chapter. Your story is unique, beautiful, and worth telling. Make it count. 📚',
                    'The magic you seek is already within you. Sometimes we look everywhere for answers when the greatest wisdom lies in trusting ourselves. You have everything you need to create the life you desire. Believe in your inner power. 🔮',
                    'Progress isn\'t always visible, but it\'s always happening. Like a tree growing stronger roots before reaching for the sky, you\'re building the foundation for something beautiful. Trust your growth, even when you can\'t see it. 🌱',
                    'You are not behind in life. You are not falling short. You are exactly where you need to be, learning exactly what you need to learn. Your timing is perfect, your path is yours, and your potential is infinite. 🦋',
                    'Every sunrise brings new possibilities, every sunset brings new wisdom. You have the power to make today different, to choose hope over fear, love over doubt. Your light can illuminate not just your path, but inspire others too. ☀️',
                    'Strength doesn\'t come from what you can do; it comes from overcoming what you thought you couldn\'t. You\'ve already proven you\'re stronger than you know. Keep surprising yourself with your resilience. 💪',
                    'Your journey is a work of art in progress. Every experience adds color, every challenge adds depth, every triumph adds light. Don\'t rush the masterpiece - embrace every brushstroke of your beautiful life. 🎨'
                )
            )
        );
        
        $this->image_categories = array(
            'photography' => array('camera', 'photo', 'art', 'creative'),
            'lifestyle' => array('lifestyle', 'home', 'relax', 'people'),
            'travel' => array('travel', 'landscape', 'city', 'adventure'),
            'food' => array('food', 'cooking', 'restaurant', 'fresh'),
            'motivation' => array('success', 'motivation', 'business', 'growth')
        );
    }
    
    public function create_random_post($category = null, $use_media_library = false) {
        $categories = array_keys($this->post_templates);

        if ($category && $category !== 'random' && isset($this->post_templates[$category])) {
            $selected_category = $category;
        } else {
            $selected_category = $categories[array_rand($categories)];
        }

        $titles = $this->post_templates[$selected_category]['titles'];
        $contents = $this->post_templates[$selected_category]['content'];

        $title = $titles[array_rand($titles)];
        $content = $contents[array_rand($contents)];

        // Add some randomization to make posts unique
        $title .= ' #' . rand(1, 999);

        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_excerpt' => substr($content, 0, 100) . '...',
            'post_status' => 'publish',
            'post_type' => 'post',
            'post_author' => $this->get_random_author(),
            'meta_input' => array(
                '_ramom_generated' => 'yes',
                '_ramom_category' => $selected_category,
                '_ramom_generated_date' => current_time('mysql'),
                '_ramom_use_media_library' => $use_media_library ? 'yes' : 'no'
            )
        );

        $post_id = wp_insert_post($post_data);

        if ($post_id && !is_wp_error($post_id)) {
            // Add featured image
            $this->add_random_featured_image($post_id, $selected_category, $use_media_library);

            // Add to category
            $this->assign_post_category($post_id, $selected_category);

            // Log activity
            $this->log_activity('post_created', $post_id, 'post');

            return $post_id;
        }

        return false;
    }
    
    public function ajax_generate_post() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'ramom_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check user permissions
        if (!current_user_can('publish_posts')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $category = sanitize_text_field($_POST['category'] ?? 'photography');
        $post_id = $this->create_random_post();
        
        if ($post_id) {
            wp_send_json_success(array(
                'post_id' => $post_id,
                'message' => __('Post created successfully!', 'ramom'),
                'post_url' => get_permalink($post_id)
            ));
        } else {
            wp_send_json_error('Failed to create post');
        }
    }
    
    private function get_random_author() {
        // Get random user with author capabilities
        $users = get_users(array(
            'capability' => 'publish_posts',
            'number' => 10
        ));
        
        if (!empty($users)) {
            $random_user = $users[array_rand($users)];
            return $random_user->ID;
        }
        
        // Fallback to admin user
        return 1;
    }
    
    private function add_random_featured_image($post_id, $category, $use_media_library = false) {
        if ($use_media_library) {
            // Try to get a random image from media library
            $media_image_id = $this->get_random_media_image();
            if ($media_image_id) {
                set_post_thumbnail($post_id, $media_image_id);
                return;
            }
        }

        // Fallback to placeholder image
        $image_keywords = $this->image_categories[$category] ?? array('random');
        $keyword = $image_keywords[array_rand($image_keywords)];

        // Generate random image URL
        $image_url = "https://picsum.photos/800/800?random=" . $post_id;

        // Store as meta for placeholder handling
        update_post_meta($post_id, '_placeholder_image_url', $image_url);
        update_post_meta($post_id, '_thumbnail_id', 'placeholder_' . $post_id);
    }

    private function get_random_media_image() {
        // Get random image attachments
        $images = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'numberposts' => 50,
            'orderby' => 'rand'
        ));

        if (!empty($images)) {
            $random_image = $images[array_rand($images)];
            return $random_image->ID;
        }

        return false;
    }
    
    private function assign_post_category($post_id, $category) {
        // Create category if it doesn't exist
        $term = get_term_by('slug', $category, 'category');
        if (!$term) {
            $term = wp_insert_term(ucfirst($category), 'category', array('slug' => $category));
            if (!is_wp_error($term)) {
                $term_id = $term['term_id'];
            }
        } else {
            $term_id = $term->term_id;
        }
        
        if (isset($term_id)) {
            wp_set_post_categories($post_id, array($term_id));
        }
    }
    
    private function log_activity($activity_type, $object_id, $object_type) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'ramom_user_activity';
        $wpdb->insert(
            $table,
            array(
                'user_id' => get_current_user_id() ?: 1,
                'activity_type' => $activity_type,
                'object_id' => $object_id,
                'object_type' => $object_type,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%s', '%d', '%s', '%s')
        );
    }
    
    public function get_generated_posts_count() {
        $posts = get_posts(array(
            'meta_key' => '_ramom_generated',
            'meta_value' => 'yes',
            'numberposts' => -1,
            'post_status' => 'any'
        ));
        
        return count($posts);
    }
    
    public function delete_generated_posts() {
        $posts = get_posts(array(
            'meta_key' => '_ramom_generated',
            'meta_value' => 'yes',
            'numberposts' => -1,
            'post_status' => 'any'
        ));
        
        $deleted = 0;
        foreach ($posts as $post) {
            if (wp_delete_post($post->ID, true)) {
                $deleted++;
            }
        }
        
        return $deleted;
    }
}
