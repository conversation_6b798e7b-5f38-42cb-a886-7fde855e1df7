<?php
/**
 * Plugin Name: InstaFeed Profile
 * Plugin URI: https://example.com/instafeed-profile
 * Description: Complete user profile management system for InstaFeed. Allows users to customize their profiles, manage personal information, upload avatars, and control privacy settings with Instagram-style interface.
 * Version: 1.0.0
 * Author: InstaFeed Development Team
 * License: GPL v2 or later
 * Text Domain: instafeed-profile
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('INSTAFEED_PROFILE_URL', plugin_dir_url(__FILE__));
define('INSTAFEED_PROFILE_PATH', plugin_dir_path(__FILE__));
define('INSTAFEED_PROFILE_VERSION', '1.0.0');

// Include required files
require_once INSTAFEED_PROFILE_PATH . 'includes/class-instafeed-profile-core.php';
require_once INSTAFEED_PROFILE_PATH . 'includes/class-instafeed-profile-ajax.php';
require_once INSTAFEED_PROFILE_PATH . 'includes/class-instafeed-profile-admin.php';

/**
 * Main InstaFeed Profile Plugin Class
 */
class InstaFeed_Profile_Plugin {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Initialize plugin components
        new InstaFeed_Profile_Core();
        new InstaFeed_Profile_Ajax();
        new InstaFeed_Profile_Admin();
        
        // Load text domain
        load_plugin_textdomain('instafeed-profile', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Add user meta fields
        $this->add_user_meta_fields();
        
        // Add custom user roles and capabilities
        $this->add_user_capabilities();
    }
    
    public function enqueue_scripts() {
        // Always enqueue on frontend for logged-in users
        if (is_user_logged_in() && !is_admin()) {
            // Enqueue CSS
            wp_enqueue_style(
                'instafeed-profile-style',
                INSTAFEED_PROFILE_URL . 'assets/css/instafeed-profile.css',
                array(),
                INSTAFEED_PROFILE_VERSION
            );
            
            // Enqueue JavaScript
            wp_enqueue_script(
                'instafeed-profile-script',
                INSTAFEED_PROFILE_URL . 'assets/js/instafeed-profile.js',
                array('jquery', 'wp-util'),
                INSTAFEED_PROFILE_VERSION,
                true
            );
            
            // Localize script
            wp_localize_script('instafeed-profile-script', 'instafeed_profile_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('instafeed_profile_nonce'),
                'user_id' => get_current_user_id(),
                'max_file_size' => wp_max_upload_size(),
                'allowed_types' => array('jpg', 'jpeg', 'png', 'gif'),
                'strings' => array(
                    'updating_profile' => __('Updating profile...', 'instafeed-profile'),
                    'uploading_avatar' => __('Uploading avatar...', 'instafeed-profile'),
                    'profile_updated' => __('Profile updated successfully!', 'instafeed-profile'),
                    'avatar_updated' => __('Avatar updated successfully!', 'instafeed-profile'),
                    'error_occurred' => __('An error occurred', 'instafeed-profile'),
                    'invalid_file_type' => __('Invalid file type', 'instafeed-profile'),
                    'file_too_large' => __('File is too large', 'instafeed-profile'),
                    'confirm_delete_avatar' => __('Are you sure you want to remove your avatar?', 'instafeed-profile'),
                    'avatar_removed' => __('Avatar removed successfully', 'instafeed-profile'),
                    'password_updated' => __('Password updated successfully', 'instafeed-profile'),
                    'passwords_dont_match' => __('Passwords do not match', 'instafeed-profile'),
                    'current_password_incorrect' => __('Current password is incorrect', 'instafeed-profile'),
                    'password_too_short' => __('Password must be at least 8 characters', 'instafeed-profile'),
                )
            ));
            
            // Enqueue media uploader
            wp_enqueue_media();
        }
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'instafeed-profile') !== false) {
            wp_enqueue_style('instafeed-profile-admin', INSTAFEED_PROFILE_URL . 'assets/css/admin.css', array(), INSTAFEED_PROFILE_VERSION);
            wp_enqueue_script('instafeed-profile-admin', INSTAFEED_PROFILE_URL . 'assets/js/admin.js', array('jquery'), INSTAFEED_PROFILE_VERSION, true);
        }
    }
    
    public function activate() {
        // Create custom tables
        $this->create_tables();
        
        // Set default options
        add_option('instafeed_profile_enabled', 1);
        add_option('instafeed_profile_allow_avatar_upload', 1);
        add_option('instafeed_profile_require_bio', 0);
        add_option('instafeed_profile_max_bio_length', 500);
        add_option('instafeed_profile_allow_social_links', 1);
        add_option('instafeed_profile_default_privacy', 'public');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    private function add_user_meta_fields() {
        // Add custom user meta fields for profile information
        add_action('show_user_profile', array($this, 'add_profile_fields'));
        add_action('edit_user_profile', array($this, 'add_profile_fields'));
        add_action('personal_options_update', array($this, 'save_profile_fields'));
        add_action('edit_user_profile_update', array($this, 'save_profile_fields'));
    }
    
    public function add_profile_fields($user) {
        ?>
        <h3><?php _e('InstaFeed Profile Information', 'instafeed-profile'); ?></h3>
        <table class="form-table">
            <tr>
                <th><label for="instafeed_bio"><?php _e('Bio', 'instafeed-profile'); ?></label></th>
                <td>
                    <textarea name="instafeed_bio" id="instafeed_bio" rows="5" cols="30"><?php echo esc_attr(get_user_meta($user->ID, 'instafeed_bio', true)); ?></textarea>
                    <p class="description"><?php _e('Tell us about yourself', 'instafeed-profile'); ?></p>
                </td>
            </tr>
            <tr>
                <th><label for="instafeed_location"><?php _e('Location', 'instafeed-profile'); ?></label></th>
                <td>
                    <input type="text" name="instafeed_location" id="instafeed_location" value="<?php echo esc_attr(get_user_meta($user->ID, 'instafeed_location', true)); ?>" class="regular-text" />
                </td>
            </tr>
            <tr>
                <th><label for="instafeed_website"><?php _e('Website', 'instafeed-profile'); ?></label></th>
                <td>
                    <input type="url" name="instafeed_website" id="instafeed_website" value="<?php echo esc_attr(get_user_meta($user->ID, 'instafeed_website', true)); ?>" class="regular-text" />
                </td>
            </tr>
        </table>
        <?php
    }
    
    public function save_profile_fields($user_id) {
        if (!current_user_can('edit_user', $user_id)) {
            return false;
        }
        
        update_user_meta($user_id, 'instafeed_bio', sanitize_textarea_field($_POST['instafeed_bio']));
        update_user_meta($user_id, 'instafeed_location', sanitize_text_field($_POST['instafeed_location']));
        update_user_meta($user_id, 'instafeed_website', esc_url_raw($_POST['instafeed_website']));
    }
    
    private function add_user_capabilities() {
        $role = get_role('subscriber');
        if ($role) {
            $role->add_cap('edit_instafeed_profile');
            $role->add_cap('upload_instafeed_avatar');
        }
        
        $role = get_role('author');
        if ($role) {
            $role->add_cap('edit_instafeed_profile');
            $role->add_cap('upload_instafeed_avatar');
        }
    }
    
    private function create_tables() {
        global $wpdb;
        
        // User profile settings table
        $table_name = $wpdb->prefix . 'instafeed_user_profiles';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            avatar_url varchar(255),
            cover_image_url varchar(255),
            bio text,
            location varchar(255),
            website varchar(255),
            instagram_url varchar(255),
            twitter_url varchar(255),
            facebook_url varchar(255),
            privacy_setting varchar(20) DEFAULT 'public',
            show_email tinyint(1) DEFAULT 0,
            show_posts_count tinyint(1) DEFAULT 1,
            show_followers_count tinyint(1) DEFAULT 1,
            show_following_count tinyint(1) DEFAULT 1,
            allow_messages tinyint(1) DEFAULT 1,
            email_notifications tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_id (user_id)
        ) $charset_collate;";
        
        // User followers table
        $followers_table = $wpdb->prefix . 'instafeed_user_followers';
        $sql2 = "CREATE TABLE $followers_table (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            follower_id bigint(20) NOT NULL,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_follower (user_id, follower_id),
            KEY user_id (user_id),
            KEY follower_id (follower_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        dbDelta($sql2);
    }
}

// Initialize the plugin
function instafeed_profile_init() {
    return InstaFeed_Profile_Plugin::get_instance();
}

// Start the plugin
instafeed_profile_init();
