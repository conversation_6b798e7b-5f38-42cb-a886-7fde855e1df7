<?php
/**
 * InstaFeed Profile AJAX Class
 * Handles all AJAX requests for profile management
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Profile_Ajax {
    
    public function __construct() {
        // Profile management
        add_action('wp_ajax_instafeed_update_profile', array($this, 'update_profile'));
        add_action('wp_ajax_instafeed_upload_avatar', array($this, 'upload_avatar'));
        add_action('wp_ajax_instafeed_remove_avatar', array($this, 'remove_avatar'));
        add_action('wp_ajax_instafeed_get_profile_posts', array($this, 'get_profile_posts'));
        add_action('wp_ajax_instafeed_get_profile_activity', array($this, 'get_profile_activity'));
        add_action('wp_ajax_instafeed_follow_user', array($this, 'follow_user'));
        add_action('wp_ajax_instafeed_unfollow_user', array($this, 'unfollow_user'));
        add_action('wp_ajax_instafeed_change_password', array($this, 'change_password'));
    }
    
    public function update_profile() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_profile_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to update your profile');
        }
        
        // Update WordPress user data
        $user_data = array(
            'ID' => $user_id,
            'display_name' => sanitize_text_field($_POST['display_name'])
        );
        
        $result = wp_update_user($user_data);
        if (is_wp_error($result)) {
            wp_send_json_error('Failed to update user data: ' . $result->get_error_message());
        }
        
        // Update profile data
        global $wpdb;
        $table_name = $wpdb->prefix . 'instafeed_user_profiles';
        
        $profile_data = array(
            'bio' => sanitize_textarea_field($_POST['bio']),
            'location' => sanitize_text_field($_POST['location']),
            'website' => esc_url_raw($_POST['website']),
            'instagram_url' => esc_url_raw($_POST['instagram_url']),
            'twitter_url' => esc_url_raw($_POST['twitter_url']),
            'facebook_url' => esc_url_raw($_POST['facebook_url']),
            'privacy_setting' => sanitize_text_field($_POST['privacy_setting']),
            'show_email' => isset($_POST['show_email']) ? 1 : 0,
            'allow_messages' => isset($_POST['allow_messages']) ? 1 : 0,
            'email_notifications' => isset($_POST['email_notifications']) ? 1 : 0,
            'updated_at' => current_time('mysql')
        );
        
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE user_id = %d",
            $user_id
        ));
        
        if ($existing) {
            $wpdb->update(
                $table_name,
                $profile_data,
                array('user_id' => $user_id),
                array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%d', '%d', '%s'),
                array('%d')
            );
        } else {
            $profile_data['user_id'] = $user_id;
            $wpdb->insert(
                $table_name,
                $profile_data,
                array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%d', '%d', '%s')
            );
        }
        
        // Log activity
        $this->log_user_activity($user_id, 'profile_updated');
        
        wp_send_json_success(array(
            'message' => __('Profile updated successfully!', 'instafeed-profile'),
            'profile_data' => $profile_data
        ));
    }
    
    public function upload_avatar() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_profile_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to upload avatar');
        }
        
        if (!current_user_can('upload_instafeed_avatar')) {
            wp_send_json_error('You do not have permission to upload avatars');
        }
        
        if (empty($_FILES['avatar']['name'])) {
            wp_send_json_error('No file uploaded');
        }
        
        // Handle file upload
        $attachment_id = $this->handle_avatar_upload($_FILES['avatar']);
        if (is_wp_error($attachment_id)) {
            wp_send_json_error($attachment_id->get_error_message());
        }
        
        // Update profile with new avatar
        global $wpdb;
        $table_name = $wpdb->prefix . 'instafeed_user_profiles';
        $avatar_url = wp_get_attachment_image_url($attachment_id, 'medium');
        
        $wpdb->query($wpdb->prepare(
            "INSERT INTO $table_name (user_id, avatar_url, updated_at) 
             VALUES (%d, %s, %s) 
             ON DUPLICATE KEY UPDATE avatar_url = %s, updated_at = %s",
            $user_id, $avatar_url, current_time('mysql'), $avatar_url, current_time('mysql')
        ));
        
        // Log activity
        $this->log_user_activity($user_id, 'avatar_updated');
        
        wp_send_json_success(array(
            'message' => __('Avatar updated successfully!', 'instafeed-profile'),
            'avatar_url' => $avatar_url
        ));
    }
    
    public function remove_avatar() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_profile_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to remove avatar');
        }
        
        // Remove avatar from profile
        global $wpdb;
        $table_name = $wpdb->prefix . 'instafeed_user_profiles';
        
        $wpdb->update(
            $table_name,
            array('avatar_url' => '', 'updated_at' => current_time('mysql')),
            array('user_id' => $user_id),
            array('%s', '%s'),
            array('%d')
        );
        
        // Log activity
        $this->log_user_activity($user_id, 'avatar_removed');
        
        wp_send_json_success(array(
            'message' => __('Avatar removed successfully!', 'instafeed-profile'),
            'default_avatar' => get_avatar_url($user_id, array('size' => 150, 'default' => 'mp'))
        ));
    }
    
    public function get_profile_posts() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_profile_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in');
        }
        
        $page = intval($_POST['page']) ?: 1;
        $per_page = 12;
        $offset = ($page - 1) * $per_page;
        
        $args = array(
            'author' => $user_id,
            'post_type' => array('post', 'instafeed_post'),
            'post_status' => 'publish',
            'posts_per_page' => $per_page,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        $posts = get_posts($args);
        $posts_data = array();
        
        foreach ($posts as $post) {
            $thumbnail_url = get_the_post_thumbnail_url($post->ID, 'medium');
            if (!$thumbnail_url) {
                $thumbnail_url = 'https://via.placeholder.com/300x300?text=No+Image';
            }
            
            $posts_data[] = array(
                'id' => $post->ID,
                'title' => $post->post_title,
                'thumbnail' => $thumbnail_url,
                'url' => get_permalink($post->ID),
                'date' => get_the_date('M j, Y', $post->ID),
                'likes' => get_post_meta($post->ID, 'instafeed_likes', true) ?: 0,
                'comments' => get_comments_number($post->ID)
            );
        }
        
        // Get total posts count
        $total_posts = $this->get_user_posts_count($user_id);
        
        wp_send_json_success(array(
            'posts' => $posts_data,
            'total' => $total_posts,
            'page' => $page,
            'has_more' => ($offset + $per_page) < $total_posts
        ));
    }
    
    public function get_profile_activity() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_profile_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in');
        }
        
        global $wpdb;
        $activity_table = $wpdb->prefix . 'instafeed_user_activity';
        
        $activities = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $activity_table 
             WHERE user_id = %d 
             ORDER BY created_at DESC 
             LIMIT 20",
            $user_id
        ));
        
        $activity_data = array();
        foreach ($activities as $activity) {
            $activity_data[] = array(
                'action' => $activity->action,
                'object_id' => $activity->object_id,
                'object_type' => $activity->object_type,
                'date' => human_time_diff(strtotime($activity->created_at), current_time('timestamp')) . ' ago',
                'description' => $this->get_activity_description($activity)
            );
        }
        
        wp_send_json_success(array(
            'activities' => $activity_data
        ));
    }
    
    public function follow_user() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_profile_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        $target_user_id = intval($_POST['target_user_id']);
        
        if (!$user_id) {
            wp_send_json_error('Please log in to follow users');
        }
        
        if ($user_id == $target_user_id) {
            wp_send_json_error('You cannot follow yourself');
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'instafeed_user_followers';
        
        // Check if already following
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE user_id = %d AND follower_id = %d",
            $target_user_id, $user_id
        ));
        
        if ($existing) {
            wp_send_json_error('Already following this user');
        }
        
        // Add follow relationship
        $result = $wpdb->insert(
            $table_name,
            array(
                'user_id' => $target_user_id,
                'follower_id' => $user_id,
                'status' => 'active',
                'created_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s')
        );
        
        if ($result === false) {
            wp_send_json_error('Failed to follow user');
        }
        
        // Log activity
        $this->log_user_activity($user_id, 'user_followed', $target_user_id);
        
        wp_send_json_success(array(
            'message' => __('User followed successfully!', 'instafeed-profile')
        ));
    }
    
    public function unfollow_user() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_profile_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        $target_user_id = intval($_POST['target_user_id']);
        
        if (!$user_id) {
            wp_send_json_error('Please log in to unfollow users');
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'instafeed_user_followers';
        
        $result = $wpdb->delete(
            $table_name,
            array(
                'user_id' => $target_user_id,
                'follower_id' => $user_id
            ),
            array('%d', '%d')
        );
        
        if ($result === false) {
            wp_send_json_error('Failed to unfollow user');
        }
        
        // Log activity
        $this->log_user_activity($user_id, 'user_unfollowed', $target_user_id);
        
        wp_send_json_success(array(
            'message' => __('User unfollowed successfully!', 'instafeed-profile')
        ));
    }
    
    public function change_password() {
        if (!wp_verify_nonce($_POST['nonce'], 'instafeed_profile_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_send_json_error('Please log in to change password');
        }
        
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        // Validate passwords
        if (strlen($new_password) < 8) {
            wp_send_json_error('Password must be at least 8 characters long');
        }
        
        if ($new_password !== $confirm_password) {
            wp_send_json_error('New passwords do not match');
        }
        
        // Verify current password
        $user = get_userdata($user_id);
        if (!wp_check_password($current_password, $user->user_pass, $user_id)) {
            wp_send_json_error('Current password is incorrect');
        }
        
        // Update password
        wp_set_password($new_password, $user_id);
        
        // Log activity
        $this->log_user_activity($user_id, 'password_changed');
        
        wp_send_json_success(array(
            'message' => __('Password updated successfully!', 'instafeed-profile')
        ));
    }
    
    private function handle_avatar_upload($file) {
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }
        
        $upload_overrides = array('test_form' => false);
        $movefile = wp_handle_upload($file, $upload_overrides);
        
        if ($movefile && !isset($movefile['error'])) {
            $attachment = array(
                'post_mime_type' => $movefile['type'],
                'post_title' => 'Profile Avatar',
                'post_content' => '',
                'post_status' => 'inherit'
            );
            
            $attachment_id = wp_insert_attachment($attachment, $movefile['file']);
            
            if (!is_wp_error($attachment_id)) {
                require_once(ABSPATH . 'wp-admin/includes/image.php');
                $attachment_data = wp_generate_attachment_metadata($attachment_id, $movefile['file']);
                wp_update_attachment_metadata($attachment_id, $attachment_data);
                return $attachment_id;
            }
        }
        
        return new WP_Error('upload_error', 'Failed to upload avatar');
    }
    
    private function get_user_posts_count($user_id) {
        $posts = get_posts(array(
            'author' => $user_id,
            'post_type' => array('post', 'instafeed_post'),
            'post_status' => 'publish',
            'numberposts' => -1,
            'fields' => 'ids'
        ));
        
        return count($posts);
    }
    
    private function get_activity_description($activity) {
        switch ($activity->action) {
            case 'profile_updated':
                return __('Updated profile information', 'instafeed-profile');
            case 'avatar_updated':
                return __('Changed profile photo', 'instafeed-profile');
            case 'avatar_removed':
                return __('Removed profile photo', 'instafeed-profile');
            case 'post_created':
                return __('Created a new post', 'instafeed-profile');
            case 'user_followed':
                return __('Followed a user', 'instafeed-profile');
            case 'user_unfollowed':
                return __('Unfollowed a user', 'instafeed-profile');
            case 'password_changed':
                return __('Changed account password', 'instafeed-profile');
            default:
                return __('Unknown activity', 'instafeed-profile');
        }
    }
    
    private function log_user_activity($user_id, $action, $object_id = 0) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'instafeed_user_activity';
        
        $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'action' => $action,
                'object_id' => $object_id,
                'object_type' => 'profile',
                'created_at' => current_time('mysql')
            ),
            array('%d', '%s', '%d', '%s', '%s')
        );
    }
}
