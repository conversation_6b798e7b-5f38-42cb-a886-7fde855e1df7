<?php
/**
 * InstaFeed Profile Core Class
 * Handles core profile functionality and user interactions
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Profile_Core {
    
    public function __construct() {
        add_action('wp_footer', array($this, 'add_profile_modal'));
        add_action('wp_footer', array($this, 'add_edit_profile_modal'));
        add_shortcode('instafeed_profile', array($this, 'profile_shortcode'));
        add_action('wp_head', array($this, 'add_profile_meta'));
        add_filter('get_avatar', array($this, 'custom_avatar'), 10, 5);
    }
    
    public function add_profile_modal() {
        if (!is_user_logged_in()) {
            return;
        }

        $current_user = wp_get_current_user();
        $profile_data = $this->get_user_profile_data($current_user->ID);
        $posts_count = $this->get_user_posts_count($current_user->ID);
        $followers_count = $this->get_followers_count($current_user->ID);
        $following_count = $this->get_following_count($current_user->ID);
        ?>
        <!-- InstaFeed Profile Modal -->
        <div id="instafeed-profile-modal" class="instafeed-modal" style="display: none;">
            <div class="instafeed-modal-overlay" onclick="closeInstaFeedModal('instafeed-profile-modal')"></div>
            <div class="instafeed-modal-content instafeed-modal-large">
                <div class="instafeed-modal-header">
                    <h2><?php _e('My Profile', 'instafeed-profile'); ?></h2>
                    <div class="instafeed-profile-actions">
                        <button class="instafeed-btn instafeed-btn-secondary" onclick="openInstaFeedModal('instafeed-edit-profile-modal')">
                            <?php _e('Edit Profile', 'instafeed-profile'); ?>
                        </button>
                        <button class="instafeed-modal-close" onclick="closeInstaFeedModal('instafeed-profile-modal')">&times;</button>
                    </div>
                </div>
                <div class="instafeed-modal-body">
                    <div class="instafeed-profile-container">
                        <!-- Profile Header -->
                        <div class="instafeed-profile-header">
                            <div class="instafeed-profile-avatar-section">
                                <div class="instafeed-profile-avatar">
                                    <img id="profile-avatar-display" src="<?php echo $this->get_user_avatar_url($current_user->ID); ?>" alt="<?php echo esc_attr($current_user->display_name); ?>">
                                </div>
                                <button class="instafeed-change-avatar-btn" onclick="changeProfileAvatar()">
                                    <?php _e('Change Photo', 'instafeed-profile'); ?>
                                </button>
                            </div>
                            
                            <div class="instafeed-profile-info">
                                <div class="instafeed-profile-username">
                                    <h3><?php echo esc_html($current_user->display_name); ?></h3>
                                    <span class="instafeed-username">@<?php echo esc_html($current_user->user_login); ?></span>
                                </div>
                                
                                <div class="instafeed-profile-stats">
                                    <div class="instafeed-stat" onclick="showProfileTab('posts')">
                                        <span class="instafeed-stat-number" id="posts-count"><?php echo $posts_count; ?></span>
                                        <span class="instafeed-stat-label"><?php _e('Posts', 'instafeed-profile'); ?></span>
                                    </div>
                                    <div class="instafeed-stat" onclick="showFollowersModal()">
                                        <span class="instafeed-stat-number" id="followers-count"><?php echo $followers_count; ?></span>
                                        <span class="instafeed-stat-label"><?php _e('Followers', 'instafeed-profile'); ?></span>
                                    </div>
                                    <div class="instafeed-stat" onclick="showFollowingModal()">
                                        <span class="instafeed-stat-number" id="following-count"><?php echo $following_count; ?></span>
                                        <span class="instafeed-stat-label"><?php _e('Following', 'instafeed-profile'); ?></span>
                                    </div>
                                </div>
                                
                                <div class="instafeed-profile-bio">
                                    <p id="profile-bio-display"><?php echo esc_html($profile_data['bio'] ?: __('No bio yet. Tell us about yourself!', 'instafeed-profile')); ?></p>
                                    <?php if (empty($profile_data['bio'])) : ?>
                                        <button class="instafeed-add-bio-btn" onclick="openInstaFeedModal('instafeed-edit-profile-modal')">
                                            <?php _e('Add Bio', 'instafeed-profile'); ?>
                                        </button>
                                    <?php endif; ?>
                                </div>

                                <!-- Profile Completion Indicator -->
                                <?php
                                $completion_percentage = $this->calculate_profile_completion($profile_data, $current_user);
                                if ($completion_percentage < 100) :
                                ?>
                                <div class="instafeed-profile-completion">
                                    <div class="instafeed-completion-header">
                                        <span class="instafeed-completion-text"><?php printf(__('Profile %d%% complete', 'instafeed-profile'), $completion_percentage); ?></span>
                                        <button class="instafeed-completion-btn" onclick="openInstaFeedModal('instafeed-edit-profile-modal')">
                                            <?php _e('Complete Profile', 'instafeed-profile'); ?>
                                        </button>
                                    </div>
                                    <div class="instafeed-completion-bar">
                                        <div class="instafeed-completion-progress" style="width: <?php echo $completion_percentage; ?>%"></div>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($profile_data['location']) || !empty($profile_data['website'])) : ?>
                                <div class="instafeed-profile-details">
                                    <?php if (!empty($profile_data['location'])) : ?>
                                        <div class="instafeed-profile-detail">
                                            <span class="instafeed-detail-icon">📍</span>
                                            <span id="profile-location-display"><?php echo esc_html($profile_data['location']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($profile_data['website'])) : ?>
                                        <div class="instafeed-profile-detail">
                                            <span class="instafeed-detail-icon">🔗</span>
                                            <a href="<?php echo esc_url($profile_data['website']); ?>" target="_blank" id="profile-website-display">
                                                <?php echo esc_html($profile_data['website']); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                                
                                <!-- Social Links -->
                                <div class="instafeed-social-links">
                                    <?php if (!empty($profile_data['instagram_url'])) : ?>
                                        <a href="<?php echo esc_url($profile_data['instagram_url']); ?>" target="_blank" class="instafeed-social-link instagram">📷</a>
                                    <?php endif; ?>
                                    <?php if (!empty($profile_data['twitter_url'])) : ?>
                                        <a href="<?php echo esc_url($profile_data['twitter_url']); ?>" target="_blank" class="instafeed-social-link twitter">🐦</a>
                                    <?php endif; ?>
                                    <?php if (!empty($profile_data['facebook_url'])) : ?>
                                        <a href="<?php echo esc_url($profile_data['facebook_url']); ?>" target="_blank" class="instafeed-social-link facebook">📘</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Profile Tabs -->
                        <div class="instafeed-profile-tabs">
                            <button class="instafeed-tab-btn active" onclick="showProfileTab('posts')"><?php _e('Posts', 'instafeed-profile'); ?></button>
                            <button class="instafeed-tab-btn" onclick="showProfileTab('saved')"><?php _e('Saved', 'instafeed-profile'); ?></button>
                            <button class="instafeed-tab-btn" onclick="showProfileTab('activity')"><?php _e('Activity', 'instafeed-profile'); ?></button>
                        </div>
                        
                        <!-- Profile Content -->
                        <div class="instafeed-profile-content">
                            <div id="profile-posts-tab" class="instafeed-tab-content active">
                                <div class="instafeed-profile-posts-grid" id="profile-posts-grid">
                                    <div class="instafeed-loading"><?php _e('Loading posts...', 'instafeed-profile'); ?></div>
                                </div>
                            </div>
                            
                            <div id="profile-saved-tab" class="instafeed-tab-content">
                                <div class="instafeed-profile-saved-grid" id="profile-saved-grid">
                                    <div class="instafeed-loading"><?php _e('Loading saved posts...', 'instafeed-profile'); ?></div>
                                </div>
                            </div>
                            
                            <div id="profile-activity-tab" class="instafeed-tab-content">
                                <div class="instafeed-profile-activity" id="profile-activity">
                                    <div class="instafeed-loading"><?php _e('Loading activity...', 'instafeed-profile'); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function add_edit_profile_modal() {
        if (!is_user_logged_in()) {
            return;
        }
        
        $current_user = wp_get_current_user();
        $profile_data = $this->get_user_profile_data($current_user->ID);
        ?>
        <!-- Edit Profile Modal -->
        <div id="instafeed-edit-profile-modal" class="instafeed-modal" style="display: none;">
            <div class="instafeed-modal-overlay" onclick="closeInstaFeedModal('instafeed-edit-profile-modal')"></div>
            <div class="instafeed-modal-content">
                <div class="instafeed-modal-header">
                    <h2><?php _e('Edit Profile', 'instafeed-profile'); ?></h2>
                    <button class="instafeed-modal-close" onclick="closeInstaFeedModal('instafeed-edit-profile-modal')">&times;</button>
                </div>
                <div class="instafeed-modal-body">
                    <form id="instafeed-edit-profile-form">
                        <!-- Avatar Upload Section -->
                        <div class="instafeed-form-group instafeed-avatar-upload-section">
                            <label><?php _e('Profile Photo', 'instafeed-profile'); ?></label>
                            <div class="instafeed-avatar-upload">
                                <div class="instafeed-current-avatar">
                                    <img id="edit-avatar-preview" src="<?php echo $this->get_user_avatar_url($current_user->ID); ?>" alt="Current Avatar">
                                </div>
                                <div class="instafeed-avatar-actions">
                                    <button type="button" class="instafeed-btn instafeed-btn-secondary" onclick="selectNewAvatar()">
                                        <?php _e('Change Photo', 'instafeed-profile'); ?>
                                    </button>
                                    <button type="button" class="instafeed-btn instafeed-btn-danger" onclick="removeAvatar()">
                                        <?php _e('Remove', 'instafeed-profile'); ?>
                                    </button>
                                </div>
                                <input type="file" id="avatar-upload" accept="image/*" style="display: none;">
                            </div>
                        </div>
                        
                        <!-- Basic Information -->
                        <div class="instafeed-form-group">
                            <label for="edit-display-name"><?php _e('Display Name', 'instafeed-profile'); ?></label>
                            <input type="text" id="edit-display-name" name="display_name" value="<?php echo esc_attr($current_user->display_name); ?>" required>
                        </div>
                        
                        <div class="instafeed-form-group">
                            <label for="edit-bio"><?php _e('Bio', 'instafeed-profile'); ?></label>
                            <textarea id="edit-bio" name="bio" rows="4" maxlength="500" placeholder="<?php _e('Tell us about yourself...', 'instafeed-profile'); ?>"><?php echo esc_textarea($profile_data['bio']); ?></textarea>
                            <small class="instafeed-help-text">
                                <span id="bio-char-count"><?php echo strlen($profile_data['bio']); ?></span>/500 <?php _e('characters', 'instafeed-profile'); ?>
                            </small>
                        </div>
                        
                        <div class="instafeed-form-group">
                            <label for="edit-location"><?php _e('Location', 'instafeed-profile'); ?></label>
                            <input type="text" id="edit-location" name="location" value="<?php echo esc_attr($profile_data['location']); ?>" placeholder="<?php _e('Where are you from?', 'instafeed-profile'); ?>">
                        </div>
                        
                        <div class="instafeed-form-group">
                            <label for="edit-website"><?php _e('Website', 'instafeed-profile'); ?></label>
                            <input type="url" id="edit-website" name="website" value="<?php echo esc_attr($profile_data['website']); ?>" placeholder="https://yourwebsite.com">
                        </div>
                        
                        <!-- Social Media Links -->
                        <div class="instafeed-form-section">
                            <h4><?php _e('Social Media Links', 'instafeed-profile'); ?></h4>
                            
                            <div class="instafeed-form-group">
                                <label for="edit-instagram"><?php _e('Instagram', 'instafeed-profile'); ?></label>
                                <input type="url" id="edit-instagram" name="instagram_url" value="<?php echo esc_attr($profile_data['instagram_url']); ?>" placeholder="https://instagram.com/username">
                            </div>
                            
                            <div class="instafeed-form-group">
                                <label for="edit-twitter"><?php _e('Twitter', 'instafeed-profile'); ?></label>
                                <input type="url" id="edit-twitter" name="twitter_url" value="<?php echo esc_attr($profile_data['twitter_url']); ?>" placeholder="https://twitter.com/username">
                            </div>
                            
                            <div class="instafeed-form-group">
                                <label for="edit-facebook"><?php _e('Facebook', 'instafeed-profile'); ?></label>
                                <input type="url" id="edit-facebook" name="facebook_url" value="<?php echo esc_attr($profile_data['facebook_url']); ?>" placeholder="https://facebook.com/username">
                            </div>
                        </div>
                        
                        <!-- Privacy Settings -->
                        <div class="instafeed-form-section">
                            <h4><?php _e('Privacy Settings', 'instafeed-profile'); ?></h4>
                            
                            <div class="instafeed-form-group">
                                <label for="privacy-setting"><?php _e('Profile Visibility', 'instafeed-profile'); ?></label>
                                <select id="privacy-setting" name="privacy_setting">
                                    <option value="public" <?php selected($profile_data['privacy_setting'], 'public'); ?>><?php _e('Public', 'instafeed-profile'); ?></option>
                                    <option value="private" <?php selected($profile_data['privacy_setting'], 'private'); ?>><?php _e('Private', 'instafeed-profile'); ?></option>
                                    <option value="friends" <?php selected($profile_data['privacy_setting'], 'friends'); ?>><?php _e('Friends Only', 'instafeed-profile'); ?></option>
                                </select>
                            </div>
                            
                            <div class="instafeed-form-group instafeed-checkbox-group">
                                <label>
                                    <input type="checkbox" name="show_email" value="1" <?php checked($profile_data['show_email'], 1); ?>>
                                    <?php _e('Show email address on profile', 'instafeed-profile'); ?>
                                </label>
                            </div>
                            
                            <div class="instafeed-form-group instafeed-checkbox-group">
                                <label>
                                    <input type="checkbox" name="allow_messages" value="1" <?php checked($profile_data['allow_messages'], 1); ?>>
                                    <?php _e('Allow direct messages', 'instafeed-profile'); ?>
                                </label>
                            </div>
                            
                            <div class="instafeed-form-group instafeed-checkbox-group">
                                <label>
                                    <input type="checkbox" name="email_notifications" value="1" <?php checked($profile_data['email_notifications'], 1); ?>>
                                    <?php _e('Receive email notifications', 'instafeed-profile'); ?>
                                </label>
                            </div>
                        </div>
                        
                        <div class="instafeed-form-actions">
                            <button type="button" class="instafeed-btn instafeed-btn-secondary" onclick="closeInstaFeedModal('instafeed-edit-profile-modal')">
                                <?php _e('Cancel', 'instafeed-profile'); ?>
                            </button>
                            <button type="submit" class="instafeed-btn instafeed-btn-primary">
                                <?php _e('Save Changes', 'instafeed-profile'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function profile_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<p>' . __('Please log in to view your profile.', 'instafeed-profile') . '</p>';
        }
        
        return '<button class="instafeed-btn instafeed-btn-primary" onclick="openInstaFeedModal(\'instafeed-profile-modal\')">' . 
               __('View Profile', 'instafeed-profile') . '</button>';
    }
    
    public function add_profile_meta() {
        if (is_user_logged_in()) {
            $current_user = wp_get_current_user();
            echo '<meta name="instafeed-user-id" content="' . $current_user->ID . '">';
            echo '<meta name="instafeed-user-name" content="' . esc_attr($current_user->display_name) . '">';
        }
    }
    
    public function custom_avatar($avatar, $id_or_email, $size, $default, $alt) {
        $user_id = 0;
        
        if (is_numeric($id_or_email)) {
            $user_id = (int) $id_or_email;
        } elseif (is_object($id_or_email)) {
            if (!empty($id_or_email->user_id)) {
                $user_id = (int) $id_or_email->user_id;
            }
        } else {
            $user = get_user_by('email', $id_or_email);
            if ($user) {
                $user_id = $user->ID;
            }
        }
        
        if ($user_id) {
            $custom_avatar = $this->get_user_avatar_url($user_id);
            if ($custom_avatar) {
                $avatar = '<img alt="' . esc_attr($alt) . '" src="' . esc_url($custom_avatar) . '" class="avatar avatar-' . $size . ' photo" height="' . $size . '" width="' . $size . '" />';
            }
        }
        
        return $avatar;
    }
    
    public function get_user_profile_data($user_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'instafeed_user_profiles';
        $profile = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE user_id = %d",
            $user_id
        ), ARRAY_A);
        
        if (!$profile) {
            // Create default profile
            $default_profile = array(
                'user_id' => $user_id,
                'bio' => '',
                'location' => '',
                'website' => '',
                'instagram_url' => '',
                'twitter_url' => '',
                'facebook_url' => '',
                'privacy_setting' => 'public',
                'show_email' => 0,
                'show_posts_count' => 1,
                'show_followers_count' => 1,
                'show_following_count' => 1,
                'allow_messages' => 1,
                'email_notifications' => 1
            );
            
            $wpdb->insert($table_name, $default_profile);
            return $default_profile;
        }
        
        return $profile;
    }
    
    public function get_user_avatar_url($user_id) {
        $profile_data = $this->get_user_profile_data($user_id);
        
        if (!empty($profile_data['avatar_url'])) {
            return $profile_data['avatar_url'];
        }
        
        // Fallback to Gravatar
        $user = get_userdata($user_id);
        if ($user) {
            return get_avatar_url($user->user_email, array('size' => 150, 'default' => 'mp'));
        }
        
        return 'https://www.gravatar.com/avatar/?d=mp&s=150';
    }
    
    public function get_user_posts_count($user_id) {
        $posts = get_posts(array(
            'author' => $user_id,
            'post_type' => array('post', 'instafeed_post'),
            'post_status' => 'publish',
            'numberposts' => -1,
            'fields' => 'ids'
        ));
        
        return count($posts);
    }
    
    public function get_followers_count($user_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'instafeed_user_followers';
        return $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE user_id = %d AND status = 'active'",
            $user_id
        ));
    }
    
    public function get_following_count($user_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'instafeed_user_followers';
        return $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE follower_id = %d AND status = 'active'",
            $user_id
        ));
    }

    public function calculate_profile_completion($profile_data, $user) {
        $completion_items = array(
            'avatar' => !empty($profile_data['avatar_url']) ? 20 : 0,
            'bio' => !empty($profile_data['bio']) ? 25 : 0,
            'location' => !empty($profile_data['location']) ? 15 : 0,
            'website' => !empty($profile_data['website']) ? 15 : 0,
            'social_links' => (!empty($profile_data['instagram_url']) || !empty($profile_data['twitter_url']) || !empty($profile_data['facebook_url'])) ? 15 : 0,
            'display_name' => (!empty($user->display_name) && $user->display_name !== $user->user_login) ? 10 : 0
        );

        return array_sum($completion_items);
    }

    public function get_profile_completion_suggestions($profile_data, $user) {
        $suggestions = array();

        if (empty($profile_data['avatar_url'])) {
            $suggestions[] = array(
                'text' => __('Add a profile photo', 'instafeed-profile'),
                'action' => 'upload_avatar',
                'points' => 20
            );
        }

        if (empty($profile_data['bio'])) {
            $suggestions[] = array(
                'text' => __('Write a bio', 'instafeed-profile'),
                'action' => 'add_bio',
                'points' => 25
            );
        }

        if (empty($profile_data['location'])) {
            $suggestions[] = array(
                'text' => __('Add your location', 'instafeed-profile'),
                'action' => 'add_location',
                'points' => 15
            );
        }

        if (empty($profile_data['website'])) {
            $suggestions[] = array(
                'text' => __('Add your website', 'instafeed-profile'),
                'action' => 'add_website',
                'points' => 15
            );
        }

        if (empty($profile_data['instagram_url']) && empty($profile_data['twitter_url']) && empty($profile_data['facebook_url'])) {
            $suggestions[] = array(
                'text' => __('Connect social media', 'instafeed-profile'),
                'action' => 'add_social',
                'points' => 15
            );
        }

        if (empty($user->display_name) || $user->display_name === $user->user_login) {
            $suggestions[] = array(
                'text' => __('Set a display name', 'instafeed-profile'),
                'action' => 'add_display_name',
                'points' => 10
            );
        }

        return $suggestions;
    }
}
