<?php
/**
 * InstaFeed Profile Admin Class
 * Handles admin interface and settings for profile management
 */

if (!defined('ABSPATH')) {
    exit;
}

class InstaFeed_Profile_Admin {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
        add_action('admin_notices', array($this, 'admin_notices'));
    }
    
    public function add_admin_menu() {
        add_options_page(
            __('InstaFeed Profile Settings', 'instafeed-profile'),
            __('InstaFeed Profile', 'instafeed-profile'),
            'manage_options',
            'instafeed-profile-settings',
            array($this, 'settings_page')
        );
        
        add_users_page(
            __('InstaFeed User Profiles', 'instafeed-profile'),
            __('InstaFeed Profiles', 'instafeed-profile'),
            'manage_options',
            'instafeed-user-profiles',
            array($this, 'user_profiles_page')
        );
    }
    
    public function init_settings() {
        register_setting('instafeed_profile_settings', 'instafeed_profile_enabled');
        register_setting('instafeed_profile_settings', 'instafeed_profile_allow_avatar_upload');
        register_setting('instafeed_profile_settings', 'instafeed_profile_require_bio');
        register_setting('instafeed_profile_settings', 'instafeed_profile_max_bio_length');
        register_setting('instafeed_profile_settings', 'instafeed_profile_allow_social_links');
        register_setting('instafeed_profile_settings', 'instafeed_profile_default_privacy');
        register_setting('instafeed_profile_settings', 'instafeed_profile_enable_following');
        register_setting('instafeed_profile_settings', 'instafeed_profile_moderate_avatars');
        register_setting('instafeed_profile_settings', 'instafeed_profile_show_completion');
        register_setting('instafeed_profile_settings', 'instafeed_profile_require_avatar');
        register_setting('instafeed_profile_settings', 'instafeed_profile_enable_achievements');
        register_setting('instafeed_profile_settings', 'instafeed_profile_public_profiles');
        register_setting('instafeed_profile_settings', 'instafeed_profile_profile_views');
        register_setting('instafeed_profile_settings', 'instafeed_profile_custom_fields');
    }
    
    public function admin_notices() {
        if (isset($_GET['page']) && $_GET['page'] === 'instafeed-profile-settings') {
            if (isset($_GET['settings-updated']) && $_GET['settings-updated']) {
                echo '<div class="notice notice-success is-dismissible"><p>' . 
                     __('InstaFeed Profile settings saved!', 'instafeed-profile') . '</p></div>';
            }
        }
    }
    
    public function settings_page() {
        if (isset($_POST['submit'])) {
            update_option('instafeed_profile_enabled', isset($_POST['instafeed_profile_enabled']) ? 1 : 0);
            update_option('instafeed_profile_allow_avatar_upload', isset($_POST['instafeed_profile_allow_avatar_upload']) ? 1 : 0);
            update_option('instafeed_profile_require_bio', isset($_POST['instafeed_profile_require_bio']) ? 1 : 0);
            update_option('instafeed_profile_max_bio_length', intval($_POST['instafeed_profile_max_bio_length']));
            update_option('instafeed_profile_allow_social_links', isset($_POST['instafeed_profile_allow_social_links']) ? 1 : 0);
            update_option('instafeed_profile_default_privacy', sanitize_text_field($_POST['instafeed_profile_default_privacy']));
            update_option('instafeed_profile_enable_following', isset($_POST['instafeed_profile_enable_following']) ? 1 : 0);
            update_option('instafeed_profile_moderate_avatars', isset($_POST['instafeed_profile_moderate_avatars']) ? 1 : 0);
            update_option('instafeed_profile_show_completion', isset($_POST['instafeed_profile_show_completion']) ? 1 : 0);
            update_option('instafeed_profile_require_avatar', isset($_POST['instafeed_profile_require_avatar']) ? 1 : 0);
            update_option('instafeed_profile_enable_achievements', isset($_POST['instafeed_profile_enable_achievements']) ? 1 : 0);
            update_option('instafeed_profile_public_profiles', isset($_POST['instafeed_profile_public_profiles']) ? 1 : 0);
            update_option('instafeed_profile_profile_views', isset($_POST['instafeed_profile_profile_views']) ? 1 : 0);
            update_option('instafeed_profile_custom_fields', sanitize_textarea_field($_POST['instafeed_profile_custom_fields']));
            
            echo '<div class="notice notice-success"><p>' . __('Settings saved!', 'instafeed-profile') . '</p></div>';
        }
        
        $enabled = get_option('instafeed_profile_enabled', 1);
        $allow_avatar_upload = get_option('instafeed_profile_allow_avatar_upload', 1);
        $require_bio = get_option('instafeed_profile_require_bio', 0);
        $max_bio_length = get_option('instafeed_profile_max_bio_length', 500);
        $allow_social_links = get_option('instafeed_profile_allow_social_links', 1);
        $default_privacy = get_option('instafeed_profile_default_privacy', 'public');
        $enable_following = get_option('instafeed_profile_enable_following', 1);
        $moderate_avatars = get_option('instafeed_profile_moderate_avatars', 0);
        $show_completion = get_option('instafeed_profile_show_completion', 1);
        $require_avatar = get_option('instafeed_profile_require_avatar', 0);
        $enable_achievements = get_option('instafeed_profile_enable_achievements', 1);
        $public_profiles = get_option('instafeed_profile_public_profiles', 1);
        $profile_views = get_option('instafeed_profile_profile_views', 1);
        $custom_fields = get_option('instafeed_profile_custom_fields', '');
        
        ?>
        <div class="wrap">
            <h1><?php _e('InstaFeed Profile Settings', 'instafeed-profile'); ?></h1>
            
            <div class="card">
                <h2><?php _e('Profile Management System', 'instafeed-profile'); ?></h2>
                <p><?php _e('Configure the Instagram-style profile system for your users.', 'instafeed-profile'); ?></p>
                
                <form method="post" action="">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Profile System', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_enabled" value="1" <?php checked($enabled); ?> />
                                    <?php _e('Enable InstaFeed profile management', 'instafeed-profile'); ?>
                                </label>
                                <p class="description"><?php _e('Allow users to create and manage their profiles', 'instafeed-profile'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Avatar Upload', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_allow_avatar_upload" value="1" <?php checked($allow_avatar_upload); ?> />
                                    <?php _e('Allow users to upload custom avatars', 'instafeed-profile'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Moderate Avatars', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_moderate_avatars" value="1" <?php checked($moderate_avatars); ?> />
                                    <?php _e('Require admin approval for avatar uploads', 'instafeed-profile'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Bio Requirements', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_require_bio" value="1" <?php checked($require_bio); ?> />
                                    <?php _e('Require users to have a bio', 'instafeed-profile'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Maximum Bio Length', 'instafeed-profile'); ?></th>
                            <td>
                                <input type="number" name="instafeed_profile_max_bio_length" value="<?php echo $max_bio_length; ?>" min="50" max="1000" />
                                <p class="description"><?php _e('Maximum number of characters allowed in user bios', 'instafeed-profile'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Social Media Links', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_allow_social_links" value="1" <?php checked($allow_social_links); ?> />
                                    <?php _e('Allow users to add social media links', 'instafeed-profile'); ?>
                                </label>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Default Privacy Setting', 'instafeed-profile'); ?></th>
                            <td>
                                <select name="instafeed_profile_default_privacy">
                                    <option value="public" <?php selected($default_privacy, 'public'); ?>><?php _e('Public', 'instafeed-profile'); ?></option>
                                    <option value="private" <?php selected($default_privacy, 'private'); ?>><?php _e('Private', 'instafeed-profile'); ?></option>
                                    <option value="friends" <?php selected($default_privacy, 'friends'); ?>><?php _e('Friends Only', 'instafeed-profile'); ?></option>
                                </select>
                                <p class="description"><?php _e('Default privacy setting for new user profiles', 'instafeed-profile'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row"><?php _e('Following System', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_enable_following" value="1" <?php checked($enable_following); ?> />
                                    <?php _e('Enable user following/followers system', 'instafeed-profile'); ?>
                                </label>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Profile Completion', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_show_completion" value="1" <?php checked($show_completion); ?> />
                                    <?php _e('Show profile completion indicator', 'instafeed-profile'); ?>
                                </label>
                                <p class="description"><?php _e('Encourage users to complete their profiles', 'instafeed-profile'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Avatar Requirements', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_require_avatar" value="1" <?php checked($require_avatar); ?> />
                                    <?php _e('Require users to upload an avatar', 'instafeed-profile'); ?>
                                </label>
                                <p class="description"><?php _e('Users must upload a profile photo to complete registration', 'instafeed-profile'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Public Profiles', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_public_profiles" value="1" <?php checked($public_profiles); ?> />
                                    <?php _e('Allow public profile viewing', 'instafeed-profile'); ?>
                                </label>
                                <p class="description"><?php _e('Non-logged-in users can view public profiles', 'instafeed-profile'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Profile Views', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_profile_views" value="1" <?php checked($profile_views); ?> />
                                    <?php _e('Track profile views', 'instafeed-profile'); ?>
                                </label>
                                <p class="description"><?php _e('Show users who viewed their profile', 'instafeed-profile'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Achievements', 'instafeed-profile'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="instafeed_profile_enable_achievements" value="1" <?php checked($enable_achievements); ?> />
                                    <?php _e('Enable profile achievements and badges', 'instafeed-profile'); ?>
                                </label>
                                <p class="description"><?php _e('Users earn badges for profile completion, posts, etc.', 'instafeed-profile'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Custom Profile Fields', 'instafeed-profile'); ?></th>
                            <td>
                                <textarea name="instafeed_profile_custom_fields" rows="5" cols="50" placeholder="field_name|Field Label|field_type"><?php echo esc_textarea($custom_fields); ?></textarea>
                                <p class="description">
                                    <?php _e('Add custom fields (one per line): field_name|Field Label|text', 'instafeed-profile'); ?><br>
                                    <?php _e('Supported types: text, textarea, select, checkbox, url', 'instafeed-profile'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                    
                    <?php submit_button(); ?>
                </form>
            </div>
            
            <div class="card">
                <h2><?php _e('Profile Features', 'instafeed-profile'); ?></h2>
                <p><?php _e('This profile system includes:', 'instafeed-profile'); ?></p>
                <ul>
                    <li>✅ <?php _e('Custom avatar uploads', 'instafeed-profile'); ?></li>
                    <li>✅ <?php _e('Bio and personal information', 'instafeed-profile'); ?></li>
                    <li>✅ <?php _e('Social media links integration', 'instafeed-profile'); ?></li>
                    <li>✅ <?php _e('Privacy controls', 'instafeed-profile'); ?></li>
                    <li>✅ <?php _e('Following/followers system', 'instafeed-profile'); ?></li>
                    <li>✅ <?php _e('Activity tracking', 'instafeed-profile'); ?></li>
                    <li>✅ <?php _e('Post management from profile', 'instafeed-profile'); ?></li>
                    <li>✅ <?php _e('Instagram-style interface', 'instafeed-profile'); ?></li>
                </ul>
            </div>
            
            <div class="card">
                <h2><?php _e('Profile Statistics', 'instafeed-profile'); ?></h2>
                <?php $this->display_profile_statistics(); ?>
            </div>
        </div>
        <?php
    }
    
    public function user_profiles_page() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'instafeed_user_profiles';
        $profiles = $wpdb->get_results(
            "SELECT p.*, u.display_name, u.user_email, u.user_registered 
             FROM $table_name p 
             LEFT JOIN {$wpdb->users} u ON p.user_id = u.ID 
             ORDER BY p.updated_at DESC 
             LIMIT 50"
        );
        
        ?>
        <div class="wrap">
            <h1><?php _e('InstaFeed User Profiles', 'instafeed-profile'); ?></h1>
            
            <div class="card">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('User', 'instafeed-profile'); ?></th>
                            <th><?php _e('Avatar', 'instafeed-profile'); ?></th>
                            <th><?php _e('Bio', 'instafeed-profile'); ?></th>
                            <th><?php _e('Location', 'instafeed-profile'); ?></th>
                            <th><?php _e('Privacy', 'instafeed-profile'); ?></th>
                            <th><?php _e('Last Updated', 'instafeed-profile'); ?></th>
                            <th><?php _e('Actions', 'instafeed-profile'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($profiles)) : ?>
                            <tr>
                                <td colspan="7"><?php _e('No profiles found.', 'instafeed-profile'); ?></td>
                            </tr>
                        <?php else : ?>
                            <?php foreach ($profiles as $profile) : ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($profile->display_name); ?></strong><br>
                                        <small><?php echo esc_html($profile->user_email); ?></small>
                                    </td>
                                    <td>
                                        <?php if ($profile->avatar_url) : ?>
                                            <img src="<?php echo esc_url($profile->avatar_url); ?>" alt="Avatar" style="width: 40px; height: 40px; border-radius: 50%;">
                                        <?php else : ?>
                                            <?php echo get_avatar($profile->user_id, 40); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo esc_html(wp_trim_words($profile->bio, 10)); ?>
                                    </td>
                                    <td>
                                        <?php echo esc_html($profile->location); ?>
                                    </td>
                                    <td>
                                        <span class="privacy-<?php echo esc_attr($profile->privacy_setting); ?>">
                                            <?php echo ucfirst($profile->privacy_setting); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo human_time_diff(strtotime($profile->updated_at), current_time('timestamp')) . ' ago'; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo get_edit_user_link($profile->user_id); ?>" class="button button-small">
                                            <?php _e('Edit User', 'instafeed-profile'); ?>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        
        <style>
        .privacy-public { color: #46b450; }
        .privacy-private { color: #dc3232; }
        .privacy-friends { color: #ffb900; }
        </style>
        <?php
    }
    
    private function display_profile_statistics() {
        global $wpdb;
        
        $profiles_table = $wpdb->prefix . 'instafeed_user_profiles';
        $followers_table = $wpdb->prefix . 'instafeed_user_followers';
        
        // Get statistics
        $total_profiles = $wpdb->get_var("SELECT COUNT(*) FROM $profiles_table");
        $profiles_with_avatars = $wpdb->get_var("SELECT COUNT(*) FROM $profiles_table WHERE avatar_url != ''");
        $profiles_with_bios = $wpdb->get_var("SELECT COUNT(*) FROM $profiles_table WHERE bio != ''");
        $total_follows = $wpdb->get_var("SELECT COUNT(*) FROM $followers_table WHERE status = 'active'");
        
        // Privacy distribution
        $privacy_stats = $wpdb->get_results(
            "SELECT privacy_setting, COUNT(*) as count FROM $profiles_table GROUP BY privacy_setting"
        );
        
        ?>
        <div class="instafeed-stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
            <div class="stat-card" style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="margin: 0 0 10px; color: #0073aa;"><?php echo number_format($total_profiles); ?></h3>
                <p style="margin: 0; color: #666;"><?php _e('Total Profiles', 'instafeed-profile'); ?></p>
            </div>
            
            <div class="stat-card" style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="margin: 0 0 10px; color: #46b450;"><?php echo number_format($profiles_with_avatars); ?></h3>
                <p style="margin: 0; color: #666;"><?php _e('Custom Avatars', 'instafeed-profile'); ?></p>
            </div>
            
            <div class="stat-card" style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="margin: 0 0 10px; color: #ffb900;"><?php echo number_format($profiles_with_bios); ?></h3>
                <p style="margin: 0; color: #666;"><?php _e('Profiles with Bios', 'instafeed-profile'); ?></p>
            </div>
            
            <div class="stat-card" style="background: #f9f9f9; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="margin: 0 0 10px; color: #dc3232;"><?php echo number_format($total_follows); ?></h3>
                <p style="margin: 0; color: #666;"><?php _e('Total Follows', 'instafeed-profile'); ?></p>
            </div>
        </div>
        
        <?php if (!empty($privacy_stats)) : ?>
        <h4><?php _e('Privacy Settings Distribution', 'instafeed-profile'); ?></h4>
        <table class="wp-list-table widefat">
            <thead>
                <tr>
                    <th><?php _e('Privacy Setting', 'instafeed-profile'); ?></th>
                    <th><?php _e('Count', 'instafeed-profile'); ?></th>
                    <th><?php _e('Percentage', 'instafeed-profile'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($privacy_stats as $stat) : ?>
                    <tr>
                        <td><?php echo ucfirst($stat->privacy_setting); ?></td>
                        <td><?php echo number_format($stat->count); ?></td>
                        <td><?php echo $total_profiles > 0 ? round(($stat->count / $total_profiles) * 100, 1) : 0; ?>%</td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>
        <?php
    }
}
