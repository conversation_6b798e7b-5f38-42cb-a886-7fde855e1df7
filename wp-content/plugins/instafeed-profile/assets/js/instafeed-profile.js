/**
 * InstaFeed Profile JavaScript
 * Handles profile management functionality
 */

(function($) {
    'use strict';

    let currentTab = 'posts';
    let isLoading = false;

    $(document).ready(function() {
        console.log('InstaFeed Profile plugin JavaScript loaded');
        initializeProfile();

        // Make sure global functions are available
        window.instaFeedProfileLoaded = true;
    });

    function initializeProfile() {
        bindEvents();
        setupBioCharacterCount();
        setupAvatarUpload();
    }

    function bindEvents() {
        // Form submissions
        $(document).on('submit', '#instafeed-edit-profile-form', handleUpdateProfile);
        
        // Tab switching
        $(document).on('click', '.instafeed-tab-btn', function() {
            const tab = $(this).text().toLowerCase();
            showProfileTab(tab);
        });
        
        // Avatar management
        $(document).on('click', '.instafeed-change-avatar-btn', changeProfileAvatar);
        $(document).on('change', '#avatar-upload', handleAvatarSelect);
    }

    function setupBioCharacterCount() {
        $(document).on('input', '#edit-bio', function() {
            const count = $(this).val().length;
            $('#bio-char-count').text(count);
            
            if (count > 450) {
                $('#bio-char-count').css('color', '#e74c3c');
            } else {
                $('#bio-char-count').css('color', '#8e8e8e');
            }
        });
    }

    function setupAvatarUpload() {
        // Handle avatar file selection
        $(document).on('change', '#avatar-upload', function() {
            const file = this.files[0];
            if (file) {
                handleAvatarUpload(file);
            }
        });
    }

    function handleUpdateProfile(e) {
        e.preventDefault();
        
        if (isLoading) return;
        
        const form = $(this);
        const formData = new FormData(form[0]);
        const submitBtn = form.find('button[type="submit"]');
        
        // Add AJAX data
        formData.append('action', 'instafeed_update_profile');
        formData.append('nonce', instafeed_profile_ajax.nonce);
        
        isLoading = true;
        submitBtn.prop('disabled', true).text(instafeed_profile_ajax.strings.updating_profile);
        
        $.ajax({
            url: instafeed_profile_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    updateProfileDisplay(response.data.profile_data);
                    closeInstaFeedModal('instafeed-edit-profile-modal');
                } else {
                    showNotification(response.data || instafeed_profile_ajax.strings.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_profile_ajax.strings.error_occurred, 'error');
            },
            complete: function() {
                isLoading = false;
                submitBtn.prop('disabled', false).text('Save Changes');
            }
        });
    }

    function handleAvatarUpload(file) {
        // Validate file type
        const allowedTypes = instafeed_profile_ajax.allowed_types;
        const fileExtension = file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            showNotification(instafeed_profile_ajax.strings.invalid_file_type, 'error');
            return;
        }
        
        // Validate file size
        if (file.size > instafeed_profile_ajax.max_file_size) {
            showNotification(instafeed_profile_ajax.strings.file_too_large, 'error');
            return;
        }
        
        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#edit-avatar-preview').attr('src', e.target.result);
        };
        reader.readAsDataURL(file);
        
        // Upload avatar
        const formData = new FormData();
        formData.append('avatar', file);
        formData.append('action', 'instafeed_upload_avatar');
        formData.append('nonce', instafeed_profile_ajax.nonce);
        
        showNotification(instafeed_profile_ajax.strings.uploading_avatar, 'info');
        
        $.ajax({
            url: instafeed_profile_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    $('#profile-avatar-display').attr('src', response.data.avatar_url);
                    $('#edit-avatar-preview').attr('src', response.data.avatar_url);
                } else {
                    showNotification(response.data || instafeed_profile_ajax.strings.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_profile_ajax.strings.error_occurred, 'error');
            }
        });
    }

    function loadProfilePosts() {
        if (isLoading) return;
        
        isLoading = true;
        $('#profile-posts-grid').html('<div class="instafeed-profile-loading">Loading posts...</div>');
        
        $.ajax({
            url: instafeed_profile_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_get_profile_posts',
                nonce: instafeed_profile_ajax.nonce,
                page: 1
            },
            success: function(response) {
                if (response.success) {
                    displayProfilePosts(response.data.posts);
                } else {
                    $('#profile-posts-grid').html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">📷</span><h3>No posts yet</h3><p>Start sharing your moments!</p></div>');
                }
            },
            error: function() {
                $('#profile-posts-grid').html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">❌</span><h3>Error loading posts</h3><p>Please try again later.</p></div>');
            },
            complete: function() {
                isLoading = false;
            }
        });
    }

    function loadProfileActivity() {
        if (isLoading) return;
        
        isLoading = true;
        $('#profile-activity').html('<div class="instafeed-profile-loading">Loading activity...</div>');
        
        $.ajax({
            url: instafeed_profile_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_get_profile_activity',
                nonce: instafeed_profile_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayProfileActivity(response.data.activities);
                } else {
                    $('#profile-activity').html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">📊</span><h3>No activity yet</h3><p>Your activity will appear here.</p></div>');
                }
            },
            error: function() {
                $('#profile-activity').html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">❌</span><h3>Error loading activity</h3><p>Please try again later.</p></div>');
            },
            complete: function() {
                isLoading = false;
            }
        });
    }

    function displayProfilePosts(posts) {
        const container = $('#profile-posts-grid');
        container.empty();
        
        if (posts.length === 0) {
            container.html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">📷</span><h3>No posts yet</h3><p>Start sharing your moments!</p></div>');
            return;
        }
        
        posts.forEach(function(post) {
            const postItem = $(`
                <div class="instafeed-profile-post-item" data-post-id="${post.id}">
                    <img src="${post.thumbnail}" alt="${post.title}">
                    <div class="instafeed-profile-post-overlay">
                        <span>❤️ ${post.likes}</span>
                        <span>💬 ${post.comments}</span>
                    </div>
                </div>
            `);
            
            postItem.on('click', function() {
                window.open(post.url, '_blank');
            });
            
            container.append(postItem);
        });
    }

    function displayProfileActivity(activities) {
        const container = $('#profile-activity');
        container.empty();
        
        if (activities.length === 0) {
            container.html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">📊</span><h3>No activity yet</h3><p>Your activity will appear here.</p></div>');
            return;
        }
        
        activities.forEach(function(activity) {
            const activityIcon = getActivityIcon(activity.action);
            const activityItem = $(`
                <div class="instafeed-activity-item">
                    <div class="instafeed-activity-icon">${activityIcon}</div>
                    <div class="instafeed-activity-content">
                        <div class="instafeed-activity-description">${activity.description}</div>
                        <div class="instafeed-activity-time">${activity.date}</div>
                    </div>
                </div>
            `);
            
            container.append(activityItem);
        });
    }

    function getActivityIcon(action) {
        const icons = {
            'profile_updated': '👤',
            'avatar_updated': '📷',
            'avatar_removed': '🗑️',
            'post_created': '📝',
            'user_followed': '👥',
            'user_unfollowed': '👋',
            'password_changed': '🔒'
        };
        
        return icons[action] || '📊';
    }

    function updateProfileDisplay(profileData) {
        // Update profile display with new data
        if (profileData.bio !== undefined) {
            $('#profile-bio-display').text(profileData.bio || 'No bio yet');
        }
        
        if (profileData.location !== undefined) {
            if (profileData.location) {
                $('#profile-location-display').text(profileData.location).parent().show();
            } else {
                $('#profile-location-display').parent().hide();
            }
        }
        
        if (profileData.website !== undefined) {
            if (profileData.website) {
                $('#profile-website-display').text(profileData.website).attr('href', profileData.website).parent().show();
            } else {
                $('#profile-website-display').parent().hide();
            }
        }
    }

    // Global functions for theme integration
    window.showProfileTab = function(tab) {
        // Update active tab
        $('.instafeed-tab-btn').removeClass('active');
        $(`.instafeed-tab-btn:contains("${tab.charAt(0).toUpperCase() + tab.slice(1)}")`).addClass('active');
        
        // Hide all tab content
        $('.instafeed-tab-content').removeClass('active');
        
        // Show selected tab content
        $(`#profile-${tab}-tab`).addClass('active');
        
        currentTab = tab;
        
        // Load content based on tab
        switch(tab) {
            case 'posts':
                loadProfilePosts();
                break;
            case 'saved':
                loadSavedPosts();
                break;
            case 'activity':
                loadProfileActivity();
                break;
        }
    };

    window.changeProfileAvatar = function() {
        $('#avatar-upload').click();
    };

    window.selectNewAvatar = function() {
        $('#avatar-upload').click();
    };

    window.removeAvatar = function() {
        if (!confirm(instafeed_profile_ajax.strings.confirm_delete_avatar)) {
            return;
        }
        
        $.ajax({
            url: instafeed_profile_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_remove_avatar',
                nonce: instafeed_profile_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    $('#profile-avatar-display').attr('src', response.data.default_avatar);
                    $('#edit-avatar-preview').attr('src', response.data.default_avatar);
                } else {
                    showNotification(response.data || instafeed_profile_ajax.strings.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_profile_ajax.strings.error_occurred, 'error');
            }
        });
    };

    function loadSavedPosts() {
        $('#profile-saved-grid').html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">🔖</span><h3>No saved posts</h3><p>Posts you save will appear here.</p></div>');
    }

    function showNotification(message, type = 'info') {
        const notification = $(`
            <div class="instafeed-notification ${type}">
                ${message}
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(() => {
            notification.addClass('show');
        }, 100);
        
        setTimeout(() => {
            notification.removeClass('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Initialize profile when modal opens
    $(document).on('click', '[onclick*="instafeed-profile-modal"]', function() {
        setTimeout(() => {
            showProfileTab('posts');
        }, 300);
    });

    // Add profile completion suggestions
    function showProfileSuggestions() {
        const suggestions = [
            { text: 'Add a profile photo', action: 'upload_avatar', points: 20 },
            { text: 'Write a bio', action: 'add_bio', points: 25 },
            { text: 'Add your location', action: 'add_location', points: 15 },
            { text: 'Add your website', action: 'add_website', points: 15 },
            { text: 'Connect social media', action: 'add_social', points: 15 }
        ];

        // Implementation for showing suggestions
        console.log('Profile suggestions:', suggestions);
    }

    // Enhanced profile loading with better UX
    function enhanceProfileExperience() {
        // Add loading states
        $('.instafeed-profile-loading').each(function() {
            $(this).html('<div class="instafeed-spinner"></div><span>Loading...</span>');
        });

        // Add smooth transitions
        $('.instafeed-tab-content').hide().first().show();

        // Add profile completion tracking
        updateProfileCompletion();
    }

    function updateProfileCompletion() {
        // Calculate and update profile completion percentage
        const completionElements = {
            avatar: $('#profile-avatar-display').attr('src') !== '',
            bio: $('#profile-bio-display').text().trim() !== 'No bio yet. Tell us about yourself!',
            location: $('#profile-location-display').text().trim() !== '',
            website: $('#profile-website-display').attr('href') !== '',
            social: $('.instafeed-social-links a').length > 0
        };

        const completed = Object.values(completionElements).filter(Boolean).length;
        const percentage = Math.round((completed / Object.keys(completionElements).length) * 100);

        $('.instafeed-completion-progress').css('width', percentage + '%');
        $('.instafeed-completion-text').text(`Profile ${percentage}% complete`);

        if (percentage === 100) {
            $('.instafeed-profile-completion').fadeOut();
        }
    }

    // Global functions for followers/following
    window.showFollowersModal = function() {
        showNotification('Followers feature coming soon!', 'info');
    };

    window.showFollowingModal = function() {
        showNotification('Following feature coming soon!', 'info');
    };

    // Initialize enhanced features
    $(document).ready(function() {
        enhanceProfileExperience();
    });

})(jQuery);
