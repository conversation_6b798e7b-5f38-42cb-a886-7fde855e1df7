/**
 * InstaFeed Profile JavaScript
 * Handles profile management functionality
 */

(function($) {
    'use strict';

    let currentTab = 'posts';
    let isLoading = false;

    $(document).ready(function() {
        console.log('InstaFeed Profile plugin JavaScript loaded');
        initializeProfile();

        // Make sure global functions are available
        window.instaFeedProfileLoaded = true;
    });

    function initializeProfile() {
        bindEvents();
        setupBioCharacterCount();
        setupAvatarUpload();
    }

    function bindEvents() {
        // Form submissions
        $(document).on('submit', '#instafeed-edit-profile-form', handleUpdateProfile);
        
        // Tab switching
        $(document).on('click', '.instafeed-tab-btn', function() {
            const tab = $(this).text().toLowerCase();
            showProfileTab(tab);
        });
        
        // Avatar management
        $(document).on('click', '.instafeed-change-avatar-btn', changeProfileAvatar);
        $(document).on('change', '#avatar-upload', handleAvatarSelect);
    }

    function setupBioCharacterCount() {
        $(document).on('input', '#edit-bio', function() {
            const count = $(this).val().length;
            $('#bio-char-count').text(count);
            
            if (count > 450) {
                $('#bio-char-count').css('color', '#e74c3c');
            } else {
                $('#bio-char-count').css('color', '#8e8e8e');
            }
        });
    }

    function setupAvatarUpload() {
        // Handle avatar file selection
        $(document).on('change', '#avatar-upload', function() {
            const file = this.files[0];
            if (file) {
                handleAvatarUpload(file);
            }
        });
    }

    function handleUpdateProfile(e) {
        e.preventDefault();
        
        if (isLoading) return;
        
        const form = $(this);
        const formData = new FormData(form[0]);
        const submitBtn = form.find('button[type="submit"]');
        
        // Add AJAX data
        formData.append('action', 'instafeed_update_profile');
        formData.append('nonce', instafeed_profile_ajax.nonce);
        
        isLoading = true;
        submitBtn.prop('disabled', true).text(instafeed_profile_ajax.strings.updating_profile);
        
        $.ajax({
            url: instafeed_profile_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    updateProfileDisplay(response.data.profile_data);
                    closeInstaFeedModal('instafeed-edit-profile-modal');
                } else {
                    showNotification(response.data || instafeed_profile_ajax.strings.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_profile_ajax.strings.error_occurred, 'error');
            },
            complete: function() {
                isLoading = false;
                submitBtn.prop('disabled', false).text('Save Changes');
            }
        });
    }

    function handleAvatarUpload(file) {
        // Validate file type
        const allowedTypes = instafeed_profile_ajax.allowed_types;
        const fileExtension = file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            showNotification(instafeed_profile_ajax.strings.invalid_file_type, 'error');
            return;
        }
        
        // Validate file size
        if (file.size > instafeed_profile_ajax.max_file_size) {
            showNotification(instafeed_profile_ajax.strings.file_too_large, 'error');
            return;
        }
        
        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#edit-avatar-preview').attr('src', e.target.result);
        };
        reader.readAsDataURL(file);
        
        // Upload avatar
        const formData = new FormData();
        formData.append('avatar', file);
        formData.append('action', 'instafeed_upload_avatar');
        formData.append('nonce', instafeed_profile_ajax.nonce);
        
        showNotification(instafeed_profile_ajax.strings.uploading_avatar, 'info');
        
        $.ajax({
            url: instafeed_profile_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    $('#profile-avatar-display').attr('src', response.data.avatar_url);
                    $('#edit-avatar-preview').attr('src', response.data.avatar_url);
                } else {
                    showNotification(response.data || instafeed_profile_ajax.strings.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_profile_ajax.strings.error_occurred, 'error');
            }
        });
    }

    function loadProfilePosts() {
        if (isLoading) return;
        
        isLoading = true;
        $('#profile-posts-grid').html('<div class="instafeed-profile-loading">Loading posts...</div>');
        
        $.ajax({
            url: instafeed_profile_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_get_profile_posts',
                nonce: instafeed_profile_ajax.nonce,
                page: 1
            },
            success: function(response) {
                if (response.success) {
                    displayProfilePosts(response.data.posts);
                } else {
                    $('#profile-posts-grid').html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">📷</span><h3>No posts yet</h3><p>Start sharing your moments!</p></div>');
                }
            },
            error: function() {
                $('#profile-posts-grid').html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">❌</span><h3>Error loading posts</h3><p>Please try again later.</p></div>');
            },
            complete: function() {
                isLoading = false;
            }
        });
    }

    function loadProfileActivity() {
        if (isLoading) return;
        
        isLoading = true;
        $('#profile-activity').html('<div class="instafeed-profile-loading">Loading activity...</div>');
        
        $.ajax({
            url: instafeed_profile_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_get_profile_activity',
                nonce: instafeed_profile_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    displayProfileActivity(response.data.activities);
                } else {
                    $('#profile-activity').html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">📊</span><h3>No activity yet</h3><p>Your activity will appear here.</p></div>');
                }
            },
            error: function() {
                $('#profile-activity').html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">❌</span><h3>Error loading activity</h3><p>Please try again later.</p></div>');
            },
            complete: function() {
                isLoading = false;
            }
        });
    }

    function displayProfilePosts(posts) {
        const container = $('#profile-posts-grid');
        container.empty();
        
        if (posts.length === 0) {
            container.html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">📷</span><h3>No posts yet</h3><p>Start sharing your moments!</p></div>');
            return;
        }
        
        posts.forEach(function(post) {
            const postItem = $(`
                <div class="instafeed-profile-post-item" data-post-id="${post.id}">
                    <img src="${post.thumbnail}" alt="${post.title}">
                    <div class="instafeed-profile-post-overlay">
                        <span>❤️ ${post.likes}</span>
                        <span>💬 ${post.comments}</span>
                    </div>
                </div>
            `);
            
            postItem.on('click', function() {
                window.open(post.url, '_blank');
            });
            
            container.append(postItem);
        });
    }

    function displayProfileActivity(activities) {
        const container = $('#profile-activity');
        container.empty();
        
        if (activities.length === 0) {
            container.html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">📊</span><h3>No activity yet</h3><p>Your activity will appear here.</p></div>');
            return;
        }
        
        activities.forEach(function(activity) {
            const activityIcon = getActivityIcon(activity.action);
            const activityItem = $(`
                <div class="instafeed-activity-item">
                    <div class="instafeed-activity-icon">${activityIcon}</div>
                    <div class="instafeed-activity-content">
                        <div class="instafeed-activity-description">${activity.description}</div>
                        <div class="instafeed-activity-time">${activity.date}</div>
                    </div>
                </div>
            `);
            
            container.append(activityItem);
        });
    }

    function getActivityIcon(action) {
        const icons = {
            'profile_updated': '👤',
            'avatar_updated': '📷',
            'avatar_removed': '🗑️',
            'post_created': '📝',
            'user_followed': '👥',
            'user_unfollowed': '👋',
            'password_changed': '🔒'
        };
        
        return icons[action] || '📊';
    }

    function updateProfileDisplay(profileData) {
        // Update profile display with new data
        if (profileData.bio !== undefined) {
            $('#profile-bio-display').text(profileData.bio || 'No bio yet');
        }
        
        if (profileData.location !== undefined) {
            if (profileData.location) {
                $('#profile-location-display').text(profileData.location).parent().show();
            } else {
                $('#profile-location-display').parent().hide();
            }
        }
        
        if (profileData.website !== undefined) {
            if (profileData.website) {
                $('#profile-website-display').text(profileData.website).attr('href', profileData.website).parent().show();
            } else {
                $('#profile-website-display').parent().hide();
            }
        }
    }

    // Global functions for theme integration
    window.showProfileTab = function(tab) {
        // Update active tab
        $('.instafeed-tab-btn').removeClass('active');
        $(`.instafeed-tab-btn:contains("${tab.charAt(0).toUpperCase() + tab.slice(1)}")`).addClass('active');
        
        // Hide all tab content
        $('.instafeed-tab-content').removeClass('active');
        
        // Show selected tab content
        $(`#profile-${tab}-tab`).addClass('active');
        
        currentTab = tab;
        
        // Load content based on tab
        switch(tab) {
            case 'posts':
                loadProfilePosts();
                break;
            case 'likes':
                loadLikedPosts();
                break;
            case 'friends':
                loadUserFriends();
                break;
            case 'messages':
                loadUserMessages();
                break;
            case 'saved':
                loadSavedPosts();
                break;
            case 'activity':
                loadProfileActivity();
                break;
        }

        // Update URL hash for navigation
        if (history.pushState) {
            history.pushState(null, null, '#profile-' + tab);
        }
    };

    // Enhanced modal control - prevent accidental closing
    window.preventModalClose = function() {
        // Override the modal overlay click
        $('.instafeed-profile-modal .instafeed-modal-overlay').off('click');

        // Add confirmation for accidental closes
        $(document).on('keydown.profile-modal', function(e) {
            if (e.key === 'Escape' && $('#instafeed-profile-modal').is(':visible')) {
                e.preventDefault();
                confirmCloseProfile();
            }
        });
    };

    window.confirmCloseProfile = function() {
        if (confirm('Are you sure you want to close your profile? Any unsaved changes will be lost.')) {
            closeInstaFeedModal('instafeed-profile-modal');
            $(document).off('keydown.profile-modal');
        }
    };

    // Enhanced profile edit mode
    window.showProfileEditMode = function() {
        console.log('Entering edit mode');
        createInlineEditMode();
    };

    function createInlineEditMode() {
        // Remove existing edit interface
        $('.instafeed-inline-edit').remove();

        // Create inline edit interface with enhanced styling
        const editInterface = `
            <div class="instafeed-inline-edit" style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(145deg, rgba(0, 0, 0, 0.98), rgba(26, 26, 26, 0.98));
                z-index: 1000;
                padding: 20px;
                overflow-y: auto;
                backdrop-filter: blur(10px);
                animation: slideInFromRight 0.3s ease;
            ">
                <div class="edit-header" style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 30px;
                    padding-bottom: 15px;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                ">
                    <h3 style="color: #fff; margin: 0; font-size: 20px; font-weight: 700;">✏️ Edit Profile</h3>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="saveProfileChanges()" class="save-btn">💾 Save Changes</button>
                        <button onclick="cancelProfileEdit()" class="cancel-btn">❌ Cancel</button>
                    </div>
                </div>
                <div class="edit-content" style="
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 12px;
                    padding: 30px;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                ">
                    <div style="text-align: center; color: #8e8e8e; padding: 40px;">
                        <div style="font-size: 48px; margin-bottom: 20px;">🚧</div>
                        <h4 style="color: #fff; margin-bottom: 10px;">Edit Mode Coming Soon!</h4>
                        <p>Advanced profile editing features are being developed.</p>
                        <p>For now, you can edit your profile through WordPress admin.</p>
                    </div>
                </div>
            </div>
        `;

        $('.instafeed-profile-modal-content').append(editInterface);

        // Add button styles
        $('<style>').text(`
            .save-btn, .cancel-btn {
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                border: none;
                transition: all 0.3s ease;
            }
            .save-btn {
                background: linear-gradient(45deg, #0095f6, #1877f2);
                color: #fff;
                box-shadow: 0 4px 12px rgba(0, 149, 246, 0.3);
            }
            .save-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0, 149, 246, 0.4);
            }
            .cancel-btn {
                background: rgba(255, 255, 255, 0.1);
                color: #fff;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            .cancel-btn:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            @keyframes slideInFromRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `).appendTo('head');
    }

    window.saveProfileChanges = function() {
        $('.instafeed-inline-edit').remove();
        showNotification('Profile updated successfully!', 'success');
    };

    window.cancelProfileEdit = function() {
        $('.instafeed-inline-edit').css('animation', 'slideOutToRight 0.3s ease');
        setTimeout(() => $('.instafeed-inline-edit').remove(), 300);
    };

    window.changeProfileAvatar = function() {
        $('#avatar-upload').click();
    };

    window.selectNewAvatar = function() {
        $('#avatar-upload').click();
    };

    window.removeAvatar = function() {
        if (!confirm(instafeed_profile_ajax.strings.confirm_delete_avatar)) {
            return;
        }
        
        $.ajax({
            url: instafeed_profile_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'instafeed_remove_avatar',
                nonce: instafeed_profile_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    $('#profile-avatar-display').attr('src', response.data.default_avatar);
                    $('#edit-avatar-preview').attr('src', response.data.default_avatar);
                } else {
                    showNotification(response.data || instafeed_profile_ajax.strings.error_occurred, 'error');
                }
            },
            error: function() {
                showNotification(instafeed_profile_ajax.strings.error_occurred, 'error');
            }
        });
    };

    function loadSavedPosts() {
        $('#profile-saved-grid').html('<div class="instafeed-empty-state"><span class="instafeed-empty-state-icon">🔖</span><h3>No saved posts</h3><p>Posts you save will appear here.</p></div>');
    }

    function showNotification(message, type = 'info') {
        const notification = $(`
            <div class="instafeed-notification ${type}">
                ${message}
            </div>
        `);
        
        $('body').append(notification);
        
        setTimeout(() => {
            notification.addClass('show');
        }, 100);
        
        setTimeout(() => {
            notification.removeClass('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Initialize profile when modal opens
    $(document).on('click', '[onclick*="instafeed-profile-modal"]', function() {
        setTimeout(() => {
            showProfileTab('posts');
        }, 300);
    });

    // Add profile completion suggestions
    function showProfileSuggestions() {
        const suggestions = [
            { text: 'Add a profile photo', action: 'upload_avatar', points: 20 },
            { text: 'Write a bio', action: 'add_bio', points: 25 },
            { text: 'Add your location', action: 'add_location', points: 15 },
            { text: 'Add your website', action: 'add_website', points: 15 },
            { text: 'Connect social media', action: 'add_social', points: 15 }
        ];

        // Implementation for showing suggestions
        console.log('Profile suggestions:', suggestions);
    }

    // Enhanced profile loading with better UX
    function enhanceProfileExperience() {
        // Add loading states
        $('.instafeed-profile-loading').each(function() {
            $(this).html('<div class="instafeed-spinner"></div><span>Loading...</span>');
        });

        // Add smooth transitions
        $('.instafeed-tab-content').hide().first().show();

        // Add profile completion tracking
        updateProfileCompletion();
    }

    function updateProfileCompletion() {
        // Calculate and update profile completion percentage
        const completionElements = {
            avatar: $('#profile-avatar-display').attr('src') !== '',
            bio: $('#profile-bio-display').text().trim() !== 'No bio yet. Tell us about yourself!',
            location: $('#profile-location-display').text().trim() !== '',
            website: $('#profile-website-display').attr('href') !== '',
            social: $('.instafeed-social-links a').length > 0
        };

        const completed = Object.values(completionElements).filter(Boolean).length;
        const percentage = Math.round((completed / Object.keys(completionElements).length) * 100);

        $('.instafeed-completion-progress').css('width', percentage + '%');
        $('.instafeed-completion-text').text(`Profile ${percentage}% complete`);

        if (percentage === 100) {
            $('.instafeed-profile-completion').fadeOut();
        }
    }

    // Global functions for followers/following
    window.showFollowersModal = function() {
        showNotification('Followers feature coming soon!', 'info');
    };

    window.showFollowingModal = function() {
        showNotification('Following feature coming soon!', 'info');
    };

    // New content loading functions
    function loadLikedPosts() {
        const container = $('#profile-likes-grid');
        container.html('<div class="instafeed-loading"><div class="loading-spinner"></div><p>Loading posts you liked...</p></div>');

        setTimeout(() => {
            container.html(`
                <div style="text-align: center; padding: 40px; color: #8e8e8e;">
                    <div style="font-size: 48px; margin-bottom: 20px;">❤️</div>
                    <h4 style="color: #fff; margin-bottom: 10px;">No Liked Posts Yet</h4>
                    <p>Posts you like will appear here</p>
                </div>
            `);
        }, 1000);
    }

    function loadUserFriends() {
        const container = $('#profile-friends-grid');
        container.html('<div class="instafeed-loading"><div class="loading-spinner"></div><p>Loading your friends...</p></div>');

        setTimeout(() => {
            container.html(`
                <div style="text-align: center; padding: 40px; color: #8e8e8e;">
                    <div style="font-size: 48px; margin-bottom: 20px;">👥</div>
                    <h4 style="color: #fff; margin-bottom: 10px;">No Friends Yet</h4>
                    <p>Start following other users to build your network</p>
                    <button style="
                        background: #0095f6;
                        color: #fff;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        margin-top: 20px;
                        cursor: pointer;
                        font-weight: 600;
                    " onclick="findFriends()">Find Friends</button>
                </div>
            `);
        }, 1000);
    }

    function loadUserMessages() {
        const container = $('#profile-messages-list');
        container.html('<div class="instafeed-loading"><div class="loading-spinner"></div><p>Loading your messages...</p></div>');

        setTimeout(() => {
            container.html(`
                <div style="text-align: center; padding: 40px; color: #8e8e8e;">
                    <div style="font-size: 48px; margin-bottom: 20px;">💬</div>
                    <h4 style="color: #fff; margin-bottom: 10px;">No Messages Yet</h4>
                    <p>Your direct messages will appear here</p>
                    <button style="
                        background: linear-gradient(45deg, #0095f6, #1877f2);
                        color: #fff;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        margin-top: 20px;
                        cursor: pointer;
                        font-weight: 600;
                    " onclick="startNewMessage()">Send Your First Message</button>
                </div>
            `);
        }, 1000);
    }

    // Helper functions for new features
    window.findFriends = function() {
        showNotification('Friend discovery feature coming soon!', 'info');
    };

    window.startNewMessage = function() {
        showNotification('Direct messaging feature coming soon!', 'info');
    };

    // Initialize enhanced features and prevent modal close
    $(document).ready(function() {
        enhanceProfileExperience();

        // Initialize modal controls when profile modal opens
        $(document).on('click', '[onclick*="instafeed-profile-modal"]', function() {
            setTimeout(() => {
                preventModalClose();
                showProfileTab('posts');
            }, 300);
        });
    });

})(jQuery);
