/* InstaFeed Profile Plugin Styles */

/* Profile Modal Specific Styles */
.instafeed-profile-container {
    color: #ffffff;
}

/* Profile Header */
.instafeed-profile-header {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #262626;
}

.instafeed-profile-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.instafeed-profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #262626;
    transition: border-color 0.3s ease;
}

.instafeed-profile-avatar:hover {
    border-color: #0095f6;
}

.instafeed-profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.instafeed-change-avatar-btn {
    background: none;
    border: none;
    color: #0095f6;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: color 0.3s ease;
}

.instafeed-change-avatar-btn:hover {
    color: #1877f2;
}

/* Profile Info */
.instafeed-profile-info {
    flex: 1;
}

.instafeed-profile-username {
    margin-bottom: 20px;
}

.instafeed-profile-username h3 {
    margin: 0 0 5px;
    font-size: 24px;
    font-weight: 300;
    color: #ffffff;
}

.instafeed-username {
    color: #8e8e8e;
    font-size: 16px;
}

/* Profile Stats */
.instafeed-profile-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 20px;
}

.instafeed-stat {
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.instafeed-stat:hover {
    transform: translateY(-2px);
}

.instafeed-stat-number {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.instafeed-stat-label {
    display: block;
    font-size: 14px;
    color: #8e8e8e;
    margin-top: 2px;
}

/* Profile Bio */
.instafeed-profile-bio {
    margin-bottom: 20px;
}

.instafeed-profile-bio p {
    margin: 0 0 10px;
    line-height: 1.5;
    color: #ffffff;
}

.instafeed-add-bio-btn {
    background: none;
    border: 1px solid #0095f6;
    color: #0095f6;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.instafeed-add-bio-btn:hover {
    background: #0095f6;
    color: #ffffff;
}

/* Profile Completion */
.instafeed-profile-completion {
    background: rgba(0, 149, 246, 0.1);
    border: 1px solid rgba(0, 149, 246, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.instafeed-completion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.instafeed-completion-text {
    color: #0095f6;
    font-size: 14px;
    font-weight: 600;
}

.instafeed-completion-btn {
    background: #0095f6;
    color: #ffffff;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.instafeed-completion-btn:hover {
    background: #1877f2;
    transform: translateY(-1px);
}

.instafeed-completion-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.instafeed-completion-progress {
    height: 100%;
    background: linear-gradient(90deg, #0095f6, #1877f2);
    border-radius: 2px;
    transition: width 0.5s ease;
}

/* Profile Details */
.instafeed-profile-details {
    margin-bottom: 20px;
}

.instafeed-profile-detail {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: #8e8e8e;
    font-size: 14px;
}

.instafeed-detail-icon {
    font-size: 16px;
}

.instafeed-profile-detail a {
    color: #0095f6;
    text-decoration: none;
}

.instafeed-profile-detail a:hover {
    text-decoration: underline;
}

/* Social Links */
.instafeed-social-links {
    display: flex;
    gap: 15px;
}

.instafeed-social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #262626;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.instafeed-social-link:hover {
    background: #0095f6;
    transform: translateY(-2px);
}

.instafeed-social-link.instagram:hover { background: #E4405F; }
.instafeed-social-link.twitter:hover { background: #1DA1F2; }
.instafeed-social-link.facebook:hover { background: #4267B2; }

/* Profile Tabs */
.instafeed-profile-tabs {
    display: flex;
    border-bottom: 1px solid #262626;
    margin-bottom: 20px;
}

.instafeed-tab-btn {
    background: none;
    border: none;
    padding: 15px 20px;
    color: #8e8e8e;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.instafeed-tab-btn:hover,
.instafeed-tab-btn.active {
    color: #ffffff;
    border-bottom-color: #0095f6;
}

/* Tab Content */
.instafeed-tab-content {
    display: none;
}

.instafeed-tab-content.active {
    display: block;
}

/* Profile Posts Grid */
.instafeed-profile-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.instafeed-profile-post-item {
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
    position: relative;
}

.instafeed-profile-post-item:hover {
    transform: scale(1.05);
}

.instafeed-profile-post-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.instafeed-profile-post-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: #ffffff;
    font-weight: 600;
}

.instafeed-profile-post-item:hover .instafeed-profile-post-overlay {
    opacity: 1;
}

/* Edit Profile Modal Styles */
.instafeed-avatar-upload-section {
    text-align: center;
    margin-bottom: 30px;
}

.instafeed-avatar-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.instafeed-current-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #262626;
}

.instafeed-current-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.instafeed-avatar-actions {
    display: flex;
    gap: 10px;
}

/* Form Sections */
.instafeed-form-section {
    margin: 30px 0;
    padding: 20px 0;
    border-top: 1px solid #262626;
}

.instafeed-form-section h4 {
    color: #ffffff;
    margin: 0 0 20px;
    font-size: 16px;
    font-weight: 600;
}

/* Checkbox Groups */
.instafeed-checkbox-group {
    margin-bottom: 15px;
}

.instafeed-checkbox-group label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: normal;
}

.instafeed-checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Profile Actions */
.instafeed-profile-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Activity Styles */
.instafeed-profile-activity {
    max-height: 400px;
    overflow-y: auto;
}

.instafeed-activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #262626;
}

.instafeed-activity-item:last-child {
    border-bottom: none;
}

.instafeed-activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #262626;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.instafeed-activity-content {
    flex: 1;
}

.instafeed-activity-description {
    color: #ffffff;
    margin-bottom: 5px;
}

.instafeed-activity-time {
    color: #8e8e8e;
    font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .instafeed-profile-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 20px;
    }
    
    .instafeed-profile-stats {
        justify-content: center;
        gap: 30px;
    }
    
    .instafeed-profile-posts-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .instafeed-avatar-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .instafeed-avatar-actions .instafeed-btn {
        width: 100%;
    }
    
    .instafeed-profile-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .instafeed-tab-btn {
        flex-shrink: 0;
        min-width: 80px;
    }
}

@media (max-width: 480px) {
    .instafeed-profile-avatar {
        width: 80px;
        height: 80px;
    }
    
    .instafeed-current-avatar {
        width: 80px;
        height: 80px;
    }
    
    .instafeed-profile-username h3 {
        font-size: 20px;
    }
    
    .instafeed-profile-stats {
        gap: 20px;
    }
    
    .instafeed-stat-number {
        font-size: 16px;
    }
    
    .instafeed-stat-label {
        font-size: 12px;
    }
}

/* Loading States */
.instafeed-profile-loading {
    text-align: center;
    padding: 40px;
    color: #8e8e8e;
}

.instafeed-profile-loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #262626;
    border-radius: 50%;
    border-top-color: #0095f6;
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty States */
.instafeed-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #8e8e8e;
}

.instafeed-empty-state-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.instafeed-empty-state h3 {
    color: #ffffff;
    margin-bottom: 10px;
}

.instafeed-empty-state p {
    margin: 0;
    line-height: 1.5;
}

/* Enhanced User Experience */
.instafeed-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #262626;
    border-top: 2px solid #0095f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Profile Enhancement Suggestions */
.instafeed-profile-suggestions {
    background: rgba(0, 149, 246, 0.05);
    border: 1px solid rgba(0, 149, 246, 0.2);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.instafeed-suggestion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.instafeed-suggestion-item:last-child {
    border-bottom: none;
}

.instafeed-suggestion-text {
    color: #ffffff;
    font-size: 14px;
}

.instafeed-suggestion-points {
    color: #0095f6;
    font-size: 12px;
    font-weight: 600;
}

.instafeed-suggestion-action {
    background: #0095f6;
    color: #ffffff;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.instafeed-suggestion-action:hover {
    background: #1877f2;
}

/* Profile Stats Enhancement */
.instafeed-stat {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 5px;
}

.instafeed-stat:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
}

/* Profile Actions Enhancement */
.instafeed-profile-actions {
    gap: 10px;
}

.instafeed-profile-actions .instafeed-btn {
    font-size: 14px;
    padding: 8px 16px;
}

/* Modal Enhancements */
.instafeed-modal-content {
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.instafeed-modal.show .instafeed-modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Profile Image Enhancements */
.instafeed-profile-avatar {
    position: relative;
    overflow: hidden;
}

.instafeed-profile-avatar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.instafeed-profile-avatar:hover::after {
    box-shadow: inset 0 0 0 2px #0095f6;
}

/* Tab Enhancement */
.instafeed-profile-tabs {
    position: relative;
}

.instafeed-profile-tabs::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: #262626;
}

.instafeed-tab-btn {
    position: relative;
    z-index: 1;
}

.instafeed-tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: #0095f6;
    border-radius: 1px;
}

/* Form Enhancements */
.instafeed-form-group {
    position: relative;
}

.instafeed-form-group input:focus + label,
.instafeed-form-group textarea:focus + label {
    color: #0095f6;
}

/* Success States */
.instafeed-success-message {
    background: rgba(46, 204, 113, 0.1);
    border: 1px solid rgba(46, 204, 113, 0.3);
    color: #2ecc71;
    padding: 10px 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-size: 14px;
}

/* Error States */
.instafeed-error-message {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    color: #e74c3c;
    padding: 10px 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-size: 14px;
}

/* Profile Badges */
.instafeed-profile-badges {
    display: flex;
    gap: 8px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.instafeed-badge {
    background: linear-gradient(45deg, #0095f6, #1877f2);
    color: #ffffff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.instafeed-badge.verified {
    background: linear-gradient(45deg, #1da1f2, #0095f6);
}

.instafeed-badge.premium {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #000000;
}

/* Tooltip Enhancements */
.instafeed-tooltip {
    position: relative;
}

.instafeed-tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    margin-bottom: 5px;
}

.instafeed-tooltip:hover::before {
    opacity: 1;
    visibility: visible;
}
