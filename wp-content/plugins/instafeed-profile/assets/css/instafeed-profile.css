/* InstaFeed Profile Plugin Styles */

/* Profile Modal Specific Styles */
.instafeed-profile-container {
    color: #ffffff;
}

/* Profile Header */
.instafeed-profile-header {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #262626;
}

.instafeed-profile-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.instafeed-profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #262626;
    transition: border-color 0.3s ease;
}

.instafeed-profile-avatar:hover {
    border-color: #0095f6;
}

.instafeed-profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.instafeed-change-avatar-btn {
    background: none;
    border: none;
    color: #0095f6;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: color 0.3s ease;
}

.instafeed-change-avatar-btn:hover {
    color: #1877f2;
}

/* Profile Info */
.instafeed-profile-info {
    flex: 1;
}

.instafeed-profile-username {
    margin-bottom: 20px;
}

.instafeed-profile-username h3 {
    margin: 0 0 5px;
    font-size: 24px;
    font-weight: 300;
    color: #ffffff;
}

.instafeed-username {
    color: #8e8e8e;
    font-size: 16px;
}

/* Profile Stats */
.instafeed-profile-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 20px;
}

.instafeed-stat {
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.instafeed-stat:hover {
    transform: translateY(-2px);
}

.instafeed-stat-number {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.instafeed-stat-label {
    display: block;
    font-size: 14px;
    color: #8e8e8e;
    margin-top: 2px;
}

/* Profile Bio */
.instafeed-profile-bio {
    margin-bottom: 15px;
}

.instafeed-profile-bio p {
    margin: 0;
    line-height: 1.5;
    color: #ffffff;
}

/* Profile Details */
.instafeed-profile-details {
    margin-bottom: 20px;
}

.instafeed-profile-detail {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: #8e8e8e;
    font-size: 14px;
}

.instafeed-detail-icon {
    font-size: 16px;
}

.instafeed-profile-detail a {
    color: #0095f6;
    text-decoration: none;
}

.instafeed-profile-detail a:hover {
    text-decoration: underline;
}

/* Social Links */
.instafeed-social-links {
    display: flex;
    gap: 15px;
}

.instafeed-social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #262626;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.instafeed-social-link:hover {
    background: #0095f6;
    transform: translateY(-2px);
}

.instafeed-social-link.instagram:hover { background: #E4405F; }
.instafeed-social-link.twitter:hover { background: #1DA1F2; }
.instafeed-social-link.facebook:hover { background: #4267B2; }

/* Profile Tabs */
.instafeed-profile-tabs {
    display: flex;
    border-bottom: 1px solid #262626;
    margin-bottom: 20px;
}

.instafeed-tab-btn {
    background: none;
    border: none;
    padding: 15px 20px;
    color: #8e8e8e;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.instafeed-tab-btn:hover,
.instafeed-tab-btn.active {
    color: #ffffff;
    border-bottom-color: #0095f6;
}

/* Tab Content */
.instafeed-tab-content {
    display: none;
}

.instafeed-tab-content.active {
    display: block;
}

/* Profile Posts Grid */
.instafeed-profile-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.instafeed-profile-post-item {
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
    position: relative;
}

.instafeed-profile-post-item:hover {
    transform: scale(1.05);
}

.instafeed-profile-post-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.instafeed-profile-post-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: #ffffff;
    font-weight: 600;
}

.instafeed-profile-post-item:hover .instafeed-profile-post-overlay {
    opacity: 1;
}

/* Edit Profile Modal Styles */
.instafeed-avatar-upload-section {
    text-align: center;
    margin-bottom: 30px;
}

.instafeed-avatar-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.instafeed-current-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #262626;
}

.instafeed-current-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.instafeed-avatar-actions {
    display: flex;
    gap: 10px;
}

/* Form Sections */
.instafeed-form-section {
    margin: 30px 0;
    padding: 20px 0;
    border-top: 1px solid #262626;
}

.instafeed-form-section h4 {
    color: #ffffff;
    margin: 0 0 20px;
    font-size: 16px;
    font-weight: 600;
}

/* Checkbox Groups */
.instafeed-checkbox-group {
    margin-bottom: 15px;
}

.instafeed-checkbox-group label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: normal;
}

.instafeed-checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Profile Actions */
.instafeed-profile-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Activity Styles */
.instafeed-profile-activity {
    max-height: 400px;
    overflow-y: auto;
}

.instafeed-activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #262626;
}

.instafeed-activity-item:last-child {
    border-bottom: none;
}

.instafeed-activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #262626;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.instafeed-activity-content {
    flex: 1;
}

.instafeed-activity-description {
    color: #ffffff;
    margin-bottom: 5px;
}

.instafeed-activity-time {
    color: #8e8e8e;
    font-size: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .instafeed-profile-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 20px;
    }
    
    .instafeed-profile-stats {
        justify-content: center;
        gap: 30px;
    }
    
    .instafeed-profile-posts-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .instafeed-avatar-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .instafeed-avatar-actions .instafeed-btn {
        width: 100%;
    }
    
    .instafeed-profile-tabs {
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .instafeed-tab-btn {
        flex-shrink: 0;
        min-width: 80px;
    }
}

@media (max-width: 480px) {
    .instafeed-profile-avatar {
        width: 80px;
        height: 80px;
    }
    
    .instafeed-current-avatar {
        width: 80px;
        height: 80px;
    }
    
    .instafeed-profile-username h3 {
        font-size: 20px;
    }
    
    .instafeed-profile-stats {
        gap: 20px;
    }
    
    .instafeed-stat-number {
        font-size: 16px;
    }
    
    .instafeed-stat-label {
        font-size: 12px;
    }
}

/* Loading States */
.instafeed-profile-loading {
    text-align: center;
    padding: 40px;
    color: #8e8e8e;
}

.instafeed-profile-loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #262626;
    border-radius: 50%;
    border-top-color: #0095f6;
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty States */
.instafeed-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #8e8e8e;
}

.instafeed-empty-state-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.instafeed-empty-state h3 {
    color: #ffffff;
    margin-bottom: 10px;
}

.instafeed-empty-state p {
    margin: 0;
    line-height: 1.5;
}
